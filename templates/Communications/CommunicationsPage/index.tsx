"use client";

import { useState } from "react";
import Layout from "@/components/Layout";
import Card from "@/components/Card";
import Button from "@/components/Button";
import Tabs from "@/components/Tabs";
import Icon from "@/components/Icon";

const messageTypes = [
    { id: 1, name: "All Messages" },
    { id: 2, name: "Em<PERSON>" },
    { id: 3, name: "SMS" },
    { id: 4, name: "Announcements" },
];

const messages = [
    {
        id: 1,
        type: "email",
        subject: "Welcome to Our Church Family",
        content: "Thank you for visiting us last Sunday. We're excited to have you as part of our church family...",
        recipients: 12,
        sent: true,
        sentAt: "2024-01-21T10:30:00Z",
        openRate: 85,
        clickRate: 12,
        template: "Welcome Email",
    },
    {
        id: 2,
        type: "sms",
        subject: "Service Reminder",
        content: "Don't forget about tonight's prayer meeting at 7 PM. See you there!",
        recipients: 45,
        sent: true,
        sentAt: "2024-01-21T15:00:00Z",
        deliveryRate: 98,
        template: null,
    },
    {
        id: 3,
        type: "announcement",
        subject: "Upcoming Community Outreach",
        content: "Join us this Saturday for our community outreach event. We'll be serving meals and sharing God's love...",
        recipients: 234,
        sent: false,
        scheduledFor: "2024-01-22T08:00:00Z",
        template: "Event Announcement",
    },
    {
        id: 4,
        type: "email",
        subject: "Monthly Newsletter",
        content: "Here's what's happening this month at our church...",
        recipients: 189,
        sent: true,
        sentAt: "2024-01-20T09:00:00Z",
        openRate: 72,
        clickRate: 8,
        template: "Newsletter",
    },
];

const CommunicationsPage = () => {
    const [activeType, setActiveType] = useState(messageTypes[0]);

    const filteredMessages = messages.filter((message) => {
        if (activeType.name === "All Messages") return true;
        if (activeType.name === "Emails") return message.type === "email";
        if (activeType.name === "SMS") return message.type === "sms";
        if (activeType.name === "Announcements") return message.type === "announcement";
        return true;
    });

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'email':
                return 'mail';
            case 'sms':
                return 'chat-think';
            case 'announcement':
                return 'bell';
            default:
                return 'chat-think';
        }
    };

    const getTypeColor = (type: string) => {
        switch (type) {
            case 'email':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'sms':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'announcement':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    return (
        <Layout title="Communications">
            <div className="flex items-center justify-between mb-8 max-lg:block">
                <div className="max-lg:mb-6">
                    <h1 className="text-h3 text-t-primary mb-2">Communications</h1>
                    <p className="text-body-2 text-t-secondary">
                        Send messages, announcements, and manage communication templates
                    </p>
                </div>
                <div className="flex items-center gap-3">
                    <Button isWhite href="/communications/templates">
                        Templates
                    </Button>
                    <Button href="/communications/new">
                        Compose Message
                    </Button>
                </div>
            </div>

            <div className="flex max-lg:block">
                <div className="col-left">
                    <Card title="Communication Overview" className="mb-6">
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 p-5 max-lg:p-3">
                            <div className="text-center">
                                <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-blue-100 dark:bg-blue-900 mx-auto mb-3">
                                    <Icon className="w-6 h-6 fill-blue-600 dark:fill-blue-400" name="mail" />
                                </div>
                                <div className="text-h4 text-t-primary font-semibold mb-1">156</div>
                                <div className="text-caption text-t-secondary">Emails Sent</div>
                            </div>
                            <div className="text-center">
                                <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-green-100 dark:bg-green-900 mx-auto mb-3">
                                    <Icon className="w-6 h-6 fill-green-600 dark:fill-green-400" name="chat-think" />
                                </div>
                                <div className="text-h4 text-t-primary font-semibold mb-1">89</div>
                                <div className="text-caption text-t-secondary">SMS Sent</div>
                            </div>
                            <div className="text-center">
                                <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-purple-100 dark:bg-purple-900 mx-auto mb-3">
                                    <Icon className="w-6 h-6 fill-purple-600 dark:fill-purple-400" name="bell" />
                                </div>
                                <div className="text-h4 text-t-primary font-semibold mb-1">23</div>
                                <div className="text-caption text-t-secondary">Announcements</div>
                            </div>
                            <div className="text-center">
                                <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-orange-100 dark:bg-orange-900 mx-auto mb-3">
                                    <Icon className="w-6 h-6 fill-orange-600 dark:fill-orange-400" name="chart-line" />
                                </div>
                                <div className="text-h4 text-t-primary font-semibold mb-1">78%</div>
                                <div className="text-caption text-t-secondary">Open Rate</div>
                            </div>
                        </div>
                    </Card>

                    <Card title="Recent Messages" className="mb-6">
                        <div className="p-5">
                            <div className="flex items-center justify-between mb-4">
                                <Tabs
                                    items={messageTypes}
                                    value={activeType}
                                    setValue={setActiveType}
                                />
                            </div>
                            
                            <div className="space-y-4">
                                {filteredMessages.map((message) => (
                                    <div 
                                        key={message.id}
                                        className="p-4 bg-b-surface2 rounded-xl border border-transparent hover:border-s-stroke2 transition-all"
                                    >
                                        <div className="flex items-start justify-between mb-3">
                                            <div className="flex items-start">
                                                <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-b-surface1 mr-4 mt-1">
                                                    <Icon 
                                                        className="w-5 h-5 fill-t-secondary" 
                                                        name={getTypeIcon(message.type)} 
                                                    />
                                                </div>
                                                <div className="flex-1">
                                                    <div className="flex items-center mb-2">
                                                        <h3 className="text-body-2 text-t-primary font-medium mr-3">
                                                            {message.subject}
                                                        </h3>
                                                        <div className={`inline-flex px-2 py-1 rounded-full text-caption font-medium ${getTypeColor(message.type)}`}>
                                                            {message.type.toUpperCase()}
                                                        </div>
                                                        {!message.sent && (
                                                            <div className="inline-flex px-2 py-1 rounded-full text-caption font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 ml-2">
                                                                Scheduled
                                                            </div>
                                                        )}
                                                    </div>
                                                    <p className="text-caption text-t-secondary mb-2 line-clamp-2">
                                                        {message.content}
                                                    </p>
                                                    <div className="flex items-center text-caption text-t-secondary">
                                                        <Icon className="w-4 h-4 mr-1 fill-current" name="profile" />
                                                        <span>{message.recipients} recipient{message.recipients !== 1 ? 's' : ''}</span>
                                                        <span className="mx-2">•</span>
                                                        <span>
                                                            {message.sent 
                                                                ? `Sent ${formatDate(message.sentAt)}` 
                                                                : `Scheduled for ${formatDate(message.scheduledFor)}`
                                                            }
                                                        </span>
                                                        {message.template && (
                                                            <>
                                                                <span className="mx-2">•</span>
                                                                <span>{message.template}</span>
                                                            </>
                                                        )}
                                                    </div>
                                                    {message.sent && message.type === 'email' && (
                                                        <div className="flex items-center text-caption text-t-secondary mt-2">
                                                            <span className="text-green-600">{message.openRate}% opened</span>
                                                            <span className="mx-2">•</span>
                                                            <span className="text-blue-600">{message.clickRate}% clicked</span>
                                                        </div>
                                                    )}
                                                    {message.sent && message.type === 'sms' && (
                                                        <div className="flex items-center text-caption text-t-secondary mt-2">
                                                            <span className="text-green-600">{message.deliveryRate}% delivered</span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 ml-4">
                                                <Button
                                                    isWhite
                                                    isCircle
                                                    icon="chart-line"
                                                    title="View Analytics"
                                                />
                                                <Button 
                                                    isWhite 
                                                    isCircle 
                                                    icon="edit"
                                                    title="Edit"
                                                />
                                                <Button 
                                                    isWhite 
                                                    isCircle 
                                                    icon="dots"
                                                    title="More Options"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>
                </div>

                <div className="col-right">
                    <Card title="Quick Compose" className="mb-6">
                        <div className="p-5">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-body-2 text-t-primary mb-2">
                                        Message Type
                                    </label>
                                    <select
                                        className="w-full h-12 px-4 border border-s-stroke2 rounded-xl text-body-2 text-t-primary outline-none focus:border-s-highlight"
                                        title="Select Message Type"
                                    >
                                        <option value="email">Email</option>
                                        <option value="sms">SMS</option>
                                        <option value="announcement">Announcement</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-body-2 text-t-primary mb-2">
                                        Recipients
                                    </label>
                                    <select
                                        className="w-full h-12 px-4 border border-s-stroke2 rounded-xl text-body-2 text-t-primary outline-none focus:border-s-highlight"
                                        title="Select Recipients"
                                    >
                                        <option value="">Select a list...</option>
                                        <option value="all-members">All Members</option>
                                        <option value="visitors">Recent Visitors</option>
                                        <option value="volunteers">Volunteers</option>
                                        <option value="youth">Youth Group</option>
                                    </select>
                                </div>
                                <Button className="w-full" href="/communications/new">
                                    Compose Message
                                </Button>
                            </div>
                        </div>
                    </Card>

                    <Card title="Templates" className="mb-6">
                        <div className="p-5">
                            <div className="space-y-3">
                                {[
                                    "Welcome Email",
                                    "Service Reminder",
                                    "Event Announcement",
                                    "Newsletter",
                                    "Thank You Note"
                                ].map((template, index) => (
                                    <div 
                                        key={index}
                                        className="flex items-center justify-between p-3 bg-b-surface2 rounded-lg hover:bg-b-surface1 transition-colors cursor-pointer"
                                    >
                                        <span className="text-body-2 text-t-primary">{template}</span>
                                        <Button 
                                            isWhite 
                                            isCircle 
                                            icon="edit"
                                            href={`/communications/templates/${index + 1}`}
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>

                    <Card title="Quick Actions">
                        <div className="p-5 space-y-3">
                            <Button 
                                className="w-full justify-start" 
                                icon="plus" 
                                isWhite
                                href="/communications/new"
                            >
                                Compose Message
                            </Button>
                            <Button
                                className="w-full justify-start"
                                icon="document"
                                isWhite
                                href="/communications/templates"
                            >
                                Manage Templates
                            </Button>
                            <Button
                                className="w-full justify-start"
                                icon="chart-line"
                                isWhite
                                href="/communications/analytics"
                            >
                                View Analytics
                            </Button>
                            <Button
                                className="w-full justify-start"
                                icon="gear"
                                isWhite
                                href="/communications/settings"
                            >
                                Communication Settings
                            </Button>
                        </div>
                    </Card>
                </div>
            </div>
        </Layout>
    );
};

export default CommunicationsPage;
