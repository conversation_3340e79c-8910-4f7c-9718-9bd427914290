import { Person } from "@/types/person";
import Table from "@/components/Table";
import TableRow from "@/components/TableRow";
import Checkbox from "@/components/Checkbox";
import Image from "@/components/Image";
import Icon from "@/components/Icon";

type ListProps = {
    people: Person[];
    selectedItems: Person[];
    selectAll: boolean;
    onSelectAll: (value: boolean) => void;
    onSelectItem: (item: Person) => void;
};

const List = ({ people, selectedItems, selectAll, onSelectAll, onSelectItem }: ListProps) => {
    const isSelected = (person: Person) => 
        selectedItems.some(item => item.id === person.id);

    const getMembershipStatusColor = (status: string) => {
        switch (status) {
            case 'member':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'visitor':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'inactive':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    };

    const formatLastAttendance = (dateString?: string) => {
        if (!dateString) return 'Never';
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - date.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
        return `${Math.ceil(diffDays / 30)} months ago`;
    };

    const formatCurrency = (amount?: number) => {
        if (!amount) return '$0';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    };

    return (
        <Table
            selectAll={selectAll}
            onSelectAll={onSelectAll}
            cellsThead={
                <>
                    <th>Person</th>
                    <th>Contact</th>
                    <th>Status</th>
                    <th>Groups</th>
                    <th>Teams</th>
                    <th>Last Attendance</th>
                    <th>Giving</th>
                    <th className="max-md:hidden">Actions</th>
                </>
            }
        >
            {people.map((person) => (
                <TableRow
                    key={person.id}
                    isSelected={isSelected(person)}
                    onSelect={() => onSelectItem(person)}
                >
                    <td>
                        <div className="flex items-center">
                            <div className="relative mr-4">
                                <Image
                                    className="w-10 h-10 rounded-full object-cover"
                                    src={person.avatar}
                                    width={40}
                                    height={40}
                                    alt={`${person.firstName} ${person.lastName}`}
                                />
                                {person.lastAttendance && new Date(person.lastAttendance) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) && (
                                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border border-white dark:border-gray-800"></div>
                                )}
                            </div>
                            <div>
                                <div className="text-body-2 text-t-primary font-medium">
                                    {person.firstName} {person.lastName}
                                </div>
                                {person.preferredName && (
                                    <div className="text-caption text-t-secondary">
                                        "{person.preferredName}"
                                    </div>
                                )}
                            </div>
                        </div>
                    </td>
                    <td>
                        <div className="space-y-1">
                            <div className="text-body-2 text-t-primary">{person.email}</div>
                            {person.phone && (
                                <div className="text-caption text-t-secondary">{person.phone}</div>
                            )}
                        </div>
                    </td>
                    <td>
                        <div className={`inline-flex px-2 py-1 rounded-full text-caption font-medium ${getMembershipStatusColor(person.membershipStatus)}`}>
                            {person.membershipStatus.charAt(0).toUpperCase() + person.membershipStatus.slice(1)}
                        </div>
                    </td>
                    <td>
                        <div className="text-body-2 text-t-primary">
                            {person.groups.length > 0 ? (
                                <span>{person.groups.length} group{person.groups.length !== 1 ? 's' : ''}</span>
                            ) : (
                                <span className="text-t-secondary">None</span>
                            )}
                        </div>
                    </td>
                    <td>
                        <div className="text-body-2 text-t-primary">
                            {person.teams.length > 0 ? (
                                <span>{person.teams.length} team{person.teams.length !== 1 ? 's' : ''}</span>
                            ) : (
                                <span className="text-t-secondary">None</span>
                            )}
                        </div>
                    </td>
                    <td>
                        <div className="text-body-2 text-t-primary">
                            {formatLastAttendance(person.lastAttendance)}
                        </div>
                    </td>
                    <td>
                        <div className="text-body-2 text-t-primary">
                            {formatCurrency(person.totalDonations)}
                        </div>
                    </td>
                    <td className="max-md:hidden">
                        <div className="flex items-center gap-2">
                            <button
                                type="button"
                                className="p-2 text-t-secondary hover:text-t-primary transition-colors"
                                title="Send Message"
                            >
                                <Icon className="w-4 h-4 fill-current" name="chat-think" />
                            </button>
                            <button
                                type="button"
                                className="p-2 text-t-secondary hover:text-t-primary transition-colors"
                                title="Edit Person"
                            >
                                <Icon className="w-4 h-4 fill-current" name="edit" />
                            </button>
                            <button
                                type="button"
                                className="p-2 text-t-secondary hover:text-t-primary transition-colors"
                                title="More Options"
                            >
                                <Icon className="w-4 h-4 fill-current" name="dots" />
                            </button>
                        </div>
                    </td>
                </TableRow>
            ))}
        </Table>
    );
};

export default List;
