import { Person } from "@/types/person";
import Checkbox from "@/components/Checkbox";
import Image from "@/components/Image";
import Icon from "@/components/Icon";

type GridProps = {
    people: Person[];
    selectedItems: Person[];
    selectAll: boolean;
    onSelectAll: (value: boolean) => void;
    onSelectItem: (item: Person) => void;
};

const Grid = ({ people, selectedItems, selectAll, onSelectAll, onSelectItem }: GridProps) => {
    const isSelected = (person: Person) => 
        selectedItems.some(item => item.id === person.id);

    const getMembershipStatusColor = (status: string) => {
        switch (status) {
            case 'member':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'visitor':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'inactive':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    };

    const formatLastAttendance = (dateString?: string) => {
        if (!dateString) return 'Never';
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - date.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
        return `${Math.ceil(diffDays / 30)} months ago`;
    };

    return (
        <div className="p-5 max-lg:p-3">
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                    <Checkbox
                        checked={selectAll}
                        onChange={onSelectAll}
                    />
                    <span className="ml-3 text-body-2 text-t-secondary">
                        {people.length} people
                    </span>
                </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {people.map((person) => (
                    <div
                        key={person.id}
                        className={`relative p-6 bg-b-surface2 rounded-2xl border transition-all hover:shadow-lg ${
                            isSelected(person) 
                                ? 'border-s-highlight shadow-lg' 
                                : 'border-transparent'
                        }`}
                    >
                        <div className="absolute top-4 right-4">
                            <Checkbox
                                checked={isSelected(person)}
                                onChange={() => onSelectItem(person)}
                            />
                        </div>
                        
                        <div className="flex flex-col items-center text-center">
                            <div className="relative mb-4">
                                <Image
                                    className="w-16 h-16 rounded-full object-cover"
                                    src={person.avatar}
                                    width={64}
                                    height={64}
                                    alt={`${person.firstName} ${person.lastName}`}
                                />
                                {person.lastAttendance && new Date(person.lastAttendance) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) && (
                                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                                )}
                            </div>
                            
                            <h3 className="text-h6 text-t-primary mb-1">
                                {person.firstName} {person.lastName}
                            </h3>
                            
                            {person.preferredName && (
                                <p className="text-caption text-t-secondary mb-2">
                                    "{person.preferredName}"
                                </p>
                            )}
                            
                            <div className={`inline-flex px-2 py-1 rounded-full text-caption font-medium mb-3 ${getMembershipStatusColor(person.membershipStatus)}`}>
                                {person.membershipStatus.charAt(0).toUpperCase() + person.membershipStatus.slice(1)}
                            </div>
                            
                            <div className="w-full space-y-2 text-caption text-t-secondary">
                                <div className="flex items-center justify-center">
                                    <Icon className="w-4 h-4 mr-2 fill-t-tertiary" name="mail" />
                                    <span className="truncate">{person.email}</span>
                                </div>

                                {person.phone && (
                                    <div className="flex items-center justify-center">
                                        <Icon className="w-4 h-4 mr-2 fill-t-tertiary" name="mobile" />
                                        <span>{person.phone}</span>
                                    </div>
                                )}

                                <div className="flex items-center justify-center">
                                    <Icon className="w-4 h-4 mr-2 fill-t-tertiary" name="calendar" />
                                    <span>Last: {formatLastAttendance(person.lastAttendance)}</span>
                                </div>

                                {person.groups.length > 0 && (
                                    <div className="flex items-center justify-center">
                                        <Icon className="w-4 h-4 mr-2 fill-t-tertiary" name="cube" />
                                        <span>{person.groups.length} group{person.groups.length !== 1 ? 's' : ''}</span>
                                    </div>
                                )}

                                {person.teams.length > 0 && (
                                    <div className="flex items-center justify-center">
                                        <Icon className="w-4 h-4 mr-2 fill-t-tertiary" name="star-fill" />
                                        <span>{person.teams.length} team{person.teams.length !== 1 ? 's' : ''}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                        
                        <div className="mt-4 pt-4 border-t border-s-stroke2">
                            <div className="flex justify-between items-center">
                                <button
                                    type="button"
                                    className="flex items-center text-caption text-t-secondary hover:text-t-primary transition-colors"
                                    title="Send Message"
                                >
                                    <Icon className="w-4 h-4 mr-1 fill-current" name="chat-think" />
                                    Message
                                </button>
                                <button
                                    type="button"
                                    className="flex items-center text-caption text-t-secondary hover:text-t-primary transition-colors"
                                    title="Edit Person"
                                >
                                    <Icon className="w-4 h-4 mr-1 fill-current" name="edit" />
                                    Edit
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Grid;
