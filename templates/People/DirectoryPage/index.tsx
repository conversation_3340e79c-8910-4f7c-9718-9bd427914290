"use client";

import { useState } from "react";
import Layout from "@/components/Layout";
import Search from "@/components/Search";
import Tabs from "@/components/Tabs";
import Button from "@/components/Button";
import DeleteItems from "@/components/DeleteItems";
import NoFound from "@/components/NoFound";
import Filters from "@/components/Filters";
import List from "./List";
import Grid from "./Grid";
import { Person } from "@/types/person";

import { people } from "@/mocks/people";

const views = [
    { id: 1, name: "grid" },
    { id: 2, name: "list" },
];

const categories = [
    { id: 1, name: "All People" },
    { id: 2, name: "Members" },
    { id: 3, name: "Visitors" },
    { id: 4, name: "Inactive" },
];

const DirectoryPage = () => {
    const [activeView, setActiveView] = useState(views[0]);
    const [activeCategory, setActiveCategory] = useState(categories[0]);
    const [search, setSearch] = useState("");

    const [selectedItems, setSelectedItems] = useState<Person[]>([]);
    const [selectAll, setSelectAll] = useState(false);

    const handleSelectAll = (checked: boolean) => {
        setSelectAll(checked);
        if (checked) {
            setSelectedItems(filteredPeople);
        } else {
            setSelectedItems([]);
        }
    };

    const handleSelectItem = (person: Person) => {
        setSelectedItems(prev => {
            const isSelected = prev.some(item => item.id === person.id);
            if (isSelected) {
                return prev.filter(item => item.id !== person.id);
            } else {
                return [...prev, person];
            }
        });
    };

    const clearSelection = () => {
        setSelectedItems([]);
        setSelectAll(false);
    };

    const filteredPeople = (people || []).filter((person) => {
        const matchesSearch =
            person.firstName.toLowerCase().includes(search.toLowerCase()) ||
            person.lastName.toLowerCase().includes(search.toLowerCase()) ||
            person.email.toLowerCase().includes(search.toLowerCase());

        const matchesCategory =
            activeCategory.name === "All People" ||
            (activeCategory.name === "Members" && person.membershipStatus === "member") ||
            (activeCategory.name === "Visitors" && person.membershipStatus === "visitor") ||
            (activeCategory.name === "Inactive" && person.membershipStatus === "inactive");

        return matchesSearch && matchesCategory;
    });

    return (
        <Layout title="People Directory">
            <div className="flex items-center justify-between mb-8 max-lg:block">
                <div className="flex items-center max-lg:mb-6">
                    <Tabs
                        className="mr-6 max-lg:mr-0 max-lg:mb-4"
                        items={categories}
                        value={activeCategory}
                        setValue={setActiveCategory}
                    />
                </div>
                <div className="flex items-center max-lg:justify-between">
                    <div className="flex items-center mr-6 max-lg:mr-0">
                        <Search
                            className="mr-3 max-lg:w-full"
                            placeholder="Search people..."
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                        />
                        <Filters />
                    </div>
                    <div className="flex items-center">
                        <Tabs
                            className="mr-6 max-lg:mr-3"
                            items={views}
                            value={activeView}
                            setValue={setActiveView}
                            isOnlyIcon
                        />
                        <Button
                            className="max-lg:hidden"
                            href="/people/new"
                            as="link"
                        >
                            Add Person
                        </Button>
                    </div>
                </div>
            </div>

            {selectedItems.length > 0 && (
                <div className="flex items-center justify-between mb-6 p-4 bg-b-surface2 rounded-2xl">
                    <div className="text-body-2 text-t-secondary">
                        {selectedItems.length} people selected
                    </div>
                    <div className="flex items-center gap-3">
                        <Button isWhite onClick={clearSelection}>
                            Clear
                        </Button>
                        <Button>Add to Group</Button>
                        <Button>Send Message</Button>
                        <DeleteItems
                            className="ml-3"
                            selectedItems={selectedItems}
                            onClearSelection={clearSelection}
                        />
                    </div>
                </div>
            )}

            <div className="card">
                {filteredPeople.length > 0 ? (
                    activeView.name === "grid" ? (
                        <Grid
                            people={filteredPeople}
                            selectedItems={selectedItems}
                            selectAll={selectAll}
                            onSelectAll={handleSelectAll}
                            onSelectItem={handleSelectItem}
                        />
                    ) : (
                        <List
                            people={filteredPeople}
                            selectedItems={selectedItems}
                            selectAll={selectAll}
                            onSelectAll={handleSelectAll}
                            onSelectItem={handleSelectItem}
                        />
                    )
                ) : (
                    <NoFound
                        title="No people found"
                        content="Try adjusting your search or filters to find what you're looking for."
                    />
                )}
            </div>

            <Button
                className="fixed right-6 bottom-6 lg:hidden"
                icon="plus"
                isCircle
                href="/people/new"
                as="link"
            />
        </Layout>
    );
};

export default DirectoryPage;
