"use client";

import { useState } from "react";
import Layout from "@/components/Layout";
import Card from "@/components/Card";
import Button from "@/components/Button";
import Search from "@/components/Search";
import Tabs from "@/components/Tabs";
import Icon from "@/components/Icon";
import Image from "@/components/Image";

const groupTypes = [
    { id: 1, name: "All Groups" },
    { id: 2, name: "Small Groups" },
    { id: 3, name: "Classes" },
    { id: 4, name: "Ministries" },
];

const groups = [
    {
        id: 1,
        name: "Young Adults Small Group",
        type: "Small Group",
        leader: "<PERSON>",
        leaderAvatar: "/images/avatars/1.png",
        members: 12,
        maxMembers: 15,
        meetingDay: "Wednesday",
        meetingTime: "7:00 PM",
        location: "Room 201",
        description: "A group for young adults (18-30) to grow in faith together",
        isOpen: true,
        lastMeeting: "2024-01-17",
        attendance: 10,
    },
    {
        id: 2,
        name: "Women's Bible Study",
        type: "Class",
        leader: "<PERSON>",
        leaderAvatar: "/images/avatars/2.png",
        members: 18,
        maxMembers: 20,
        meetingDay: "Tuesday",
        meetingTime: "10:00 AM",
        location: "Fellowship Hall",
        description: "Weekly Bible study for women of all ages",
        isOpen: true,
        lastMeeting: "2024-01-16",
        attendance: 16,
    },
    {
        id: 3,
        name: "Children's Ministry",
        type: "Ministry",
        leader: "Mike Johnson",
        leaderAvatar: "/images/avatars/3.png",
        members: 25,
        maxMembers: null,
        meetingDay: "Sunday",
        meetingTime: "10:00 AM",
        location: "Kids Zone",
        description: "Ministry focused on children ages 3-12",
        isOpen: false,
        lastMeeting: "2024-01-14",
        attendance: 22,
    },
    {
        id: 4,
        name: "Men's Fellowship",
        type: "Small Group",
        leader: "David Brown",
        leaderAvatar: "/images/avatars/4.png",
        members: 8,
        maxMembers: 12,
        meetingDay: "Saturday",
        meetingTime: "8:00 AM",
        location: "Coffee Shop",
        description: "Men's group for fellowship and accountability",
        isOpen: true,
        lastMeeting: "2024-01-13",
        attendance: 7,
    },
];

const GroupsOverviewPage = () => {
    const [activeType, setActiveType] = useState(groupTypes[0]);
    const [search, setSearch] = useState("");

    const filteredGroups = groups.filter((group) => {
        const matchesSearch = 
            group.name.toLowerCase().includes(search.toLowerCase()) ||
            group.leader.toLowerCase().includes(search.toLowerCase()) ||
            group.description.toLowerCase().includes(search.toLowerCase());
        
        const matchesType = 
            activeType.name === "All Groups" ||
            group.type === activeType.name.slice(0, -1); // Remove 's' from plural
        
        return matchesSearch && matchesType;
    });

    const getAttendanceColor = (attendance: number, members: number) => {
        const percentage = (attendance / members) * 100;
        if (percentage >= 80) return 'text-green-600';
        if (percentage >= 60) return 'text-yellow-600';
        return 'text-red-600';
    };

    return (
        <Layout title="Groups">
            <div className="flex items-center justify-between mb-8 max-lg:block">
                <div className="max-lg:mb-6">
                    <h1 className="text-h3 text-t-primary mb-2">Groups</h1>
                    <p className="text-body-2 text-t-secondary">
                        Manage small groups, classes, and ministries
                    </p>
                </div>
                <div className="flex items-center gap-3">
                    <Button isWhite href="/groups/reports">
                        View Reports
                    </Button>
                    <Button href="/groups/new">
                        Create Group
                    </Button>
                </div>
            </div>

            <div className="flex items-center justify-between mb-6 max-lg:block">
                <Tabs
                    className="max-lg:mb-4"
                    items={groupTypes}
                    value={activeType}
                    setValue={setActiveType}
                />
                <Search
                    className="max-lg:w-full"
                    placeholder="Search groups..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredGroups.map((group) => (
                    <Card key={group.id} title={group.name} className="h-fit">
                        <div className="p-5">
                            <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center">
                                    <Image
                                        className="w-10 h-10 rounded-full object-cover mr-3"
                                        src={group.leaderAvatar}
                                        width={40}
                                        height={40}
                                        alt={group.leader}
                                    />
                                    <div>
                                        <div className="text-body-2 text-t-primary font-medium">
                                            {group.leader}
                                        </div>
                                        <div className="text-caption text-t-secondary">
                                            Group Leader
                                        </div>
                                    </div>
                                </div>
                                <div className={`inline-flex px-2 py-1 rounded-full text-caption font-medium ${
                                    group.isOpen 
                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
                                }`}>
                                    {group.isOpen ? 'Open' : 'Closed'}
                                </div>
                            </div>

                            <p className="text-body-2 text-t-secondary mb-4">
                                {group.description}
                            </p>

                            <div className="space-y-3 mb-4">
                                <div className="flex items-center text-caption text-t-secondary">
                                    <Icon className="w-4 h-4 mr-2 fill-current" name="calendar" />
                                    <span>{group.meetingDay}s at {group.meetingTime}</span>
                                </div>
                                <div className="flex items-center text-caption text-t-secondary">
                                    <Icon className="w-4 h-4 mr-2 fill-current" name="map" />
                                    <span>{group.location}</span>
                                </div>
                                <div className="flex items-center text-caption text-t-secondary">
                                    <Icon className="w-4 h-4 mr-2 fill-current" name="profile" />
                                    <span>
                                        {group.members} member{group.members !== 1 ? 's' : ''}
                                        {group.maxMembers && ` of ${group.maxMembers}`}
                                    </span>
                                </div>
                            </div>

                            <div className="flex items-center justify-between pt-4 border-t border-s-stroke2">
                                <div className="text-caption text-t-secondary">
                                    Last meeting: 
                                    <span className={`ml-1 font-medium ${getAttendanceColor(group.attendance, group.members)}`}>
                                        {group.attendance}/{group.members} attended
                                    </span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button 
                                        isWhite 
                                        isCircle 
                                        icon="chat-think"
                                        title="Message Group"
                                    />
                                    <Button 
                                        isWhite 
                                        isCircle 
                                        icon="edit"
                                        href={`/groups/${group.id}/edit`}
                                        title="Edit Group"
                                    />
                                </div>
                            </div>
                        </div>
                    </Card>
                ))}
            </div>

            {filteredGroups.length === 0 && (
                <div className="text-center py-12">
                    <Icon className="w-16 h-16 mx-auto mb-4 fill-t-tertiary" name="grid" />
                    <h3 className="text-h6 text-t-primary mb-2">No groups found</h3>
                    <p className="text-body-2 text-t-secondary mb-6">
                        Try adjusting your search or create a new group.
                    </p>
                    <Button href="/groups/new">
                        Create Your First Group
                    </Button>
                </div>
            )}
        </Layout>
    );
};

export default GroupsOverviewPage;
