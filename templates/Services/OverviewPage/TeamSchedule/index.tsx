import Card from "@/components/Card";
import Button from "@/components/Button";
import Image from "@/components/Image";
import Icon from "@/components/Icon";

const teamSchedule = [
    {
        id: 1,
        date: "2024-01-21",
        service: "Sunday Morning",
        teams: [
            {
                name: "Worship Team",
                members: [
                    { id: 1, name: "<PERSON>", role: "Lead Vocals", avatar: "/images/avatars/1.png" },
                    { id: 2, name: "<PERSON>", role: "<PERSON>", avatar: "/images/avatars/2.png" },
                    { id: 3, name: "<PERSON>", role: "<PERSON>", avatar: "/images/avatars/3.png" },
                ]
            },
            {
                name: "Tech Team",
                members: [
                    { id: 4, name: "<PERSON>", role: "<PERSON>", avatar: "/images/avatars/4.png" },
                    { id: 5, name: "<PERSON>", role: "<PERSON>", avatar: "/images/avatars/5.png" },
                ]
            }
        ]
    },
    {
        id: 2,
        date: "2024-01-21",
        service: "Sunday Evening",
        teams: [
            {
                name: "Worship Team",
                members: [
                    { id: 6, name: "<PERSON>", role: "Lead Vocals", avatar: "/images/avatars/6.png" },
                    { id: 7, name: "<PERSON>", role: "Guitar", avatar: "/images/avatars/7.png" },
                ]
            }
        ]
    }
];

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
    });
};

const TeamSchedule = () => {
    return (
        <Card 
            title="Team Schedule" 
            className="mb-6"
            headContent={
                <Button 
                    className="ml-auto" 
                    isWhite 
                    href="/services/teams"
                >
                    Manage Teams
                </Button>
            }
        >
            <div className="p-5 max-lg:p-3">
                <div className="space-y-6">
                    {teamSchedule.map((schedule) => (
                        <div key={schedule.id} className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-body-1 text-t-primary font-medium">
                                        {schedule.service}
                                    </h3>
                                    <div className="flex items-center text-caption text-t-secondary">
                                        <Icon className="w-4 h-4 mr-2 fill-current" name="calendar" />
                                        <span>{formatDate(schedule.date)}</span>
                                    </div>
                                </div>
                                <Button 
                                    isWhite 
                                    isCircle 
                                    icon="edit"
                                    href={`/services/schedule/${schedule.id}`}
                                />
                            </div>
                            
                            <div className="space-y-3">
                                {schedule.teams.map((team, teamIndex) => (
                                    <div key={teamIndex} className="p-3 bg-b-surface2 rounded-lg">
                                        <div className="flex items-center justify-between mb-3">
                                            <h4 className="text-body-2 text-t-primary font-medium">
                                                {team.name}
                                            </h4>
                                            <div className="text-caption text-t-secondary">
                                                {team.members.length} member{team.members.length !== 1 ? 's' : ''}
                                            </div>
                                        </div>
                                        
                                        <div className="space-y-2">
                                            {team.members.map((member) => (
                                                <div key={member.id} className="flex items-center">
                                                    <Image
                                                        className="w-8 h-8 rounded-full object-cover mr-3"
                                                        src={member.avatar}
                                                        alt={member.name}
                                                    />
                                                    <div className="flex-1">
                                                        <div className="text-body-2 text-t-primary">
                                                            {member.name}
                                                        </div>
                                                        <div className="text-caption text-t-secondary">
                                                            {member.role}
                                                        </div>
                                                    </div>
                                                    <button className="p-1 text-t-secondary hover:text-t-primary transition-colors">
                                                        <Icon className="w-4 h-4 fill-current" name="chat-think" />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </Card>
    );
};

export default TeamSchedule;
