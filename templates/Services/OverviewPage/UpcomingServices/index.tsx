import Card from "@/components/Card";
import Button from "@/components/Button";
import Icon from "@/components/Icon";

const upcomingServices = [
    {
        id: 1,
        title: "Sunday Morning Worship",
        date: "2024-01-21",
        time: "10:00 AM",
        type: "Sunday Service",
        status: "published",
        volunteers: 12,
        openPositions: 2,
        songs: 5,
        series: "New Beginnings",
        theme: "Fresh Start",
    },
    {
        id: 2,
        title: "Sunday Evening Service",
        date: "2024-01-21",
        time: "6:00 PM",
        type: "Evening Service",
        status: "draft",
        volunteers: 8,
        openPositions: 3,
        songs: 4,
        series: "New Beginnings",
        theme: "Hope Renewed",
    },
    {
        id: 3,
        title: "Wednesday Night Prayer",
        date: "2024-01-24",
        time: "7:00 PM",
        type: "Prayer Meeting",
        status: "published",
        volunteers: 5,
        openPositions: 1,
        songs: 3,
        series: null,
        theme: "Prayer & Worship",
    },
    {
        id: 4,
        title: "Sunday Morning Worship",
        date: "2024-01-28",
        time: "10:00 AM",
        type: "Sunday Service",
        status: "planning",
        volunteers: 0,
        openPositions: 12,
        songs: 0,
        series: "New Beginnings",
        theme: "Moving Forward",
    },
];

const getStatusColor = (status: string) => {
    switch (status) {
        case 'published':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'draft':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'planning':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
};

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
    });
};

const UpcomingServices = () => {
    return (
        <Card 
            title="Upcoming Services" 
            className="mb-6"
            headContent={
                <Button 
                    className="ml-auto" 
                    isWhite 
                    href="/services/schedule"
                >
                    View All
                </Button>
            }
        >
            <div className="p-5 max-lg:p-3">
                <div className="space-y-4">
                    {upcomingServices.map((service) => (
                        <div 
                            key={service.id}
                            className="p-4 bg-b-surface2 rounded-xl border border-transparent hover:border-s-stroke2 transition-all"
                        >
                            <div className="flex items-start justify-between mb-3">
                                <div className="flex-1">
                                    <div className="flex items-center mb-2">
                                        <h3 className="text-body-1 text-t-primary font-medium mr-3">
                                            {service.title}
                                        </h3>
                                        <div className={`inline-flex px-2 py-1 rounded-full text-caption font-medium ${getStatusColor(service.status)}`}>
                                            {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                                        </div>
                                    </div>
                                    <div className="flex items-center text-caption text-t-secondary mb-2">
                                        <Icon className="w-4 h-4 mr-2 fill-current" name="calendar" />
                                        <span>{formatDate(service.date)} at {service.time}</span>
                                        <span className="mx-2">•</span>
                                        <span>{service.type}</span>
                                    </div>
                                    {service.series && (
                                        <div className="flex items-center text-caption text-t-secondary mb-2">
                                            <Icon className="w-4 h-4 mr-2 fill-current" name="folder" />
                                            <span>Series: {service.series}</span>
                                            <span className="mx-2">•</span>
                                            <span>Theme: {service.theme}</span>
                                        </div>
                                    )}
                                </div>
                                <Button 
                                    className="ml-4" 
                                    isWhite 
                                    isCircle 
                                    icon="edit"
                                    href={`/services/${service.id}/edit`}
                                />
                            </div>
                            
                            <div className="grid grid-cols-3 gap-4 pt-3 border-t border-s-stroke2">
                                <div className="text-center">
                                    <div className="text-body-2 text-t-primary font-medium">
                                        {service.volunteers}
                                    </div>
                                    <div className="text-caption text-t-secondary">
                                        Volunteers
                                    </div>
                                </div>
                                <div className="text-center">
                                    <div className={`text-body-2 font-medium ${service.openPositions > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                                        {service.openPositions}
                                    </div>
                                    <div className="text-caption text-t-secondary">
                                        Open Positions
                                    </div>
                                </div>
                                <div className="text-center">
                                    <div className="text-body-2 text-t-primary font-medium">
                                        {service.songs}
                                    </div>
                                    <div className="text-caption text-t-secondary">
                                        Songs
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </Card>
    );
};

export default UpcomingServices;
