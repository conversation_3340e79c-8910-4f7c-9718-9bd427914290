"use client";

import Layout from "@/components/Layout";
import Card from "@/components/Card";
import Button from "@/components/Button";
import Overview from "./Overview";
import UpcomingServices from "./UpcomingServices";
import TeamSchedule from "./TeamSchedule";
import SongLibrary from "./SongLibrary";
import VolunteerNeeds from "./VolunteerNeeds";

const ServicesOverviewPage = () => {
    return (
        <Layout title="Service Planning">
            <div className="flex items-center justify-between mb-8 max-lg:block">
                <div className="max-lg:mb-6">
                    <h1 className="text-h3 text-t-primary mb-2">Service Planning</h1>
                    <p className="text-body-2 text-t-secondary">
                        Plan worship services, schedule volunteers, and coordinate your teams
                    </p>
                </div>
                <div className="flex items-center gap-3">
                    <Button isWhite href="/services/songs">
                        Song Library
                    </Button>
                    <Button href="/services/new">
                        New Service Plan
                    </Button>
                </div>
            </div>

            <div className="flex max-lg:block">
                <div className="col-left">
                    <Overview />
                    <UpcomingServices />
                    <TeamSchedule />
                </div>
                <div className="col-right">
                    <VolunteerNeeds />
                    <SongLibrary />
                    <Card
                        title="Quick Actions"
                        className="mb-6"
                    >
                        <div className="p-5 space-y-3">
                            <Button 
                                className="w-full justify-start" 
                                icon="plus" 
                                isWhite
                                href="/services/new"
                            >
                                Create Service Plan
                            </Button>
                            <Button 
                                className="w-full justify-start" 
                                icon="profile" 
                                isWhite
                                href="/services/teams"
                            >
                                Manage Teams
                            </Button>
                            <Button 
                                className="w-full justify-start" 
                                icon="calendar" 
                                isWhite
                                href="/services/schedule"
                            >
                                View Schedule
                            </Button>
                            <Button 
                                className="w-full justify-start" 
                                icon="music" 
                                isWhite
                                href="/services/songs"
                            >
                                Browse Songs
                            </Button>
                        </div>
                    </Card>
                </div>
            </div>
        </Layout>
    );
};

export default ServicesOverviewPage;
