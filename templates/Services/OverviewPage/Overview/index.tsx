import Card from "@/components/Card";
import Icon from "@/components/Icon";
import Percentage from "@/components/Percentage";
import { LineChart, Line, ResponsiveContainer } from "recharts";

const overviewData = [
    {
        id: 1,
        title: "This Week's Services",
        icon: "calendar",
        counter: "4",
        percentage: 0,
        color: "#3B82F6",
        dataChart: [
            { name: "Week 1", amt: 3 },
            { name: "Week 2", amt: 4 },
            { name: "Week 3", amt: 3 },
            { name: "Week 4", amt: 4 },
            { name: "Week 5", amt: 4 },
            { name: "Week 6", amt: 4 },
        ],
    },
    {
        id: 2,
        title: "Volunteers Scheduled",
        icon: "profile",
        counter: "47",
        percentage: 12.5,
        color: "#10B981",
        dataChart: [
            { name: "Week 1", amt: 42 },
            { name: "Week 2", amt: 45 },
            { name: "Week 3", amt: 43 },
            { name: "Week 4", amt: 48 },
            { name: "Week 5", amt: 46 },
            { name: "Week 6", amt: 47 },
        ],
    },
    {
        id: 3,
        title: "Open Positions",
        icon: "star",
        counter: "8",
        percentage: -15.2,
        color: "#F59E0B",
        dataChart: [
            { name: "Week 1", amt: 12 },
            { name: "Week 2", amt: 10 },
            { name: "Week 3", amt: 11 },
            { name: "Week 4", amt: 9 },
            { name: "Week 5", amt: 10 },
            { name: "Week 6", amt: 8 },
        ],
    },
    {
        id: 4,
        title: "Songs in Rotation",
        icon: "music",
        counter: "156",
        percentage: 8.7,
        color: "#8B5CF6",
        dataChart: [
            { name: "Week 1", amt: 145 },
            { name: "Week 2", amt: 148 },
            { name: "Week 3", amt: 150 },
            { name: "Week 4", amt: 152 },
            { name: "Week 5", amt: 154 },
            { name: "Week 6", amt: 156 },
        ],
    },
];

const Overview = () => {
    return (
        <Card title="Service Overview" className="mb-6">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 p-5 max-lg:p-3">
                {overviewData.map((item) => (
                    <div key={item.id} className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-b-surface2 mr-3">
                                    <Icon 
                                        className="w-5 h-5 fill-t-secondary" 
                                        name={item.icon} 
                                    />
                                </div>
                                <div>
                                    <div className="text-h4 text-t-primary font-semibold">
                                        {item.counter}
                                    </div>
                                    <div className="text-caption text-t-secondary">
                                        {item.title}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                            <div className="flex-1 h-12">
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart data={item.dataChart}>
                                        <Line
                                            type="monotone"
                                            dataKey="amt"
                                            stroke={item.color}
                                            strokeWidth={2}
                                            dot={false}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                            {item.percentage !== 0 && (
                                <div className="ml-3">
                                    <Percentage value={item.percentage} />
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </Card>
    );
};

export default Overview;
