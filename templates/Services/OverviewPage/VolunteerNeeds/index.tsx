import Card from "@/components/Card";
import Button from "@/components/Button";
import Icon from "@/components/Icon";

const volunteerNeeds = [
    {
        id: 1,
        position: "Sound Technician",
        team: "Tech Team",
        service: "Sunday Morning",
        date: "2024-01-28",
        urgency: "high",
        description: "Need experienced sound tech for Sunday morning service",
        requirements: ["Audio experience", "Available Sundays"],
    },
    {
        id: 2,
        position: "Backup Vocalist",
        team: "Worship Team",
        service: "Sunday Evening",
        date: "2024-01-21",
        urgency: "medium",
        description: "Looking for backup vocalist for evening service",
        requirements: ["Vocal experience", "Team player"],
    },
    {
        id: 3,
        position: "Usher",
        team: "Hospitality Team",
        service: "Sunday Morning",
        date: "2024-01-21",
        urgency: "low",
        description: "Need friendly usher for greeting and seating",
        requirements: ["Friendly demeanor", "Punctual"],
    },
    {
        id: 4,
        position: "Camera Operator",
        team: "Tech Team",
        service: "Sunday Morning",
        date: "2024-02-04",
        urgency: "medium",
        description: "Camera operator needed for live streaming",
        requirements: ["Camera experience", "Tech savvy"],
    },
];

const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
        case 'high':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        case 'medium':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'low':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
};

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
    });
};

const VolunteerNeeds = () => {
    return (
        <Card 
            title="Volunteer Needs" 
            className="mb-6"
            headContent={
                <Button 
                    className="ml-auto" 
                    isWhite 
                    href="/services/volunteers"
                >
                    View All
                </Button>
            }
        >
            <div className="p-5 max-lg:p-3">
                <div className="space-y-4">
                    {volunteerNeeds.map((need) => (
                        <div 
                            key={need.id}
                            className="p-4 bg-b-surface2 rounded-xl border border-transparent hover:border-s-stroke2 transition-all"
                        >
                            <div className="flex items-start justify-between mb-3">
                                <div className="flex-1">
                                    <div className="flex items-center mb-2">
                                        <h3 className="text-body-2 text-t-primary font-medium mr-3">
                                            {need.position}
                                        </h3>
                                        <div className={`inline-flex px-2 py-1 rounded-full text-caption font-medium ${getUrgencyColor(need.urgency)}`}>
                                            {need.urgency.charAt(0).toUpperCase() + need.urgency.slice(1)}
                                        </div>
                                    </div>
                                    <div className="flex items-center text-caption text-t-secondary mb-2">
                                        <Icon className="w-4 h-4 mr-2 fill-current" name="profile" />
                                        <span>{need.team}</span>
                                        <span className="mx-2">•</span>
                                        <span>{need.service}</span>
                                        <span className="mx-2">•</span>
                                        <span>{formatDate(need.date)}</span>
                                    </div>
                                    <p className="text-caption text-t-secondary mb-3">
                                        {need.description}
                                    </p>
                                    <div className="flex flex-wrap gap-1">
                                        {need.requirements.map((req, index) => (
                                            <span 
                                                key={index}
                                                className="inline-flex px-2 py-1 bg-b-surface1 rounded-md text-caption text-t-secondary"
                                            >
                                                {req}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-2 pt-3 border-t border-s-stroke2">
                                <Button 
                                    className="flex-1" 
                                    href={`/services/volunteers/${need.id}/fill`}
                                >
                                    Fill Position
                                </Button>
                                <Button 
                                    isWhite 
                                    isCircle 
                                    icon="share"
                                />
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </Card>
    );
};

export default VolunteerNeeds;
