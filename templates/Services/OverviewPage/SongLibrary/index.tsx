import Card from "@/components/Card";
import Button from "@/components/Button";
import Icon from "@/components/Icon";

const recentSongs = [
    {
        id: 1,
        title: "Amazing Grace",
        artist: "Traditional",
        key: "G",
        tempo: 72,
        lastUsed: "2024-01-14",
        timesUsed: 24,
        themes: ["<PERSON>", "Salvation"],
    },
    {
        id: 2,
        title: "How Great Thou Art",
        artist: "<PERSON>",
        key: "Bb",
        tempo: 68,
        lastUsed: "2024-01-07",
        timesUsed: 18,
        themes: ["Worship", "Praise"],
    },
    {
        id: 3,
        title: "10,000 Reasons",
        artist: "<PERSON>",
        key: "C",
        tempo: 73,
        lastUsed: "2024-01-14",
        timesUsed: 32,
        themes: ["Praise", "Thanksgiving"],
    },
    {
        id: 4,
        title: "Way Maker",
        artist: "Sinach",
        key: "E",
        tempo: 132,
        lastUsed: "2024-01-07",
        timesUsed: 28,
        themes: ["Faith", "Miracles"],
    },
    {
        id: 5,
        title: "Great Are You Lord",
        artist: "All Sons & Daughters",
        key: "A",
        tempo: 76,
        lastUsed: "2023-12-31",
        timesUsed: 15,
        themes: ["Worship", "Adoration"],
    },
];

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return `${Math.ceil(diffDays / 30)} months ago`;
};

const SongLibrary = () => {
    return (
        <Card 
            title="Recent Songs" 
            className="mb-6"
            headContent={
                <Button 
                    className="ml-auto" 
                    isWhite 
                    href="/services/songs"
                >
                    Browse All
                </Button>
            }
        >
            <div className="p-5 max-lg:p-3">
                <div className="space-y-4">
                    {recentSongs.map((song) => (
                        <div 
                            key={song.id}
                            className="p-4 bg-b-surface2 rounded-xl border border-transparent hover:border-s-stroke2 transition-all cursor-pointer"
                        >
                            <div className="flex items-start justify-between mb-3">
                                <div className="flex-1">
                                    <h3 className="text-body-2 text-t-primary font-medium mb-1">
                                        {song.title}
                                    </h3>
                                    <div className="text-caption text-t-secondary mb-2">
                                        by {song.artist}
                                    </div>
                                    <div className="flex items-center text-caption text-t-secondary mb-2">
                                        <Icon className="w-4 h-4 mr-1 fill-current" name="music" />
                                        <span>Key: {song.key}</span>
                                        <span className="mx-2">•</span>
                                        <span>{song.tempo} BPM</span>
                                    </div>
                                    <div className="flex flex-wrap gap-1 mb-2">
                                        {song.themes.map((theme, index) => (
                                            <span 
                                                key={index}
                                                className="inline-flex px-2 py-1 bg-b-surface1 rounded-md text-caption text-t-secondary"
                                            >
                                                {theme}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                                <Button 
                                    isWhite 
                                    isCircle 
                                    icon="plus"
                                    title="Add to Service"
                                />
                            </div>
                            
                            <div className="flex items-center justify-between pt-3 border-t border-s-stroke2">
                                <div className="text-caption text-t-secondary">
                                    Used {song.timesUsed} times • Last: {formatDate(song.lastUsed)}
                                </div>
                                <div className="flex items-center gap-2">
                                    <button className="p-1 text-t-secondary hover:text-t-primary transition-colors">
                                        <Icon className="w-4 h-4 fill-current" name="play" />
                                    </button>
                                    <button className="p-1 text-t-secondary hover:text-t-primary transition-colors">
                                        <Icon className="w-4 h-4 fill-current" name="edit" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </Card>
    );
};

export default SongLibrary;
