"use client";

import Layout from "@/components/Layout";
import PopularProducts from "@/components/PopularProducts";
import RefundRequests from "@/components/RefundRequests";
import Overview from "./Overview";
import ProductView from "./ProductView";
import OverviewSlider from "./OverviewSlider";
import GetMoreCustomers from "./GetMoreCustomers";
import Comments from "./Comments";

import { popularProducts } from "@/mocks/products";

const HomePage = () => {
    return (
        <Layout title="Church Dashboard">
            <div className="mb-8">
                <h1 className="text-h3 text-t-primary mb-2">Welcome to ChurchCore</h1>
                <p className="text-body-2 text-t-secondary">
                    Your comprehensive church management dashboard
                </p>
            </div>
            <div className="flex max-lg:block">
                <div className="col-left">
                    <Overview />
                    <ProductView />
                    <OverviewSlider />
                    <GetMoreCustomers />
                </div>
                <div className="col-right">
                    <PopularProducts
                        title="Recent Activity"
                        items={popularProducts}
                    />
                    <Comments />
                    <RefundRequests />
                </div>
            </div>
        </Layout>
    );
};

export default HomePage;
