"use client";

import { useState } from "react";
import Layout from "@/components/Layout";
import Card from "@/components/Card";
import Button from "@/components/Button";
import Tabs from "@/components/Tabs";
import Icon from "@/components/Icon";
import Image from "@/components/Image";

const checkInEvents = [
    { id: 1, name: "Sunday Morning Service" },
    { id: 2, name: "Sunday Evening Service" },
    { id: 3, name: "Wednesday Prayer Meeting" },
    { id: 4, name: "Youth Group" },
];

const recentCheckIns = [
    {
        id: 1,
        person: "<PERSON>",
        avatar: "/images/avatars/1.png",
        event: "Sunday Morning Service",
        time: "09:45 AM",
        location: "Main Sanctuary",
        type: "adult",
    },
    {
        id: 2,
        person: "<PERSON>",
        avatar: "/images/avatars/2.png",
        event: "Sunday Morning Service",
        time: "09:47 AM",
        location: "Main Sanctuary",
        type: "adult",
    },
    {
        id: 3,
        person: "<PERSON>",
        avatar: "/images/avatars/3.png",
        event: "Children's Ministry",
        time: "09:50 AM",
        location: "Kids Zone",
        type: "child",
        parent: "<PERSON>",
        securityCode: "A123",
    },
    {
        id: 4,
        person: "<PERSON>",
        avatar: "/images/avatars/4.png",
        event: "Sunday Morning Service",
        time: "09:52 AM",
        location: "Main Sanctuary",
        type: "volunteer",
        team: "Ushers",
    },
];

const attendanceStats = [
    {
        id: 1,
        title: "Total Check-ins Today",
        icon: "check",
        counter: "247",
        color: "#10B981",
    },
    {
        id: 2,
        title: "Adults",
        icon: "profile",
        counter: "189",
        color: "#3B82F6",
    },
    {
        id: 3,
        title: "Children",
        icon: "star",
        counter: "58",
        color: "#F59E0B",
    },
    {
        id: 4,
        title: "First-time Visitors",
        icon: "heart",
        counter: "12",
        color: "#EF4444",
    },
];

const CheckInsPage = () => {
    const [activeEvent, setActiveEvent] = useState(checkInEvents[0]);

    const formatTime = (timeString: string) => {
        return timeString;
    };

    const getTypeColor = (type: string) => {
        switch (type) {
            case 'adult':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'child':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'volunteer':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    };

    return (
        <Layout title="Check-ins">
            <div className="flex items-center justify-between mb-8 max-lg:block">
                <div className="max-lg:mb-6">
                    <h1 className="text-h3 text-t-primary mb-2">Check-ins</h1>
                    <p className="text-body-2 text-t-secondary">
                        Manage attendance and check-in processes for services and events
                    </p>
                </div>
                <div className="flex items-center gap-3">
                    <Button isWhite href="/check-ins/reports">
                        View Reports
                    </Button>
                    <Button href="/check-ins/new-event">
                        New Check-in Event
                    </Button>
                </div>
            </div>

            <div className="flex max-lg:block">
                <div className="col-left">
                    <Card title="Attendance Overview" className="mb-6">
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 p-5 max-lg:p-3">
                            {attendanceStats.map((stat) => (
                                <div key={stat.id} className="text-center">
                                    <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-b-surface2 mx-auto mb-3">
                                        <Icon 
                                            className="w-6 h-6 fill-t-secondary" 
                                            name={stat.icon} 
                                        />
                                    </div>
                                    <div className="text-h4 text-t-primary font-semibold mb-1">
                                        {stat.counter}
                                    </div>
                                    <div className="text-caption text-t-secondary">
                                        {stat.title}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </Card>

                    <Card title="Recent Check-ins" className="mb-6">
                        <div className="p-5">
                            <div className="flex items-center justify-between mb-4">
                                <Tabs
                                    items={checkInEvents}
                                    value={activeEvent}
                                    setValue={setActiveEvent}
                                />
                                <Button isWhite icon="arrow" isCircle title="Refresh" />
                            </div>
                            
                            <div className="space-y-4">
                                {recentCheckIns
                                    .filter(checkIn => checkIn.event === activeEvent.name)
                                    .map((checkIn) => (
                                    <div 
                                        key={checkIn.id}
                                        className="flex items-center justify-between p-4 bg-b-surface2 rounded-xl"
                                    >
                                        <div className="flex items-center">
                                            <Image
                                                className="w-12 h-12 rounded-full object-cover mr-4"
                                                src={checkIn.avatar}
                                                width={48}
                                                height={48}
                                                alt={checkIn.person}
                                            />
                                            <div>
                                                <div className="flex items-center mb-1">
                                                    <span className="text-body-2 text-t-primary font-medium mr-3">
                                                        {checkIn.person}
                                                    </span>
                                                    <div className={`inline-flex px-2 py-1 rounded-full text-caption font-medium ${getTypeColor(checkIn.type)}`}>
                                                        {checkIn.type.charAt(0).toUpperCase() + checkIn.type.slice(1)}
                                                    </div>
                                                </div>
                                                <div className="flex items-center text-caption text-t-secondary">
                                                    <Icon className="w-4 h-4 mr-1 fill-current" name="clock" />
                                                    <span>{formatTime(checkIn.time)}</span>
                                                    <span className="mx-2">•</span>
                                                    <span>{checkIn.location}</span>
                                                    {checkIn.parent && (
                                                        <>
                                                            <span className="mx-2">•</span>
                                                            <span>Parent: {checkIn.parent}</span>
                                                        </>
                                                    )}
                                                    {checkIn.team && (
                                                        <>
                                                            <span className="mx-2">•</span>
                                                            <span>Team: {checkIn.team}</span>
                                                        </>
                                                    )}
                                                </div>
                                                {checkIn.securityCode && (
                                                    <div className="flex items-center text-caption text-orange-600 mt-1">
                                                        <Icon className="w-4 h-4 mr-1 fill-current" name="lock" />
                                                        <span>Security Code: {checkIn.securityCode}</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {checkIn.type === 'child' && (
                                                <Button 
                                                    isWhite 
                                                    isCircle 
                                                    icon="check"
                                                    title="Check Out"
                                                />
                                            )}
                                            <Button 
                                                isWhite 
                                                isCircle 
                                                icon="edit"
                                                title="Edit"
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>
                </div>

                <div className="col-right">
                    <Card title="Quick Check-in" className="mb-6">
                        <div className="p-5">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-body-2 text-t-primary mb-2">
                                        Search Person
                                    </label>
                                    <input
                                        type="text"
                                        placeholder="Type name or scan barcode..."
                                        className="w-full h-12 px-4 border border-s-stroke2 rounded-xl text-body-2 text-t-primary placeholder:text-t-secondary outline-none focus:border-s-highlight"
                                    />
                                </div>
                                <div>
                                    <label className="block text-body-2 text-t-primary mb-2">
                                        Event
                                    </label>
                                    <select className="w-full h-12 px-4 border border-s-stroke2 rounded-xl text-body-2 text-t-primary outline-none focus:border-s-highlight">
                                        {checkInEvents.map((event) => (
                                            <option key={event.id} value={event.id}>
                                                {event.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <Button className="w-full">
                                    Check In
                                </Button>
                            </div>
                        </div>
                    </Card>

                    <Card title="Active Events" className="mb-6">
                        <div className="p-5">
                            <div className="space-y-3">
                                {checkInEvents.map((event) => (
                                    <div 
                                        key={event.id}
                                        className="flex items-center justify-between p-3 bg-b-surface2 rounded-lg"
                                    >
                                        <div>
                                            <div className="text-body-2 text-t-primary font-medium">
                                                {event.name}
                                            </div>
                                            <div className="text-caption text-t-secondary">
                                                {Math.floor(Math.random() * 50) + 20} checked in
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Button 
                                                isWhite 
                                                isCircle 
                                                icon="chart"
                                                href={`/check-ins/events/${event.id}/stats`}
                                            />
                                            <Button 
                                                isWhite 
                                                isCircle 
                                                icon="settings"
                                                href={`/check-ins/events/${event.id}/settings`}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>

                    <Card title="Quick Actions">
                        <div className="p-5 space-y-3">
                            <Button 
                                className="w-full justify-start" 
                                icon="plus" 
                                isWhite
                                href="/check-ins/new-event"
                            >
                                Create Check-in Event
                            </Button>
                            <Button 
                                className="w-full justify-start" 
                                icon="profile" 
                                isWhite
                                href="/check-ins/volunteers"
                            >
                                Manage Volunteers
                            </Button>
                            <Button
                                className="w-full justify-start"
                                icon="chart-line"
                                isWhite
                                href="/check-ins/reports"
                            >
                                Attendance Reports
                            </Button>
                            <Button
                                className="w-full justify-start"
                                icon="gear"
                                isWhite
                                href="/check-ins/settings"
                            >
                                Check-in Settings
                            </Button>
                        </div>
                    </Card>
                </div>
            </div>
        </Layout>
    );
};

export default CheckInsPage;
