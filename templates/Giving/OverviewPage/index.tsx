"use client";

import Layout from "@/components/Layout";
import Card from "@/components/Card";
import Button from "@/components/Button";
import Icon from "@/components/Icon";
import Percentage from "@/components/Percentage";
import { LineChart, Line, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from "recharts";

const givingOverview = [
    {
        id: 1,
        title: "Total Giving (YTD)",
        icon: "income",
        counter: "$127,450",
        percentage: 12.5,
        color: "#10B981",
        dataChart: [
            { name: "<PERSON>", amt: 127450 },
            { name: "Feb", amt: 135200 },
            { name: "<PERSON>", amt: 142800 },
            { name: "Apr", amt: 138900 },
            { name: "May", amt: 145600 },
            { name: "<PERSON>", amt: 152300 },
        ],
    },
    {
        id: 2,
        title: "This Month",
        icon: "calendar",
        counter: "$18,750",
        percentage: 8.3,
        color: "#3B82F6",
        dataChart: [
            { name: "Week 1", amt: 4200 },
            { name: "Week 2", amt: 4800 },
            { name: "Week 3", amt: 5100 },
            { name: "Week 4", amt: 4650 },
        ],
    },
    {
        id: 3,
        title: "Active Givers",
        icon: "profile",
        counter: "234",
        percentage: 5.2,
        color: "#8B5CF6",
        dataChart: [
            { name: "Jan", amt: 220 },
            { name: "Feb", amt: 225 },
            { name: "Mar", amt: 228 },
            { name: "Apr", amt: 232 },
            { name: "May", amt: 230 },
            { name: "Jun", amt: 234 },
        ],
    },
    {
        id: 4,
        title: "Average Gift",
        icon: "star",
        counter: "$125",
        percentage: -2.1,
        color: "#F59E0B",
        dataChart: [
            { name: "Jan", amt: 128 },
            { name: "Feb", amt: 132 },
            { name: "Mar", amt: 130 },
            { name: "Apr", amt: 127 },
            { name: "May", amt: 126 },
            { name: "Jun", amt: 125 },
        ],
    },
];

const recentDonations = [
    {
        id: 1,
        donor: "John Smith",
        amount: 500,
        fund: "General Fund",
        method: "Online",
        date: "2024-01-21",
        recurring: true,
    },
    {
        id: 2,
        donor: "Sarah Wilson",
        amount: 250,
        fund: "Building Fund",
        method: "Check",
        date: "2024-01-21",
        recurring: false,
    },
    {
        id: 3,
        donor: "Anonymous",
        amount: 1000,
        fund: "Missions",
        method: "Cash",
        date: "2024-01-20",
        recurring: false,
    },
    {
        id: 4,
        donor: "Mike Johnson",
        amount: 150,
        fund: "General Fund",
        method: "Online",
        date: "2024-01-20",
        recurring: true,
    },
];

const fundBreakdown = [
    { name: "General Fund", amount: 85000, percentage: 67 },
    { name: "Building Fund", amount: 25000, percentage: 20 },
    { name: "Missions", amount: 12000, percentage: 9 },
    { name: "Youth Ministry", amount: 5450, percentage: 4 },
];

const GivingOverviewPage = () => {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <Layout title="Giving Overview">
            <div className="flex items-center justify-between mb-8 max-lg:block">
                <div className="max-lg:mb-6">
                    <h1 className="text-h3 text-t-primary mb-2">Giving Overview</h1>
                    <p className="text-body-2 text-t-secondary">
                        Track donations, manage funds, and generate giving statements
                    </p>
                </div>
                <div className="flex items-center gap-3">
                    <Button isWhite href="/giving/statements">
                        Generate Statements
                    </Button>
                    <Button href="/giving/donations/new">
                        Record Donation
                    </Button>
                </div>
            </div>

            <div className="flex max-lg:block">
                <div className="col-left">
                    <Card title="Giving Summary" className="mb-6">
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 p-5 max-lg:p-3">
                            {givingOverview.map((item) => (
                                <div key={item.id} className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-b-surface2 mr-3">
                                                <Icon 
                                                    className="w-5 h-5 fill-t-secondary" 
                                                    name={item.icon} 
                                                />
                                            </div>
                                            <div>
                                                <div className="text-h4 text-t-primary font-semibold">
                                                    {item.counter}
                                                </div>
                                                <div className="text-caption text-t-secondary">
                                                    {item.title}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center justify-between">
                                        <div className="flex-1 h-12">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <LineChart data={item.dataChart}>
                                                    <Line
                                                        type="monotone"
                                                        dataKey="amt"
                                                        stroke={item.color}
                                                        strokeWidth={2}
                                                        dot={false}
                                                    />
                                                </LineChart>
                                            </ResponsiveContainer>
                                        </div>
                                        <div className="ml-3">
                                            <Percentage value={item.percentage} />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </Card>

                    <Card title="Recent Donations" className="mb-6">
                        <div className="p-5">
                            <div className="space-y-4">
                                {recentDonations.map((donation) => (
                                    <div 
                                        key={donation.id}
                                        className="flex items-center justify-between p-4 bg-b-surface2 rounded-xl"
                                    >
                                        <div className="flex items-center">
                                            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-green-100 dark:bg-green-900 mr-4">
                                                <Icon className="w-5 h-5 fill-green-600 dark:fill-green-400" name="income" />
                                            </div>
                                            <div>
                                                <div className="text-body-2 text-t-primary font-medium">
                                                    {donation.donor}
                                                </div>
                                                <div className="flex items-center text-caption text-t-secondary">
                                                    <span>{donation.fund}</span>
                                                    <span className="mx-2">•</span>
                                                    <span>{donation.method}</span>
                                                    {donation.recurring && (
                                                        <>
                                                            <span className="mx-2">•</span>
                                                            <span className="text-blue-600">Recurring</span>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-body-1 text-t-primary font-semibold">
                                                {formatCurrency(donation.amount)}
                                            </div>
                                            <div className="text-caption text-t-secondary">
                                                {formatDate(donation.date)}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>
                </div>

                <div className="col-right">
                    <Card title="Fund Breakdown" className="mb-6">
                        <div className="p-5">
                            <div className="space-y-4">
                                {fundBreakdown.map((fund, index) => (
                                    <div key={index} className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <span className="text-body-2 text-t-primary">{fund.name}</span>
                                            <span className="text-body-2 text-t-primary font-medium">
                                                {formatCurrency(fund.amount)}
                                            </span>
                                        </div>
                                        <div className="w-full bg-b-surface2 rounded-full h-2">
                                            <div 
                                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                                style={{ width: `${fund.percentage}%` }}
                                            ></div>
                                        </div>
                                        <div className="text-caption text-t-secondary">
                                            {fund.percentage}% of total giving
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>

                    <Card title="Quick Actions" className="mb-6">
                        <div className="p-5 space-y-3">
                            <Button 
                                className="w-full justify-start" 
                                icon="plus" 
                                isWhite
                                href="/giving/donations/new"
                            >
                                Record Donation
                            </Button>
                            <Button 
                                className="w-full justify-start" 
                                icon="file" 
                                isWhite
                                href="/giving/statements"
                            >
                                Generate Statements
                            </Button>
                            <Button 
                                className="w-full justify-start" 
                                icon="chart" 
                                isWhite
                                href="/giving/reports"
                            >
                                View Reports
                            </Button>
                            <Button 
                                className="w-full justify-start" 
                                icon="settings" 
                                isWhite
                                href="/giving/funds"
                            >
                                Manage Funds
                            </Button>
                        </div>
                    </Card>
                </div>
            </div>
        </Layout>
    );
};

export default GivingOverviewPage;
