"use client";

import { useState } from "react";
import Layout from "@/components/Layout";
import Card from "@/components/Card";
import Button from "@/components/Button";
import Tabs from "@/components/Tabs";
import Icon from "@/components/Icon";

const viewTypes = [
    { id: 1, name: "Month" },
    { id: 2, name: "Week" },
    { id: 3, name: "List" },
];

const events = [
    {
        id: 1,
        title: "Sunday Morning Worship",
        type: "Service",
        date: "2024-01-21",
        startTime: "10:00",
        endTime: "11:30",
        location: "Main Sanctuary",
        attendees: 250,
        registrationRequired: false,
        status: "confirmed",
        color: "#3B82F6",
    },
    {
        id: 2,
        title: "Youth Group Meeting",
        type: "Ministry",
        date: "2024-01-21",
        startTime: "18:00",
        endTime: "20:00",
        location: "Youth Room",
        attendees: 35,
        registrationRequired: false,
        status: "confirmed",
        color: "#10B981",
    },
    {
        id: 3,
        title: "Community Outreach",
        type: "Outreach",
        date: "2024-01-22",
        startTime: "09:00",
        endTime: "15:00",
        location: "Downtown Park",
        attendees: 45,
        registrationRequired: true,
        status: "confirmed",
        color: "#F59E0B",
    },
    {
        id: 4,
        title: "Bible Study",
        type: "Study",
        date: "2024-01-23",
        startTime: "19:00",
        endTime: "20:30",
        location: "Fellowship Hall",
        attendees: 28,
        registrationRequired: false,
        status: "confirmed",
        color: "#8B5CF6",
    },
    {
        id: 5,
        title: "Marriage Conference",
        type: "Conference",
        date: "2024-01-27",
        startTime: "09:00",
        endTime: "16:00",
        location: "Main Sanctuary",
        attendees: 120,
        registrationRequired: true,
        status: "confirmed",
        color: "#EF4444",
    },
];

const EventsCalendarPage = () => {
    const [activeView, setActiveView] = useState(viewTypes[0]);
    const [selectedDate, setSelectedDate] = useState(new Date());

    const formatTime = (time: string) => {
        const [hours, minutes] = time.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            weekday: 'long',
            month: 'long',
            day: 'numeric'
        });
    };

    const getEventTypeColor = (type: string) => {
        switch (type) {
            case 'Service':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'Ministry':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'Outreach':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
            case 'Study':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
            case 'Conference':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    };

    return (
        <Layout title="Events Calendar">
            <div className="flex items-center justify-between mb-8 max-lg:block">
                <div className="max-lg:mb-6">
                    <h1 className="text-h3 text-t-primary mb-2">Events Calendar</h1>
                    <p className="text-body-2 text-t-secondary">
                        Manage church events, services, and activities
                    </p>
                </div>
                <div className="flex items-center gap-3">
                    <Button isWhite href="/events/facilities">
                        Facilities
                    </Button>
                    <Button href="/events/new">
                        Create Event
                    </Button>
                </div>
            </div>

            <div className="flex items-center justify-between mb-6 max-lg:block">
                <Tabs
                    className="max-lg:mb-4"
                    items={viewTypes}
                    value={activeView}
                    setValue={setActiveView}
                />
                <div className="flex items-center gap-3">
                    <Button isWhite icon="arrow-left" isCircle />
                    <span className="text-body-1 text-t-primary font-medium px-4">
                        January 2024
                    </span>
                    <Button isWhite icon="arrow-right" isCircle />
                </div>
            </div>

            {activeView.name === "List" ? (
                <Card title="Upcoming Events">
                    <div className="p-5">
                        <div className="space-y-4">
                            {events.map((event) => (
                                <div 
                                    key={event.id}
                                    className="p-4 bg-b-surface2 rounded-xl border border-transparent hover:border-s-stroke2 transition-all"
                                >
                                    <div className="flex items-start justify-between mb-3">
                                        <div className="flex-1">
                                            <div className="flex items-center mb-2">
                                                <div 
                                                    className="w-3 h-3 rounded-full mr-3"
                                                    style={{ backgroundColor: event.color }}
                                                ></div>
                                                <h3 className="text-body-1 text-t-primary font-medium mr-3">
                                                    {event.title}
                                                </h3>
                                                <div className={`inline-flex px-2 py-1 rounded-full text-caption font-medium ${getEventTypeColor(event.type)}`}>
                                                    {event.type}
                                                </div>
                                            </div>
                                            <div className="flex items-center text-caption text-t-secondary mb-2">
                                                <Icon className="w-4 h-4 mr-2 fill-current" name="calendar" />
                                                <span>{formatDate(event.date)}</span>
                                                <span className="mx-2">•</span>
                                                <span>{formatTime(event.startTime)} - {formatTime(event.endTime)}</span>
                                            </div>
                                            <div className="flex items-center text-caption text-t-secondary mb-2">
                                                <Icon className="w-4 h-4 mr-2 fill-current" name="map" />
                                                <span>{event.location}</span>
                                                <span className="mx-2">•</span>
                                                <span>{event.attendees} expected</span>
                                                {event.registrationRequired && (
                                                    <>
                                                        <span className="mx-2">•</span>
                                                        <span className="text-orange-600">Registration Required</span>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2 ml-4">
                                            <Button 
                                                isWhite 
                                                isCircle 
                                                icon="edit"
                                                href={`/events/${event.id}/edit`}
                                            />
                                            <Button 
                                                isWhite 
                                                isCircle 
                                                icon="dots"
                                            />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </Card>
            ) : (
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div className="lg:col-span-3">
                        <Card title="Calendar View">
                            <div className="p-5">
                                <div className="grid grid-cols-7 gap-1 mb-4">
                                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                                        <div key={day} className="p-3 text-center text-caption text-t-secondary font-medium">
                                            {day}
                                        </div>
                                    ))}
                                </div>
                                <div className="grid grid-cols-7 gap-1">
                                    {Array.from({ length: 35 }, (_, i) => {
                                        const dayNumber = i - 6; // Adjust for month start
                                        const isCurrentMonth = dayNumber > 0 && dayNumber <= 31;
                                        const hasEvent = isCurrentMonth && [21, 22, 23, 27].includes(dayNumber);
                                        
                                        return (
                                            <div 
                                                key={i}
                                                className={`relative p-3 h-24 border border-s-stroke2 rounded-lg cursor-pointer transition-colors hover:bg-b-surface2 ${
                                                    !isCurrentMonth ? 'text-t-tertiary bg-b-surface2' : ''
                                                } ${hasEvent ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                                            >
                                                <div className="text-caption text-t-primary">
                                                    {isCurrentMonth ? dayNumber : ''}
                                                </div>
                                                {hasEvent && (
                                                    <div className="absolute bottom-1 left-1 right-1">
                                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </Card>
                    </div>
                    
                    <div className="space-y-6">
                        <Card title="Today's Events">
                            <div className="p-5">
                                <div className="space-y-3">
                                    <div className="p-3 bg-b-surface2 rounded-lg">
                                        <div className="text-body-2 text-t-primary font-medium mb-1">
                                            Sunday Morning Worship
                                        </div>
                                        <div className="text-caption text-t-secondary">
                                            10:00 AM - Main Sanctuary
                                        </div>
                                    </div>
                                    <div className="p-3 bg-b-surface2 rounded-lg">
                                        <div className="text-body-2 text-t-primary font-medium mb-1">
                                            Youth Group Meeting
                                        </div>
                                        <div className="text-caption text-t-secondary">
                                            6:00 PM - Youth Room
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Card>

                        <Card title="Quick Actions">
                            <div className="p-5 space-y-3">
                                <Button 
                                    className="w-full justify-start" 
                                    icon="plus" 
                                    isWhite
                                    href="/events/new"
                                >
                                    Create Event
                                </Button>
                                <Button 
                                    className="w-full justify-start" 
                                    icon="calendar" 
                                    isWhite
                                    href="/events/facilities"
                                >
                                    Book Facility
                                </Button>
                                <Button 
                                    className="w-full justify-start" 
                                    icon="profile" 
                                    isWhite
                                    href="/events/registrations"
                                >
                                    View Registrations
                                </Button>
                            </div>
                        </Card>
                    </div>
                </div>
            )}
        </Layout>
    );
};

export default EventsCalendarPage;
