export const navigation = [
    {
        title: "Dashboard",
        icon: "dashboard",
        href: "/",
    },
    {
        title: "People",
        icon: "profile",
        list: [
            {
                title: "Directory",
                href: "/people",
            },
            {
                title: "Households",
                href: "/people/households",
            },
            {
                title: "Lists",
                href: "/people/lists",
            },
            {
                title: "Workflows",
                href: "/people/workflows",
            },
            {
                title: "Forms",
                href: "/people/forms",
            },
        ],
    },
    {
        title: "Services",
        icon: "product-think",
        list: [
            {
                title: "Plans",
                href: "/services",
            },
            {
                title: "Teams",
                href: "/services/teams",
            },
            {
                title: "Songs",
                href: "/services/songs",
            },
            {
                title: "Schedule",
                href: "/services/schedule",
                counter: 3,
            },
        ],
    },
    {
        title: "Groups",
        icon: "grid",
        list: [
            {
                title: "All Groups",
                href: "/groups",
            },
            {
                title: "Small Groups",
                href: "/groups/small-groups",
            },
            {
                title: "Classes",
                href: "/groups/classes",
            },
            {
                title: "Ministries",
                href: "/groups/ministries",
            },
        ],
    },
    {
        title: "Events",
        icon: "calendar",
        list: [
            {
                title: "Calendar",
                href: "/events",
            },
            {
                title: "Registrations",
                href: "/events/registrations",
                counter: 12,
            },
            {
                title: "Facilities",
                href: "/events/facilities",
            },
        ],
    },
    {
        title: "Check-ins",
        icon: "check",
        href: "/check-ins",
    },
    {
        title: "Giving",
        icon: "income",
        list: [
            {
                title: "Overview",
                href: "/giving",
            },
            {
                title: "Donations",
                href: "/giving/donations",
            },
            {
                title: "Pledges",
                href: "/giving/pledges",
            },
            {
                title: "Statements",
                href: "/giving/statements",
            },
            {
                title: "Funds",
                href: "/giving/funds",
            },
        ],
    },
    {
        title: "Communications",
        icon: "chat-think",
        list: [
            {
                title: "Messages",
                href: "/communications",
            },
            {
                title: "Announcements",
                href: "/communications/announcements",
            },
            {
                title: "Templates",
                href: "/communications/templates",
            },
        ],
    },
];

export const navigationUser = [
    {
        title: "My Profile",
        icon: "edit-profile",
        href: "/settings",
    },
    {
        title: "Church Directory",
        icon: "profile",
        href: "/people",
    },
    {
        title: "My Groups",
        icon: "grid",
        href: "/groups/my-groups",
    },
    {
        title: "My Schedule",
        icon: "calendar",
        href: "/services/my-schedule",
    },
    {
        title: "Analytics",
        icon: "chart",
        href: "/analytics",
    },
    {
        title: "Church Center",
        icon: "star-fill",
        href: "/church-center",
    },
];
