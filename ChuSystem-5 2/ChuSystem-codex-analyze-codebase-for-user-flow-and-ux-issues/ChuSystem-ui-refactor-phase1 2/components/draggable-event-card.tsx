"use client"

import { useDrag } from "react-dnd"
import type { Icon } from "lucide-react"
import TeamAvatars from "./team-avatars"

interface DraggableEventCardProps {
  id: string
  dayId: string
  title: string
  color: string
  icon?: Icon
  avatars?: string[]
  time?: string
  className?: string
}

export default function DraggableEventCard({
  id,
  dayId,
  title,
  color,
  icon: Icon,
  avatars = [],
  time,
  className = "",
}: DraggableEventCardProps) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: "EVENT",
    item: { id, dayId },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }))

  return (
    <div
      ref={drag}
      className={`${color} rounded-2xl p-3.5 h-24 flex flex-col shadow-ambient hover:shadow-ambient-hover transition-all cursor-move ${className} ${
        isDragging ? "opacity-50" : "opacity-100"
      }`}
    >
      {Icon && <Icon className="h-5 w-5 mb-2" />}
      <h4 className="text-sm font-medium">{title}</h4>
      {time && <p className="text-xs opacity-80 mt-1">{time}</p>}

      {avatars.length > 0 && (
        <div className="mt-auto">
          <TeamAvatars avatars={avatars} size="sm" />
        </div>
      )}
    </div>
  )
}
