"use client"

import React from "react"
import { cn } from "@/lib/utils"

interface BadgeProps {
  children: React.ReactNode
  variant?: "default" | "success" | "warning" | "error" | "info"
  size?: "sm" | "md" | "lg"
  className?: string
}

const variantClasses = {
  default: "bg-zinc-700 text-zinc-300",
  success: "bg-green-600 text-green-100",
  warning: "bg-amber-600 text-amber-100", 
  error: "bg-red-600 text-red-100",
  info: "bg-blue-600 text-blue-100",
}

const sizeClasses = {
  sm: "px-2 py-0.5 text-xs",
  md: "px-2.5 py-1 text-sm",
  lg: "px-3 py-1.5 text-base",
}

export function Badge({ 
  children, 
  variant = "default", 
  size = "md",
  className 
}: BadgeProps) {
  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full font-medium",
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
    >
      {children}
    </span>
  )
}
