"use client"

import React, { useState } from "react"
import { ChevronUpDownIcon, CheckIcon } from "@heroicons/react/16/solid"
import { cn } from "@/lib/utils"

interface Workspace {
  id: string
  name: string
  type: "church" | "organization" | "ministry"
  avatar?: string
}

interface WorkspaceSwitcherProps {
  currentWorkspace: Workspace
  workspaces?: Workspace[]
  onWorkspaceChange?: (workspace: Workspace) => void
  className?: string
}

const defaultWorkspaces: Workspace[] = [
  { id: "1", name: "Grace Community Church", type: "church" },
  { id: "2", name: "Youth Ministry", type: "ministry" },
  { id: "3", name: "Worship Team", type: "ministry" },
]

export default function WorkspaceSwitcher({
  currentWorkspace,
  workspaces = defaultWorkspaces,
  onWorkspaceChange,
  className
}: WorkspaceSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleWorkspaceSelect = (workspace: Workspace) => {
    onWorkspaceChange?.(workspace)
    setIsOpen(false)
  }

  return (
    <div className={cn("relative", className)}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full p-2 rounded-lg hover:bg-zinc-800 transition-colors text-left"
      >
        <div className="flex items-center gap-2 min-w-0">
          <div className="w-6 h-6 rounded bg-zinc-700 flex items-center justify-center text-xs font-medium">
            {currentWorkspace.name.charAt(0)}
          </div>
          <div className="min-w-0">
            <p className="text-sm font-medium truncate">{currentWorkspace.name}</p>
            <p className="text-xs text-zinc-400 capitalize">{currentWorkspace.type}</p>
          </div>
        </div>
        <ChevronUpDownIcon className="h-4 w-4 text-zinc-400 shrink-0" />
      </button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)} 
          />
          <div className="absolute top-full left-0 right-0 z-20 mt-1 bg-zinc-900 border border-zinc-800 rounded-lg shadow-lg py-1">
            {workspaces.map((workspace) => (
              <button
                key={workspace.id}
                onClick={() => handleWorkspaceSelect(workspace)}
                className="flex items-center justify-between w-full p-2 hover:bg-zinc-800 transition-colors text-left"
              >
                <div className="flex items-center gap-2 min-w-0">
                  <div className="w-6 h-6 rounded bg-zinc-700 flex items-center justify-center text-xs font-medium">
                    {workspace.name.charAt(0)}
                  </div>
                  <div className="min-w-0">
                    <p className="text-sm font-medium truncate">{workspace.name}</p>
                    <p className="text-xs text-zinc-400 capitalize">{workspace.type}</p>
                  </div>
                </div>
                {currentWorkspace.id === workspace.id && (
                  <CheckIcon className="h-4 w-4 text-green-400" />
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  )
}
