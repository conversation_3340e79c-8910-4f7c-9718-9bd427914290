"use client"

import React from "react"
import { cn } from "@/lib/utils"

interface SidebarProps {
  children: React.ReactNode
  variant?: "default" | "compact"
  size?: "sm" | "md" | "lg"
  className?: string
}

interface SidebarContentProps {
  children: React.ReactNode
  className?: string
}

interface SidebarHeaderProps {
  children: React.ReactNode
  className?: string
}

interface SidebarFooterProps {
  children: React.ReactNode
  className?: string
}

const sizeClasses = {
  sm: "w-48",
  md: "w-64",
  lg: "w-72",
}

export function Sidebar({ 
  children, 
  variant = "default", 
  size = "md",
  className 
}: SidebarProps) {
  return (
    <aside
      className={cn(
        "flex flex-col bg-zinc-900 border-r border-zinc-800",
        sizeClasses[size],
        variant === "compact" && "w-16",
        className
      )}
    >
      {children}
    </aside>
  )
}

export function SidebarContent({ children, className }: SidebarContentProps) {
  return (
    <div className={cn("flex-1 overflow-y-auto", className)}>
      {children}
    </div>
  )
}

export function SidebarHeader({ children, className }: SidebarHeaderProps) {
  return (
    <div className={cn("border-b border-zinc-800", className)}>
      {children}
    </div>
  )
}

export function SidebarFooter({ children, className }: SidebarFooterProps) {
  return (
    <div className={cn("border-t border-zinc-800", className)}>
      {children}
    </div>
  )
}
