"use client"

import React from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface AvatarProps {
  src?: string
  alt?: string
  size?: "xs" | "sm" | "md" | "lg" | "xl"
  ring?: boolean
  ringColor?: string
  online?: boolean
  shadow?: "none" | "soft" | "glow"
  className?: string
  fallback?: string
}

const sizeClasses = {
  xs: "w-6 h-6",
  sm: "w-8 h-8", 
  md: "w-10 h-10",
  lg: "w-12 h-12",
  xl: "w-16 h-16",
}

const ringClasses = {
  xs: "ring-1",
  sm: "ring-1",
  md: "ring-2", 
  lg: "ring-2",
  xl: "ring-4",
}

export default function Avatar({
  src,
  alt = "Avatar",
  size = "md",
  ring = false,
  ringColor = "ring-zinc-700/30",
  online = false,
  shadow = "none",
  className,
  fallback,
}: AvatarProps) {
  const initials = fallback || alt?.split(" ").map(n => n[0]).join("").toUpperCase() || "U"

  return (
    <div className={cn("relative inline-block", className)}>
      <div
        className={cn(
          "relative overflow-hidden rounded-full bg-zinc-800",
          sizeClasses[size],
          ring && ringClasses[size],
          ring && ringColor,
          shadow === "soft" && "shadow-md",
          shadow === "glow" && "shadow-lg shadow-zinc-900/20",
        )}
      >
        {src ? (
          <Image
            src={src}
            alt={alt}
            fill
            className="object-cover"
            sizes={`${sizeClasses[size].split(' ')[0].replace('w-', '')}px`}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-zinc-700 text-zinc-300 text-xs font-medium">
            {initials}
          </div>
        )}
      </div>
      
      {online && (
        <div
          className={cn(
            "absolute bottom-0 right-0 rounded-full bg-green-500 border-2 border-zinc-900",
            size === "xs" && "w-2 h-2",
            size === "sm" && "w-2.5 h-2.5", 
            size === "md" && "w-3 h-3",
            size === "lg" && "w-3.5 h-3.5",
            size === "xl" && "w-4 h-4",
          )}
        />
      )}
    </div>
  )
}
