import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline"

export default function AttendanceSnapshot() {
  const attendanceData = [
    { service: "9:00 AM Service", current: 145, previous: 132, trend: "+9.8%" },
    { service: "11:00 AM Service", current: 210, previous: 225, trend: "-6.7%" },
    { service: "Youth Service", current: 65, previous: 58, trend: "+12.1%" },
    { service: "Children's Ministry", current: 78, previous: 72, trend: "+8.3%" },
  ]

  return (
    <div className="space-y-4">
      {attendanceData.map((item, index) => (
        <div
          key={index}
          className="bg-zinc-800/50 rounded-xl p-3 border border-zinc-700/20 hover:border-zinc-700/40 transition-all"
        >
          <div className="flex justify-between items-center">
            <h4 className="font-medium">{item.service}</h4>
            <div
              className={`flex items-center text-xs font-medium ${
                item.trend.startsWith("+") ? "text-green-500" : "text-red-500"
              }`}
            >
              {item.trend.startsWith("+") ? (
                <ArrowUpIcon className="h-3.5 w-3.5 mr-1" />
              ) : (
                <ArrowDownIcon className="h-3.5 w-3.5 mr-1" />
              )}
              {item.trend}
            </div>
          </div>

          <div className="mt-2 flex items-end gap-2">
            <div className="text-xl font-semibold">{item.current}</div>
            <div className="text-xs text-zinc-400 mb-1">vs {item.previous} last week</div>
          </div>

          <div className="mt-2 w-full bg-zinc-700/30 rounded-full h-1.5">
            <div
              className={`h-1.5 rounded-full ${item.trend.startsWith("+") ? "bg-green-500" : "bg-red-500"}`}
              style={{ width: `${(item.current / (item.previous * 1.5)) * 100}%` }}
            ></div>
          </div>
        </div>
      ))}
    </div>
  )
}
