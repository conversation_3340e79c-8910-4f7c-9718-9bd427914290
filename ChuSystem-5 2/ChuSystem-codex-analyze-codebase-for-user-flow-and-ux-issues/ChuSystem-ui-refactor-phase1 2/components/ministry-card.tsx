import { CheckIcon, UserGroupIcon } from "@heroicons/react/16/solid"
import TeamAvatars from "./team-avatars"
import { Card } from "./ui/card"
import { Badge } from "./ui/badge"
import { cn } from "@/lib/utils"

interface MinistryCardProps {
  title: string
  status: string
  note: string
  avatars: string[]
  stats: {
    members: number
    events: number
    activity: number
  }
  chartColor: "blue" | "white" | "green" | "purple" | "orange"
  badgeText: string
  badgeColor: "blue" | "orange" | "purple" | "green" | "default" | "red"
  className?: string
  variant?: "default" | "elevated" | "glass" | "clay" | "gradient"
}

export default function MinistryCard({
  title,
  status,
  note,
  avatars,
  stats,
  chartColor,
  badgeText,
  badgeColor,
  className,
  variant = "default",
}: MinistryCardProps) {
  // Chart line color mapping
  const lineColors = {
    blue: "#3b82f6",
    white: "#ffffff",
    green: "#10b981",
    purple: "#8b5cf6",
    orange: "#f97316",
  }

  const lineColor = lineColors[chartColor]
  const gradientId = `gradient-${(title || "ministry").replace(/\s+/g, "-").toLowerCase()}`

  // Badge color mapping to UI component
  const badgeColorMap: Record<string, any> = {
    blue: "primary",
    orange: "warning",
    purple: "purple",
    green: "success",
    default: "default",
    red: "danger",
  }

  return (
    <Card className={cn("h-full", className)} variant={variant} padding="md">
      <div className="mb-5">
        <div className="flex items-center justify-between mb-3">
          <Badge color={badgeColorMap[badgeColor]} size="md">
            {badgeText}
          </Badge>
          <div className="text-xs text-zinc-400">{status}</div>
        </div>

        <div className="h-24 flex items-center justify-center mb-3">
          {chartColor === "blue" ? (
            <svg width="180" height="60" viewBox="0 0 180 60" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor={lineColor} stopOpacity="0.2" />
                  <stop offset="100%" stopColor={lineColor} stopOpacity="1" />
                </linearGradient>
              </defs>
              <path
                d="M0,45 Q40,15 90,35 T180,30"
                fill="none"
                stroke={`url(#${gradientId})`}
                strokeWidth="3"
                strokeLinecap="round"
              />
              <circle cx="180" cy="30" r="4" fill={lineColor} />
            </svg>
          ) : (
            <svg width="180" height="60" viewBox="0 0 180 60" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M0,30 L40,40 L80,20 L120,45 L180,30"
                fill="none"
                stroke={lineColor}
                strokeWidth="3"
                strokeLinecap="round"
              />
              <circle cx="180" cy="30" r="4" fill={lineColor} />
            </svg>
          )}
        </div>
      </div>

      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-sm text-zinc-400 mb-4">{note}</p>

      <TeamAvatars avatars={avatars} size="md" className="mb-4" />

      <div className="flex items-center justify-between mt-auto pt-4 text-sm text-zinc-400 border-t border-zinc-800/30">
        <div className="flex items-center gap-2 hover:text-zinc-300 transition-colors">
          <UserGroupIcon className="h-4 w-4 text-zinc-400" />
          <span>{stats.members}</span>
        </div>

        <div className="flex items-center gap-2 hover:text-zinc-300 transition-colors">
          <CheckIcon className="h-4 w-4 text-zinc-400" />
          <span>{stats.events}</span>
        </div>

        <div className="flex items-center gap-2 hover:text-zinc-300 transition-colors">
          <div
            className={cn("w-4 h-4 rounded-full", {
              "bg-blue-500 shadow-glow-blue-sm": chartColor === "blue",
              "bg-white shadow-glow-white-sm": chartColor === "white",
              "bg-green-500 shadow-glow-green-sm": chartColor === "green",
              "bg-purple-500 shadow-glow-purple-sm": chartColor === "purple",
              "bg-orange-500 shadow-glow-orange-sm": chartColor === "orange",
            })}
          ></div>
          <span>{stats.activity}</span>
        </div>
      </div>
    </Card>
  )
}
