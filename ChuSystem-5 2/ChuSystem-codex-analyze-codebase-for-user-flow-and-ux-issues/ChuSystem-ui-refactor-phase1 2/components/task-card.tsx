import { CalendarIcon, ClockIcon } from "@heroicons/react/16/solid"
import { cn } from "@/lib/utils"
import { Card } from "./ui/card"
import { Avatar } from "./ui/avatar"
import { Badge } from "./ui/badge"

interface TaskCardProps {
  title: string
  description: string
  dueDate: string
  priority: "high" | "medium" | "low"
  status: "completed" | "in-progress" | "not-started" | "overdue"
  assignee?: {
    name: string
    avatar: string
  }
  className?: string
  variant?: "default" | "elevated" | "glass" | "clay" | "gradient"
}

export default function TaskCard({
  title,
  description,
  dueDate,
  priority,
  status,
  assignee,
  className,
  variant = "default",
}: TaskCardProps) {
  // Status and priority styling
  const statusStyles = {
    completed: { text: "Completed", color: "success" as const },
    "in-progress": { text: "In Progress", color: "primary" as const },
    "not-started": { text: "Not Started", color: "secondary" as const },
    overdue: { text: "Overdue", color: "danger" as const },
  }

  const priorityStyles = {
    high: { text: "High", color: "danger" as const },
    medium: { text: "Medium", color: "warning" as const },
    low: { text: "Low", color: "info" as const },
  }

  return (
    <Card className={cn("h-full", className)} variant={variant} padding="md">
      <div className="flex items-start justify-between mb-3">
        <h3 className="text-base font-semibold line-clamp-1">{title}</h3>
        <Badge color={statusStyles[status].color} size="sm">
          {statusStyles[status].text}
        </Badge>
      </div>

      <p className="text-sm text-zinc-400 mb-4 line-clamp-2">{description}</p>

      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2 text-xs text-zinc-400">
          <CalendarIcon className="h-3.5 w-3.5" />
          <span>{dueDate}</span>
        </div>

        <Badge color={priorityStyles[priority].color} size="sm" variant="outline">
          {priorityStyles[priority].text}
        </Badge>
      </div>

      <div className="flex items-center justify-between mt-auto pt-3 border-t border-zinc-800/30">
        {assignee ? (
          <div className="flex items-center gap-2">
            <Avatar src={assignee.avatar} alt={assignee.name} size="sm" />
            <span className="text-xs">{assignee.name}</span>
          </div>
        ) : (
          <div className="text-xs text-zinc-500">Unassigned</div>
        )}

        <div className="flex items-center gap-1 text-xs text-zinc-400">
          <ClockIcon className="h-3.5 w-3.5" />
          <span>2 days left</span>
        </div>
      </div>
    </Card>
  )
}
