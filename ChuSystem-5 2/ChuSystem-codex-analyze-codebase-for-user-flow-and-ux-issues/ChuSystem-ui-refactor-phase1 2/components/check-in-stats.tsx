import { ArrowUpIcon, ArrowDownIcon, ClockIcon } from "@heroicons/react/24/outline"

export default function CheckInStats() {
  const stats = [
    {
      title: "Total Check-Ins",
      value: 187,
      change: "+12",
      trend: "up",
      period: "vs last week",
    },
    {
      title: "Adult Check-Ins",
      value: 112,
      change: "+8",
      trend: "up",
      period: "vs last week",
    },
    {
      title: "Children Check-Ins",
      value: 75,
      change: "+4",
      trend: "up",
      period: "vs last week",
    },
    {
      title: "First-Time Visitors",
      value: 14,
      change: "+3",
      trend: "up",
      period: "vs last week",
    },
  ]

  return (
    <div className="grid grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <div
          key={index}
          className="bg-zinc-900/90 rounded-xl p-4 border border-zinc-800/20 hover:border-zinc-700/30 transition-all"
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-zinc-400">{stat.title}</h3>
            <div
              className={`flex items-center text-xs font-medium ${
                stat.trend === "up" ? "text-green-500" : "text-red-500"
              }`}
            >
              {stat.trend === "up" ? (
                <ArrowUpIcon className="h-3.5 w-3.5 mr-1" />
              ) : (
                <ArrowDownIcon className="h-3.5 w-3.5 mr-1" />
              )}
              {stat.change}
            </div>
          </div>
          <div className="flex items-end gap-2">
            <div className="text-2xl font-semibold">{stat.value}</div>
            <div className="flex items-center text-xs text-zinc-500 mb-1">
              <ClockIcon className="h-3 w-3 mr-1" />
              {stat.period}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
