import { C<PERSON><PERSON>cyDollarIcon, CalendarIcon, UserIcon, CheckIcon } from "@heroicons/react/24/outline"

export default function GivingHistory() {
  const transactions = [
    {
      id: 1,
      donor: "<PERSON>",
      amount: "$250.00",
      date: "May 12, 2023",
      method: "Credit Card",
      fund: "General Fund",
      status: "Completed",
    },
    {
      id: 2,
      donor: "<PERSON>",
      amount: "$100.00",
      date: "May 12, 2023",
      method: "ACH Transfer",
      fund: "Building Fund",
      status: "Completed",
    },
    {
      id: 3,
      donor: "<PERSON>",
      amount: "$75.00",
      date: "May 10, 2023",
      method: "Credit Card",
      fund: "Missions Fund",
      status: "Completed",
    },
    {
      id: 4,
      donor: "<PERSON>",
      amount: "$150.00",
      date: "May 9, 2023",
      method: "ACH Transfer",
      fund: "General Fund",
      status: "Completed",
    },
    {
      id: 5,
      donor: "<PERSON>",
      amount: "$200.00",
      date: "May 5, 2023",
      method: "Credit Card",
      fund: "Youth Ministry",
      status: "Completed",
    },
  ]

  return (
    <div className="bg-zinc-900/90 rounded-xl border border-zinc-800/20 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-zinc-800/50 border-b border-zinc-700/30">
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Donor</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Amount</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Date</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Method</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Fund</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Status</th>
            </tr>
          </thead>
          <tbody>
            {transactions.map((transaction) => (
              <tr key={transaction.id} className="border-b border-zinc-800/30 hover:bg-zinc-800/20 transition-colors">
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <UserIcon className="h-4 w-4 mr-2 text-zinc-500" />
                    <span>{transaction.donor}</span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <CurrencyDollarIcon className="h-4 w-4 mr-2 text-zinc-500" />
                    <span className="font-medium">{transaction.amount}</span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2 text-zinc-500" />
                    <span>{transaction.date}</span>
                  </div>
                </td>
                <td className="px-4 py-3">{transaction.method}</td>
                <td className="px-4 py-3">
                  <span className="px-2 py-1 bg-blue-500/10 text-blue-400 rounded-full text-xs">
                    {transaction.fund}
                  </span>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <CheckIcon className="h-4 w-4 mr-2 text-green-500" />
                    <span className="text-green-500">{transaction.status}</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
