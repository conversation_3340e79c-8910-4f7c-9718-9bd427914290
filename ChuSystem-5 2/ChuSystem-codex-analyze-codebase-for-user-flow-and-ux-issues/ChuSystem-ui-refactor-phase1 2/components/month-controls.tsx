"use client"

import { ChevronLeft, ChevronRight, Paintbrush } from "lucide-react"
import { Button } from "./ui/button"

interface MonthControlsProps {
  month: string
  onPrevious: () => void
  onNext: () => void
  editTime?: string
}

export default function MonthControls({ month, onPrevious, onNext, editTime = "29m ago" }: MonthControlsProps) {
  return (
    <div className="flex items-center justify-between mb-6">
      <Button variant="ghost" size="icon" onClick={onPrevious} className="rounded-xl">
        <ChevronLeft className="h-5 w-5" />
      </Button>

      <div className="flex items-center gap-3">
        <h3 className="text-xl font-semibold">{month}</h3>
        <Button variant="secondary" size="icon" className="p-1.5 h-8 w-8 rounded-xl">
          <Paintbrush className="h-4 w-4" />
        </Button>
        <span className="text-xs text-zinc-400">Edited {editTime}</span>
      </div>

      <Button variant="ghost" size="icon" onClick={onNext} className="rounded-xl">
        <ChevronRight className="h-5 w-5" />
      </Button>
    </div>
  )
}
