"use client"

import { useState } from "react"
import { Button } from "./ui/button"

export default function ProjectProgress() {
  const [viewMode, setViewMode] = useState<"Month" | "Week">("Month")

  return (
    <div className="bg-zinc-900/90 bg-gradient-to-b from-zinc-900 to-zinc-900/80 rounded-xl p-5 shadow-ambient border border-zinc-800/20">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium">Progress</h3>
        <div className="flex items-center text-xs">
          <span className="text-green-400 mr-1">+14.67%</span>
          <svg width="24" height="16" viewBox="0 0 24 16" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#4ade80" stopOpacity="0.7" />
                <stop offset="100%" stopColor="#4ade80" stopOpacity="1" />
              </linearGradient>
            </defs>
            <path
              d="M2,12 L8,6 L14,10 L22,2"
              fill="none"
              stroke="url(#greenGradient)"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        </div>
      </div>

      {/* Stats Sections */}
      <div className="space-y-6">
        <div>
          <h4 className="text-sm text-zinc-400 mb-2">All Tasks</h4>
          <div className="flex items-center justify-between">
            <span className="text-4xl font-bold">108</span>
            <span className="badge-success">+13.66%</span>
          </div>
        </div>

        <div>
          <h4 className="text-sm text-zinc-400 mb-2">Done</h4>
          <div className="flex items-center justify-between">
            <span className="text-4xl font-bold">73</span>
            <span className="badge-danger">-8.05%</span>
          </div>
        </div>

        <div>
          <h4 className="text-sm text-zinc-400 mb-2">In Progress</h4>
          <div className="flex items-center justify-between">
            <span className="text-4xl font-bold">35</span>
            <span className="badge-success">+118.66%</span>
          </div>
        </div>
      </div>

      {/* Bar Chart */}
      <div className="mt-8">
        <div className="flex justify-between mb-4">
          <Button
            variant={viewMode === "Month" ? "secondary" : "ghost"}
            size="sm"
            className="px-4 py-1 text-xs rounded-xl"
            onClick={() => setViewMode("Month")}
          >
            Month
          </Button>
          <Button
            variant={viewMode === "Week" ? "secondary" : "ghost"}
            size="sm"
            className="px-4 py-1 text-xs rounded-xl"
            onClick={() => setViewMode("Week")}
          >
            Week
          </Button>
        </div>

        <div className="h-[80px] w-full relative">
          <div className="absolute bottom-0 left-0 w-full">
            <div className="relative h-[80px]">
              {/* Chart bars */}
              <div className="absolute bottom-0 left-[6%] w-[16px] h-[55%] bg-gradient-to-t from-blue-600 to-blue-500 rounded-t-xl shadow-glow-blue-sm"></div>
              <div className="absolute bottom-0 left-[46%] w-[16px] h-[75%] bg-gradient-to-t from-blue-600 to-blue-500 rounded-t-xl shadow-glow-blue-sm"></div>
              <div className="absolute bottom-0 left-[76%] w-[16px] h-[40%] bg-gradient-to-t from-blue-600 to-blue-500 rounded-t-xl shadow-glow-blue-sm"></div>

              {/* Chart labels */}
              <div className="absolute bottom-[-20px] left-[5%] text-xs text-zinc-400">1 Oct</div>
              <div className="absolute bottom-[-20px] left-[44%] text-xs text-zinc-400">8 Oct</div>
              <div className="absolute bottom-[-20px] left-[75%] text-xs text-zinc-400">15 Oct</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
