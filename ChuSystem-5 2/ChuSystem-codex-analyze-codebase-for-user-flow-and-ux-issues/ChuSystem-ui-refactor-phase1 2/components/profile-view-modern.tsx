"use client"

import { useState } from "react"
import {
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  BriefcaseIcon,
  CalendarIcon,
  DocumentTextIcon,
  PlusIcon,
} from "@heroicons/react/24/outline"
import Image from "next/image" // Import next/image
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs" // Already using ui/tabs
import { Card } from "@/components/ui/card" // Import design system Card
import { Badge } from "@/components/ui/badge" // Import design system Badge
import { cn } from "@/lib/utils" // Ensure cn is imported

interface ProfileViewProps {
  person: {
    id: string
    name: string
    email: string
    phone: string
    location: string
    role: string
    avatar: string
    joinedDate: string
    ministries: string[]
    familyMembers: Array<{ name: string; relationship: string }>
    attendance: { percentage: number; lastAttended: string }
    giving: { totalThisYear: number; lastGift: string }
  }
}

export default function ProfileViewModern({ person }: ProfileViewProps) {
  const [activeTab, setActiveTab] = useState("analytics")

  const metrics = [
    {
      label: "Average Score",
      value: "430",
      change: "+138 pts",
      trend: "up",
      color: "bg-yellow-500",
      chartData: [20, 35, 40, 25, 50, 45, 60, 55, 65, 70, 75, 80],
    },
    {
      label: "Deal Started",
      value: "19",
      subtitle: "Last weeks",
      color: "bg-yellow-500",
      dotPattern: true,
    },
    {
      label: "Lost Deals",
      value: "25",
      change: "+2%",
      trend: "up",
      color: "bg-red-500",
      dotPattern: true,
    },
    {
      label: "Won Deals",
      value: "24",
      subtitle: "This month",
      color: "bg-green-500",
      linePattern: true,
    },
  ]

  return (
    <div className="flex-1 p-6 space-y-8">
      {/* Profile Header */}
      <Card variant="glass" padding="lg" className="profile-card"> {/* Using profile-card for specific header styles if needed or just glass */}
        <div className="flex items-start gap-6">
          <Image
            src={person.avatar || "/placeholder.svg"}
            alt={person.name}
            width={128} // Corresponds to w-32
            height={128} // Corresponds to h-32
            className="rounded-2xl object-cover border border-[var(--border-subtle)] shadow-lg" // Themed border and shadow
          />
          <div className="flex-1">
            <h1 className="text-3xl font-bold mb-1 text-white">{person.name}</h1>
            <p className={cn("text-amber-400 mb-4 text-sm")}>Diamond Tier Member</p> {/* Example Badge/Status text */}


            <div className="grid grid-cols-2 gap-x-6 gap-y-3 mb-6">
              {(
                [
                  { icon: PhoneIcon, value: person.phone },
                  { icon: EnvelopeIcon, value: person.email },
                  { icon: MapPinIcon, value: person.location },
                  { icon: BriefcaseIcon, value: person.role },
                ] as const
              ).map((item, idx) => (
                <div key={idx} className="flex items-center gap-2 text-zinc-300">
                  <item.icon className="h-4 w-4 text-zinc-400" />
                  <span className="text-sm">{item.value}</span>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              {[
                { label: "Call", icon: PhoneIcon, variant: "secondary" as const },
                { label: "Email", icon: EnvelopeIcon, variant: "secondary" as const },
                { label: "Schedule", icon: CalendarIcon, variant: "secondary" as const },
                { label: "Note", icon: DocumentTextIcon, variant: "secondary" as const },
              ].map((btn) => (
                <Button key={btn.label} variant={btn.variant} size="default">
                  <btn.icon className="h-4 w-4 mr-2" />
                  {btn.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="bg-[var(--bg-glass)] border border-[var(--border-subtle)] rounded-xl p-1.5">
          <TabsTrigger value="analytics" className={cn("tab-modern", activeTab === "analytics" && "tab-modern-active")}>
            Analytics
          </TabsTrigger>
          <TabsTrigger value="general" className={cn("tab-modern", activeTab === "general" && "tab-modern-active")}>
            General
          </TabsTrigger>
          <TabsTrigger value="summary" className={cn("tab-modern", activeTab === "summary" && "tab-modern-active")}>
            Summary
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <Card key={index} variant="glass" padding="lg" className="metric-card"> {/* Using .metric-card for specific metric styles */}
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-zinc-300">{metric.label}</span>
              {metric.change && (
                <span
                  className={cn(
                    "text-xs font-semibold",
                    metric.trend === "up" ? "text-green-400" : "text-red-400",
                  )}
                >
                  {metric.change}
                </span>
              )}
            </div>
            <div className="text-3xl font-bold text-white mb-1">{metric.value}</div>
            {metric.subtitle && <div className="text-xs text-zinc-400">{metric.subtitle}</div>}
            <div className="mt-4 h-16 relative">
              {metric.chartData && (
                <div className="flex items-end justify-between h-full">
                  {metric.chartData.map((value, i) => (
                    <div key={i} className={cn("w-2 rounded-t", metric.color)} style={{ height: `${value}%` }} />
                  ))}
                </div>
              )}
              {metric.dotPattern && (
                <div className={cn("absolute inset-0 rounded-xl opacity-10", metric.color)}>
                  <div
                    className="w-full h-full"
                    style={{
                      backgroundImage: "radial-gradient(circle, currentColor 1px, transparent 1px)",
                      backgroundSize: "8px 8px", // Adjusted size
                    }}
                  />
                </div>
              )}
              {metric.linePattern && (
                <div className={cn("absolute inset-0 rounded-xl opacity-10", metric.color)}>
                  <div
                    className="w-full h-full"
                    style={{
                      backgroundImage:
                        "repeating-linear-gradient(45deg, currentColor, currentColor 0.5px, transparent 0.5px, transparent 5px)", // Adjusted thickness/spacing
                    }}
                  />
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>

      {/* Additional Info Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card variant="glass" padding="lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-white">Family Members</h3>
            <Button size="icon-sm" variant="ghost" className="rounded-xl">
              <PlusIcon className="h-4 w-4 text-zinc-300" />
            </Button>
          </div>
          <div className="space-y-3">
            {person.familyMembers.map((member, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <span className="text-zinc-100">{member.name}</span>
                <span className="text-zinc-400">{member.relationship}</span>
              </div>
            ))}
          </div>
        </Card>

        <Card variant="glass" padding="lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-white">Ministry Involvement</h3>
            <Button size="icon-sm" variant="ghost" className="rounded-xl">
              <PlusIcon className="h-4 w-4 text-zinc-300" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {person.ministries.map((ministry, index) => (
              // Assuming 'primary' color for Badge maps to yellow or a suitable theme color.
              // If specific yellow is needed, ensure Badge component or its color prop handles it.
              <Badge key={index} color="primary" size="sm">
                {ministry}
              </Badge>
            ))}
          </div>
        </Card>
      </div>
    </div>
  )
}
