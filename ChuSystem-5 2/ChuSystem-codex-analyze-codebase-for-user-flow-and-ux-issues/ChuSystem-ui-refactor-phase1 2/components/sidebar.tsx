"use client"

import { useState } from "react"
import {
  ChevronRightIcon,
  CalendarIcon,
  InboxIcon,
  DocumentTextIcon,
  PlusIcon,
  UserGroupIcon,
  HeartIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon,
  ChartBarIcon,
  ChevronDownIcon,
  HomeIcon,
} from "@heroicons/react/16/solid"
import SidebarItem from "./sidebar-item"
import DropdownSection from "./design-system/dropdown-section"
import SidebarLink from "./design-system/sidebar-link"
import { Button } from "./ui/button"
import MenuDropdown from "./design-system/menu-dropdown"
import { Badge } from "./design-system/badge"
import Avatar from "./design-system/avatar"
import { Sidebar as SidebarComponent, SidebarContent, SidebarFooter, SidebarHeader } from "./design-system/sidebar"
import WorkspaceSwitcher from "./design-system/workspace-switcher"

export default function Sidebar() {
  const [expanded, setExpanded] = useState({
    ministries: true,
    groups: true,
    resources: true,
  })

  const toggleSection = (section: keyof typeof expanded) => {
    setExpanded((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const profileMenuItems = [
    {
      id: "settings",
      label: "Account Settings",
      icon: <Cog6ToothIcon className="size-4 text-zinc-400" />,
      onClick: () => console.log("Settings clicked"),
    },
    {
      id: "help",
      label: "Help & Support",
      icon: <QuestionMarkCircleIcon className="size-4 text-zinc-400" />,
      onClick: () => console.log("Help clicked"),
    },
    {
      id: "divider",
      isDivider: true,
    },
    {
      id: "signout",
      label: "Sign Out",
      icon: (
        <svg className="size-4 text-red-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16 8L8 16M8 8L16 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      ),
      onClick: () => console.log("Sign out clicked"),
      className: "text-red-400",
    },
  ]

  return (
    <SidebarComponent variant="default" size="md" className="hidden md:flex">
      {/* User Profile */}
      <SidebarHeader className="p-4 flex items-center gap-3">
        <Avatar
          src="/avatar1.png"
          alt="User Profile"
          size="md"
          ring={true}
          ringColor="ring-zinc-700/30"
          online={true}
          shadow="glow"
        />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">Pastor Michael</p>
          <p className="text-xs text-zinc-400 truncate">Lead Pastor</p>
        </div>
        <MenuDropdown
          trigger={
            <Button variant="ghost" size="icon" className="ml-auto p-1 h-7 w-7 rounded-full">
              <ChevronDownIcon className="h-4 w-4 text-zinc-400" />
            </Button>
          }
          items={profileMenuItems}
        />
      </SidebarHeader>

      {/* Enhanced Workspace Switcher Section */}
      <div className="px-3 pb-2">
        <WorkspaceSwitcher />
      </div>

      {/* Subtle Divider */}
      <div className="mx-4 mb-3">
        <div className="h-px bg-gradient-to-r from-transparent via-zinc-700/30 to-transparent" />
      </div>

      {/* Navigation Sections */}
      <SidebarContent>
        {/* Main Navigation */}
        <div className="space-y-1.5">
          <div className="flex items-center justify-between mb-2 px-1">
            <span className="text-xs text-zinc-500 font-medium uppercase tracking-wider">Main</span>
          </div>

          <SidebarItem
            icon={<HomeIcon className="h-5 w-5 text-zinc-400" />}
            label="Dashboard"
            badge={{ type: "count", value: "2" }}
            active={true}
            className="py-2.5"
          />
          <SidebarItem
            icon={<UserGroupIcon className="h-5 w-5 text-zinc-400" />}
            label="People"
            badge={{ type: "count", value: "5" }}
            className="py-2.5"
          />
          <SidebarItem
            icon={<CalendarIcon className="h-5 w-5 text-zinc-400" />}
            label="Events"
            badge={{ type: "count", value: "3" }}
            className="py-2.5"
          />
          <SidebarItem icon={<ChartBarIcon className="h-5 w-5 text-zinc-400" />} label="Giving" className="py-2.5" />

          <DropdownSection
            label="Ministries"
            icon={<HeartIcon className="h-5 w-5" />}
            labelClassName="text-white font-medium"
            iconColor="text-blue-400"
            className="bg-blue-900/20 rounded-xl mt-2 p-1"
            defaultExpanded={expanded.ministries}
            onClick={() => toggleSection("ministries")}
            badge={
              <Badge variant="circle" color="default" size="md">
                6
              </Badge>
            }
          >
            <div className="pl-10 space-y-3 py-1">
              <SidebarLink
                label="Youth Ministry"
                icon={<div className="w-2 h-2 rounded-full bg-green-400"></div>}
                className="py-1.5"
              />
              <SidebarLink
                label="Worship Team"
                icon={<div className="w-2 h-2 rounded-full bg-blue-400"></div>}
                className="py-1.5"
              />
              <SidebarLink
                label="Community Outreach"
                icon={<div className="w-2 h-2 rounded-full bg-orange-500"></div>}
                textColor="text-white"
                className="py-1.5"
              />
              <SidebarLink
                label="Show More"
                icon={<ChevronRightIcon className="h-3.5 w-3.5" />}
                indicator={
                  <Badge variant="circle" size="sm">
                    +3
                  </Badge>
                }
                className="py-1.5"
              />
              <SidebarLink
                label="Create New Ministry"
                icon={<PlusIcon className="h-3.5 w-3.5" />}
                textColor="text-blue-400"
                className="py-1.5"
              />
            </div>
          </DropdownSection>
        </div>

        {/* Groups & Check-In */}
        <div className="space-y-1.5">
          <div className="flex items-center justify-between mb-2 px-1">
            <span className="text-xs text-zinc-500 font-medium uppercase tracking-wider">Groups</span>
          </div>

          <DropdownSection
            label="Small Groups"
            icon={<UserGroupIcon className="h-5 w-5 text-zinc-400" />}
            defaultExpanded={expanded.groups}
            onClick={() => toggleSection("groups")}
            className="py-1"
          >
            <div className="pl-10 space-y-3 py-1">
              <SidebarLink
                label="Small Groups"
                icon={<div className="w-2 h-2 rounded-full bg-purple-400"></div>}
                className="py-1.5"
              />
              <SidebarLink
                label="Bible Studies"
                icon={<div className="w-2 h-2 rounded-full bg-yellow-400"></div>}
                className="py-1.5"
              />
              <SidebarLink label="Create New Group" icon={<PlusIcon className="h-3.5 w-3.5" />} className="py-1.5" />
            </div>
          </DropdownSection>

          <SidebarItem
            icon={<CalendarIcon className="h-5 w-5 text-zinc-400" />}
            label="Check-In"
            badge={{ type: "count", value: "3", color: "bg-amber-500" }}
            className="py-2.5"
          />
        </div>

        {/* Resources */}
        <div className="space-y-1.5">
          <div className="flex items-center justify-between mb-2 px-1">
            <span className="text-xs text-zinc-500 font-medium uppercase tracking-wider">Resources</span>
          </div>

          <SidebarItem
            icon={<InboxIcon className="h-5 w-5 text-zinc-400" />}
            label={
              <div className="flex items-center gap-1.5">
                Messages <span className="text-xs text-zinc-400">(12)</span>
              </div>
            }
            badge={{ type: "count", value: "3", color: "bg-amber-500" }}
            className="py-2.5"
          />

          <SidebarItem
            icon={<DocumentTextIcon className="h-5 w-5 text-zinc-400" />}
            label="Documents"
            badge={{ type: "text", value: "New", color: "text-blue-400" }}
            className="py-2.5"
          />

          <SidebarItem icon={<ChartBarIcon className="h-5 w-5 text-zinc-400" />} label="Reports" className="py-2.5" />
        </div>
      </SidebarContent>

      <SidebarFooter className="p-4 flex items-center justify-between">
        <SidebarItem
          icon={<Cog6ToothIcon className="h-5 w-5 text-zinc-400" />}
          label="Settings"
          className="py-2.5 bg-zinc-800/30"
        />
        <SidebarItem
          icon={<QuestionMarkCircleIcon className="h-5 w-5 text-zinc-400" />}
          label="Help & Support"
          className="py-2.5 mt-2 bg-zinc-800/30"
        />
      </SidebarFooter>
    </SidebarComponent>
  )
}
