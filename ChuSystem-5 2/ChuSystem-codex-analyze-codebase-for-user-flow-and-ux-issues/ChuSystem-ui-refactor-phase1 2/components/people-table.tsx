"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ChevronDownIcon, ChevronUpIcon, EnvelopeIcon, PhoneIcon, PencilIcon, TrashIcon } from "@heroicons/react/24/outline" // Removed EyeIcon
import Image from "next/image"
import { cn } from "@/lib/utils"
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox" // Import Checkbox

interface Person {
  id: string // Changed to string as per instructions
  name: string
  email: string
  phone: string
  status: "Active" | "Inactive" | "New" | "Visitor"
  ministry: string[]
  lastAttended: string
  avatarUrl: string
}

interface PeopleTableProps {
  onSelectionChange?: (selectedIds: Set<string>) => void
}

export default function PeopleTable({ onSelectionChange }: PeopleTableProps) {
  const router = useRouter()
  const [sortField, setSortField] = useState<keyof Person>("name")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [selectedRowIds, setSelectedRowIds] = useState<Set<string>>(new Set())

  // Sample Data - id remains number for simplicity of data, will be stringified for Set
  const people: Omit<Person, "id"> & { id: number }[] = [
    {
      id: 1,
      name: "Michael Johnson",
      email: "<EMAIL>",
      phone: "(*************",
      status: "Active",
      ministry: ["Worship Team", "Small Groups"],
      lastAttended: "May 12, 2023",
      avatarUrl: "/avatar1.png",
    },
    {
      id: 2,
      name: "Sarah Williams",
      email: "<EMAIL>",
      phone: "(*************",
      status: "Active",
      ministry: ["Children's Ministry"],
      lastAttended: "May 12, 2023",
      avatarUrl: "/avatar2.png",
    },
    {
      id: 3,
      name: "David Thompson",
      email: "<EMAIL>",
      phone: "(*************",
      status: "Inactive",
      ministry: ["Youth Ministry"],
      lastAttended: "Apr 28, 2023",
      avatarUrl: "/avatar3.png",
    },
    {
      id: 4,
      name: "Emily Davis",
      email: "<EMAIL>",
      phone: "(*************",
      status: "New",
      ministry: [],
      lastAttended: "May 12, 2023",
      avatarUrl: "/avatar4.png",
    },
     {
      id: 5,
      name: "James Wilson",
      email: "<EMAIL>",
      phone: "(*************",
      status: "Active",
      ministry: ["Outreach", "Prayer Team"],
      lastAttended: "May 5, 2023",
      avatarUrl: "/avatar1.png", // Placeholder, cycle if more needed
    },
    {
      id: 6,
      name: "Olivia Martinez",
      email: "<EMAIL>",
      phone: "(*************",
      status: "Visitor",
      ministry: [],
      lastAttended: "May 12, 2023",
      avatarUrl: "/avatar2.png", // Placeholder
    },
  ]

  const handleSort = (field: keyof Person) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  const sortedPeople = [...people].sort((a, b) => {
    // Basic sort, can be enhanced for different data types
    if (a[sortField] < b[sortField]) return sortDirection === "asc" ? -1 : 1
    if (a[sortField] > b[sortField]) return sortDirection === "asc" ? 1 : -1
    return 0
  })

  // Effect to call onSelectionChange when selectedRowIds change
  useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(selectedRowIds)
    }
  }, [selectedRowIds, onSelectionChange])

  const handleSelectAll = (checked: boolean | "indeterminate") => {
    if (checked === true) {
      setSelectedRowIds(new Set(people.map((p) => p.id.toString())))
    } else {
      setSelectedRowIds(new Set())
    }
  }

  const handleRowSelect = (rowId: string) => {
    setSelectedRowIds((prev) => {
      const newSelected = new Set(prev)
      if (newSelected.has(rowId)) {
        newSelected.delete(rowId)
      } else {
        newSelected.add(rowId)
      }
      return newSelected
    })
  }

  const getStatusBadgeColor = (status: string): "success" | "default" | "primary" | "purple" => {
    switch (status) {
      case "Active": return "success";
      case "Inactive": return "default"; // Or a specific grey/zinc if defined in Badge
      case "New": return "primary"; // Or 'info' if blue is desired
      case "Visitor": return "purple";
      default: return "default";
    }
  }
  
  const SortableHeader = ({ field, label }: { field: keyof Person; label: string }) => (
    <TableHead
      className="cursor-pointer hover:bg-[var(--bg-glass-hover)] transition-colors"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center gap-1 text-zinc-300">
        {label}
        {sortField === field && (
          sortDirection === "asc" ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
        )}
      </div>
    </TableHead>
  )

  return (
    // The parent DataCard provides the overall glass background.
    // Table itself can be mostly transparent.
    <div className="w-full overflow-x-auto custom-scrollbar">
      <Table className="min-w-full">
        <TableHeader className="border-b border-[var(--border-subtle)]">
          <TableRow>
            <TableHead className="w-12 px-4">
              <Checkbox
                checked={
                  people.length > 0 && selectedRowIds.size === people.length
                    ? true
                    : selectedRowIds.size > 0
                    ? "indeterminate"
                    : false
                }
                onCheckedChange={handleSelectAll}
                aria-label="Select all rows"
                className="border-[var(--border-light)] bg-[var(--bg-glass)] data-[state=checked]:bg-[var(--color-primary)] data-[state=checked]:text-black"
              />
            </TableHead>
            <SortableHeader field="name" label="Name" />
            <SortableHeader field="email" label="Contact Info" />
            <SortableHeader field="status" label="Status" />
            <SortableHeader field="ministry" label="Ministry" />
            <SortableHeader field="lastAttended" label="Last Attended" />
            <TableHead className="text-right text-zinc-300">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedPeople.map((person) => (
            <TableRow
              key={person.id}
              className={cn(
                "border-b border-[var(--border-subtle)] transition-colors cursor-pointer hover:bg-[var(--bg-glass-hover)]",
                selectedRowIds.has(person.id.toString()) && "bg-[var(--bg-glass-hover)]", // Apply selected style
              )}
              onClick={() => router.push(`/people/${person.id.toString()}`)}
            >
              <TableCell onClick={(e) => e.stopPropagation()} className="px-4">
                <Checkbox
                  checked={selectedRowIds.has(person.id.toString())}
                  onCheckedChange={() => handleRowSelect(person.id.toString())}
                  aria-label={`Select row ${person.id}`}
                  className="border-[var(--border-light)] bg-[var(--bg-glass)] data-[state=checked]:bg-[var(--color-primary)] data-[state=checked]:text-black"
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Image
                    src={person.avatarUrl || `/avatar${(person.id % 4) + 1}.png`}
                    alt={person.name}
                    width={32} // w-8
                    height={32} // h-8
                    className="rounded-full object-cover ring-1 ring-[var(--border-light)]"
                  />
                  <span className="font-medium text-white">{person.name}</span>
                </div>
              </TableCell>
              <TableCell className="text-zinc-300">
                <div className="space-y-0.5">
                  <div className="flex items-center text-sm">
                    <EnvelopeIcon className="h-3.5 w-3.5 mr-1.5 text-zinc-400" />
                    <span>{person.email}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <PhoneIcon className="h-3.5 w-3.5 mr-1.5 text-zinc-400" />
                    <span>{person.phone}</span>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge color={getStatusBadgeColor(person.status)} size="sm">
                  {person.status}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {person.ministry.length > 0 ? (
                    person.ministry.map((min, index) => (
                      <Badge key={index} color="default" size="sm" className="bg-[var(--bg-glass-hover)] text-zinc-300 border-[var(--border-subtle)]">
                        {min}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-zinc-500 text-xs">None</span>
                  )}
                </div>
              </TableCell>
              <TableCell className="text-sm text-zinc-300">{person.lastAttended}</TableCell>
              <TableCell className="text-right" onClick={(e) => e.stopPropagation()}> {/* Stop propagation for actions cell */}
                <div className="flex items-center justify-end space-x-1">
                  {[
                    // Removed EyeIcon as row click handles navigation
                    { icon: PencilIcon, label: "Edit", variant: "ghost" as const, action: () => console.log(`Edit ${person.id}`) },
                    { icon: TrashIcon, label: "Delete", variant: "ghost" as const, className: "hover:text-red-400", action: () => console.log(`Delete ${person.id}`) },
                  ].map(btn => (
                    <Button 
                      key={btn.label} 
                      variant={btn.variant} 
                      size="icon-sm" 
                      className={cn("rounded-lg p-1.5", btn.className)}
                      onClick={(e) => { // Ensure button clicks also stop propagation
                        e.stopPropagation();
                        btn.action();
                      }}
                    >
                      <btn.icon className="h-4 w-4" />
                      <span className="sr-only">{btn.label}</span>
                    </Button>
                  ))}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
