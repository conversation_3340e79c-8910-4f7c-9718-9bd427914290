"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { ChevronDownIcon } from "@heroicons/react/16/solid"

// Interface for individual dropdown items, compatible with existing usage
interface DropdownMenuItemConfig {
  id: string
  label: string
  icon?: React.ReactNode
  onClick?: () => void
  shortcut?: string
  isDivider?: boolean
  className?: string // Allow custom class for individual items
  disabled?: boolean
}

// Props for the DropdownMenu component
interface DropdownMenuProps {
  trigger: React.ReactNode // The element that opens the dropdown
  items: DropdownMenuItemConfig[]
  align?: "start" | "center" | "end"
  className?: string // Class for the root container
  triggerClassName?: string // Class for the trigger button, if a default is provided
  contentClassName?: string // Class for the dropdown content area
}

const DropdownMenu = ({
  trigger,
  items,
  align = "end",
  className,
  triggerClassName,
  contentClassName,
}: DropdownMenuProps) => {
  return (
    <DropdownMenuPrimitive.Root>
      <DropdownMenuPrimitive.Trigger asChild className={cn(triggerClassName)}>
        {/* If trigger is a simple string, wrap it in a button or provide a default styled button */}
        {typeof trigger === "string" ? (
          <button
            className={cn(
              "inline-flex items-center justify-center gap-2 rounded-xl px-4 py-2.5 text-sm font-medium text-white transition-colors",
              "bg-[var(--bg-glass)] border border-[var(--border-subtle)] hover:bg-[var(--bg-glass-hover)]", // Themed button
              "focus:outline-none focus-visible:ring-2 focus-visible:ring-[rgba(var(--color-primary),0.5)]",
              triggerClassName, // Allow overriding
            )}
          >
            {trigger}
            <ChevronDownIcon className="size-4 text-zinc-400" />
          </button>
        ) : (
          trigger // Use the provided trigger directly if it's a ReactNode
        )}
      </DropdownMenuPrimitive.Trigger>
      <DropdownMenuPrimitive.Content
        align={align}
        className={cn(
          "z-50 min-w-[14rem] overflow-hidden rounded-xl border border-[var(--border-subtle)] bg-[var(--bg-glass)] p-1.5 text-white shadow-xl backdrop-blur-xl",
          // Animations (optional, can be customized or use shadcn's default)
          "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
          contentClassName,
        )}
      >
        {items.map((item) =>
          item.isDivider ? (
            <DropdownMenuPrimitive.Separator
              key={item.id}
              className={cn("-mx-1 my-1 h-px bg-[var(--border-subtle)]", item.className)}
            />
          ) : (
            <DropdownMenuPrimitive.Item
              key={item.id}
              onClick={item.onClick}
              disabled={item.disabled}
              className={cn(
                "relative flex cursor-pointer select-none items-center gap-2 rounded-lg px-3 py-2 text-sm outline-none transition-colors",
                "hover:bg-[var(--bg-glass-hover)] focus:bg-[var(--bg-glass-hover)]",
                "data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
                item.className,
              )}
            >
              {item.icon && <span className="size-4 text-zinc-300 group-hover:text-white">{item.icon}</span>}
              <span className="flex-grow">{item.label}</span>
              {item.shortcut && (
                <span className="text-xs text-zinc-500 group-hover:text-zinc-300">
                  {item.shortcut}
                </span>
              )}
            </DropdownMenuPrimitive.Item>
          ),
        )}
      </DropdownMenuPrimitive.Content>
    </DropdownMenuPrimitive.Root>
  )
}

// Re-exporting Radix dropdown primitives for advanced use
export const DropdownMenuRoot = DropdownMenuPrimitive.Root
export const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger  
export const DropdownMenuContent = DropdownMenuPrimitive.Content
export const DropdownMenuItem = DropdownMenuPrimitive.Item
export const DropdownMenuCheckboxItem = DropdownMenuPrimitive.CheckboxItem
export const DropdownMenuRadioItem = DropdownMenuPrimitive.RadioItem
export const DropdownMenuLabel = DropdownMenuPrimitive.Label
export const DropdownMenuSeparator = DropdownMenuPrimitive.Separator
export const DropdownMenuGroup = DropdownMenuPrimitive.Group
export const DropdownMenuPortal = DropdownMenuPrimitive.Portal
export const DropdownMenuSub = DropdownMenuPrimitive.Sub
export const DropdownMenuSubContent = DropdownMenuPrimitive.SubContent
export const DropdownMenuSubTrigger = DropdownMenuPrimitive.SubTrigger
export const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup

export { DropdownMenu }
export default DropdownMenu
