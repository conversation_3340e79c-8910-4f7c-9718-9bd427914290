"use client"

import React, { useState } from "react"
import { ChevronUpDownIcon, CheckIcon, PlusIcon } from "@heroicons/react/16/solid"
import { cn } from "@/lib/utils"
import GlassTooltip from "./glass-tooltip"

interface Workspace {
  id: string
  name: string
  type: "church" | "organization" | "ministry"
  avatar?: string
  isActive?: boolean
}

interface GlassWorkspaceSwitcherProps {
  currentWorkspace: Workspace
  workspaces?: Workspace[]
  onWorkspaceChange?: (workspace: Workspace) => void
  onAddWorkspace?: () => void
  className?: string
  variant?: "default" | "glass" | "compact"
  isCollapsed?: boolean
}

const defaultWorkspaces: Workspace[] = [
  { id: "1", name: "Grace Community Church", type: "church", isActive: true },
  { id: "2", name: "Youth Ministry", type: "ministry", isActive: false },
  { id: "3", name: "Worship Team", type: "ministry", isActive: false },
]

export default function GlassWorkspaceSwitcher({
  currentWorkspace,
  workspaces = defaultWorkspaces,
  onWorkspaceChang<PERSON>,
  onAddWorkspace,
  className,
  variant = "glass",
  isCollapsed = false
}: GlassWorkspaceSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleWorkspaceSelect = (workspace: Workspace) => {
    onWorkspaceChange?.(workspace)
    setIsOpen(false)
  }

  const triggerStyles = {
    default: "flex items-center justify-between w-full p-3 rounded-xl hover:bg-white/5 transition-all duration-200 text-left",
    glass: "flex items-center justify-between w-full p-3 rounded-xl bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] hover:bg-white/5 transition-all duration-200 text-left shadow-glass",
    compact: "flex items-center justify-between w-full p-2 rounded-lg hover:bg-white/5 transition-colors text-left"
  }

  const dropdownStyles = {
    default: "absolute bottom-full left-0 right-0 mb-2 bg-[var(--bg-card)] border border-[var(--border-subtle)] rounded-xl shadow-lg",
    glass: "absolute bottom-full left-0 right-0 mb-2 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-lg animate-in slide-in-from-bottom-2 duration-200",
    compact: "absolute bottom-full left-0 right-0 mb-2 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-lg shadow-lg"
  }

  const triggerContent = (
    <button
      onClick={() => setIsOpen(!isOpen)}
      className={cn(
        triggerStyles[variant],
        isCollapsed && "justify-center"
      )}
    >
      <div className="flex items-center gap-3 min-w-0">
        <div className="relative">
          <div className="w-8 h-8 rounded-lg bg-[var(--bg-glass)] border border-[var(--border-subtle)] flex items-center justify-center text-sm font-medium">
            {currentWorkspace.name.charAt(0)}
          </div>
          {currentWorkspace.isActive && (
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-[var(--color-green)] rounded-full border-2 border-[var(--bg-sidebar)]" />
          )}
        </div>
        {!isCollapsed && (
          <div className="min-w-0 flex-1">
            <p className="text-sm font-medium text-white truncate">{currentWorkspace.name}</p>
            <p className="text-xs text-zinc-400 capitalize">{currentWorkspace.type}</p>
          </div>
        )}
      </div>
      {!isCollapsed && (
        <ChevronUpDownIcon className={cn(
          "h-4 w-4 text-zinc-400 transition-transform duration-200 shrink-0",
          isOpen && "rotate-180"
        )} />
      )}
    </button>
  )

  if (isCollapsed) {
    return (
      <div className={cn("p-3 border-t border-[var(--border-subtle)]", className)}>
        <GlassTooltip 
          content={`${currentWorkspace.name} (${currentWorkspace.type})`}
          side="right"
        >
          {triggerContent}
        </GlassTooltip>
      </div>
    )
  }

  return (
    <div className={cn("relative p-3 border-t border-[var(--border-subtle)]", className)}>
      {triggerContent}

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)} 
          />
          <div className={cn("z-20", dropdownStyles[variant])}>
            {/* Header */}
            <div className="p-3 border-b border-[var(--border-subtle)]">
              <p className="text-xs font-medium text-zinc-400 uppercase tracking-wider">
                Workspaces
              </p>
            </div>
            
            {/* Workspace List */}
            <div className="p-2 space-y-1 max-h-64 overflow-y-auto custom-scrollbar">
              {workspaces.map((workspace) => (
                <button
                  key={workspace.id}
                  onClick={() => handleWorkspaceSelect(workspace)}
                  className={cn(
                    "w-full flex items-center gap-3 p-2 rounded-lg transition-colors text-left",
                    "hover:bg-white/5 focus:outline-none focus:bg-white/5",
                    workspace.id === currentWorkspace.id && "bg-[var(--color-primary)]/10 border border-[var(--color-primary)]/20"
                  )}
                >
                  <div className="relative">
                    <div className="w-7 h-7 rounded-lg bg-[var(--bg-glass)] border border-[var(--border-subtle)] flex items-center justify-center text-xs font-medium">
                      {workspace.name.charAt(0)}
                    </div>
                    {workspace.isActive && (
                      <div className="absolute -bottom-1 -right-1 w-2.5 h-2.5 bg-[var(--color-green)] rounded-full border border-[var(--bg-glass)]" />
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-white truncate">{workspace.name}</p>
                    <p className="text-xs text-zinc-400 capitalize">{workspace.type}</p>
                  </div>
                  {workspace.id === currentWorkspace.id && (
                    <CheckIcon className="h-4 w-4 text-[var(--color-primary)]" />
                  )}
                </button>
              ))}
            </div>
            
            {/* Add Workspace */}
            {onAddWorkspace && (
              <div className="p-2 border-t border-[var(--border-subtle)]">
                <button
                  onClick={() => {
                    setIsOpen(false)
                    onAddWorkspace()
                  }}
                  className="w-full flex items-center gap-3 p-2 rounded-lg transition-colors hover:bg-white/5 focus:outline-none focus:bg-white/5"
                >
                  <div className="w-7 h-7 rounded-lg border-2 border-dashed border-zinc-600 flex items-center justify-center">
                    <PlusIcon className="h-4 w-4 text-zinc-400" />
                  </div>
                  <span className="text-sm font-medium text-zinc-300">
                    Add Workspace
                  </span>
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export { GlassWorkspaceSwitcher }