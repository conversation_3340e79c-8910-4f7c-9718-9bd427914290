"use client"

import React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetDes<PERSON> } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"
import { XMarkIcon } from "@heroicons/react/24/outline"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface GlassSheetProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  description?: string
  children: React.ReactNode
  side?: "left" | "right" | "top" | "bottom"
  size?: "sm" | "md" | "lg" | "xl"
  blur?: "sm" | "md" | "lg"
  showCloseButton?: boolean
  className?: string
}

const GlassSheet: React.FC<GlassSheetProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  side = "right",
  size = "md",
  blur = "lg",
  showCloseButton = true,
  className
}) => {
  const sizeStyles = {
    sm: side === "left" || side === "right" ? "w-80" : "h-80",
    md: side === "left" || side === "right" ? "w-96" : "h-96", 
    lg: side === "left" || side === "right" ? "w-[32rem]" : "h-[32rem]",
    xl: side === "left" || side === "right" ? "w-[40rem]" : "h-[40rem]"
  }

  const blurStyles = {
    sm: "backdrop-blur-sm",
    md: "backdrop-blur-md",
    lg: "backdrop-blur-lg"
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent 
        side={side}
        className={cn(
          "bg-[var(--bg-glass)] border-[var(--border-subtle)]",
          blurStyles[blur],
          "shadow-2xl",
          sizeStyles[size],
          className
        )}
      >
        {/* Custom Close Button */}
        {showCloseButton && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4 rounded-full hover:bg-white/10 z-10"
            onClick={onClose}
          >
            <XMarkIcon className="h-4 w-4" />
          </Button>
        )}

        <SheetHeader className={showCloseButton ? "pr-12" : ""}>
          {title && (
            <SheetTitle className="text-xl font-semibold text-white">
              {title}
            </SheetTitle>
          )}
          {description && (
            <SheetDescription className="text-zinc-400">
              {description}
            </SheetDescription>
          )}
        </SheetHeader>

        <div className="mt-6 h-full overflow-auto custom-scrollbar">
          {children}
        </div>
      </SheetContent>
    </Sheet>
  )
}

export { GlassSheet }
