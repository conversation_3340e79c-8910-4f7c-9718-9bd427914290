"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { XMarkIcon, ArrowsPointingOutIcon, ArrowsPointingInIcon } from "@heroicons/react/24/outline"

interface FloatingPanelProps {
  id: string
  title: string
  subtitle?: string
  icon?: React.ComponentType<any>
  iconColor?: string
  position: { x: number; y: number }
  onClose: () => void
  onExpand?: () => void
  expanded?: boolean
  children: React.ReactNode
  width?: number
  height?: number
  className?: string
}

export default function FloatingPanel({
  id,
  title,
  subtitle,
  icon: Icon,
  iconColor,
  position,
  onClose,
  onExpand,
  expanded = false,
  children,
  width = 400,
  height,
  className
}: FloatingPanelProps) {
  return (
    <motion.div
      drag
      dragMomentum={false}
      dragElastic={0}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0, opacity: 0 }}
      className={cn(
        "fixed z-40 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-xl",
        expanded && "z-50",
        className
      )}
      style={{ 
        left: position.x, 
        top: position.y,
        width: expanded ? '80vw' : width,
        height: expanded ? '80vh' : height,
        maxWidth: expanded ? '1200px' : width,
        maxHeight: expanded ? '800px' : height
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-[var(--border-subtle)]">
        <div className="flex items-center gap-3">
          {Icon && (
            <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center", iconColor || "bg-white/10")}>
              <Icon className="h-5 w-5 text-white" />
            </div>
          )}
          <div>
            <h3 className="font-medium">{title}</h3>
            {subtitle && <p className="text-xs text-zinc-400">{subtitle}</p>}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {onExpand && (
            <button
              onClick={onExpand}
              className="p-1.5 hover:bg-white/10 rounded-lg transition-colors"
            >
              {expanded ? <ArrowsPointingInIcon className="h-4 w-4" /> : <ArrowsPointingOutIcon className="h-4 w-4" />}
            </button>
          )}
          <button
            onClick={onClose}
            className="p-1.5 hover:bg-white/10 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className={cn(
        "p-4",
        expanded ? "h-[calc(100%-60px)] overflow-y-auto custom-scrollbar" : ""
      )}>
        {children}
      </div>
    </motion.div>
  )
}