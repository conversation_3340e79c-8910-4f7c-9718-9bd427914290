"use client"

import React, { useState } from "react"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { 
  HomeIcon,
  UsersIcon,
  CalendarIcon,
  MusicalNoteIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  CheckIcon,
  FolderIcon,
  DocumentTextIcon,
  VideoCameraIcon,
  ChevronUpIcon,
  SparklesIcon,
  EnvelopeIcon,
  ChartBarIcon,
  BellIcon
} from "@heroicons/react/24/outline"

interface ProductStat {
  label: string
  value: string | number
  trend?: "up" | "down" | "neutral"
  trendValue?: string
}

interface Product {
  id: string
  name: string
  description: string
  href: string
  icon: React.ComponentType<any>
  color: string
  bgGradient: string
  stats: ProductStat[]
  quickActions?: Array<{
    label: string
    action: () => void
    icon?: React.ComponentType<any>
  }>
  isNew?: boolean
  badge?: string
}

const products: Product[] = [
  {
    id: "dashboard",
    name: "Dashboard",
    description: "Overview & insights",
    href: "/",
    icon: HomeIcon,
    color: "blue",
    bgGradient: "from-blue-500/20 to-blue-600/20",
    stats: [
      { label: "Active Members", value: "1,234", trend: "up", trendValue: "+12%" },
      { label: "This Week", value: "89%", trend: "neutral", trendValue: "attendance" }
    ]
  },
  {
    id: "people",
    name: "People",
    description: "Member management",
    href: "/people",
    icon: UsersIcon,
    color: "green", 
    bgGradient: "from-green-500/20 to-green-600/20",
    stats: [
      { label: "Total Members", value: "1,234" },
      { label: "New This Month", value: "47", trend: "up", trendValue: "+23%" }
    ],
    badge: "12 new"
  },
  {
    id: "services",
    name: "Services",
    description: "Worship planning",
    href: "/services",
    icon: MusicalNoteIcon,
    color: "purple",
    bgGradient: "from-purple-500/20 to-purple-600/20",
    stats: [
      { label: "This Sunday", value: "Ready" },
      { label: "Team Members", value: "15/18" }
    ]
  },
  {
    id: "events",
    name: "Events",
    description: "Calendar & scheduling",
    href: "/events",
    icon: CalendarIcon,
    color: "amber",
    bgGradient: "from-amber-500/20 to-amber-600/20",
    stats: [
      { label: "This Month", value: "24" },
      { label: "Registrations", value: "342", trend: "up", trendValue: "+15%" }
    ]
  },
  {
    id: "groups",
    name: "Groups",
    description: "Small groups & classes",
    href: "/groups", 
    icon: UserGroupIcon,
    color: "indigo",
    bgGradient: "from-indigo-500/20 to-indigo-600/20",
    stats: [
      { label: "Active Groups", value: "56" },
      { label: "Participation", value: "78%" }
    ]
  },
  {
    id: "giving",
    name: "Giving",
    description: "Donations & finance",
    href: "/giving",
    icon: CurrencyDollarIcon,
    color: "emerald",
    bgGradient: "from-emerald-500/20 to-emerald-600/20",
    stats: [
      { label: "This Month", value: "$45.2K", trend: "up", trendValue: "+8%" },
      { label: "YTD", value: "$523K" }
    ]
  },
  {
    id: "check-in", 
    name: "Check-in",
    description: "Attendance tracking",
    href: "/check-in",
    icon: CheckIcon,
    color: "orange",
    bgGradient: "from-orange-500/20 to-orange-600/20",
    stats: [
      { label: "Today", value: "0" },
      { label: "Last Sunday", value: "423" }
    ]
  },
  {
    id: "ministries",
    name: "Ministries",
    description: "Ministry management",
    href: "/ministries",
    icon: FolderIcon,
    color: "pink",
    bgGradient: "from-pink-500/20 to-pink-600/20",
    stats: [
      { label: "Active", value: "12" },
      { label: "Volunteers", value: "234" }
    ]
  },
  {
    id: "messaging",
    name: "Messaging",
    description: "Communication hub",
    href: "/messaging",
    icon: EnvelopeIcon,
    color: "cyan",
    bgGradient: "from-cyan-500/20 to-cyan-600/20",
    stats: [
      { label: "Sent Today", value: "45" },
      { label: "Open Rate", value: "67%" }
    ]
  }
]

// Group products by category
const productCategories = [
  {
    name: "Core",
    products: ["dashboard", "people", "services", "events"]
  },
  {
    name: "Community", 
    products: ["groups", "check-in", "ministries"]
  },
  {
    name: "Operations",
    products: ["giving", "messaging"]
  }
]

interface EnhancedProductSwitcherProps {
  className?: string
  isCollapsed?: boolean
}

export default function EnhancedProductSwitcher({ className, isCollapsed }: EnhancedProductSwitcherProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [isExpanded, setIsExpanded] = useState(false)
  const [hoveredProduct, setHoveredProduct] = useState<string | null>(null)
  
  const currentProduct = products.find(p => 
    pathname === p.href || (p.href !== "/" && pathname.startsWith(p.href))
  ) || products[0]

  if (isCollapsed) {
    return null // Hide when sidebar is collapsed
  }

  return (
    <div className={cn("border-t border-[var(--border-subtle)]", className)}>
      {/* Current Product Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-3 flex items-center justify-between hover:bg-white/5 transition-colors"
      >
        <div className="flex items-center gap-3">
          <div className={cn(
            "w-9 h-9 rounded-lg flex items-center justify-center",
            "bg-gradient-to-br", currentProduct.bgGradient
          )}>
            <currentProduct.icon className="h-5 w-5" />
          </div>
          <div className="text-left">
            <p className="text-sm font-medium">{currentProduct.name}</p>
            <p className="text-xs text-zinc-500">{currentProduct.description}</p>
          </div>
        </div>
        <ChevronUpIcon className={cn(
          "h-4 w-4 text-zinc-400 transition-transform",
          isExpanded && "rotate-180"
        )} />
      </button>

      {/* Expanded Product Grid */}
      <AnimatePresence>
        {isExpanded && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-40" 
              onClick={() => setIsExpanded(false)}
            />
            
            {/* Product Panel */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.2 }}
              className="absolute bottom-full mb-2 left-2 right-2 z-50"
            >
              <div className="bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)] rounded-xl shadow-glow-xl p-4 max-h-[600px] overflow-y-auto custom-scrollbar">
                {/* Search */}
                <div className="mb-4">
                  <input
                    type="text"
                    placeholder="Search products..."
                    className="w-full px-3 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                  />
                </div>

                {/* Product Categories */}
                {productCategories.map((category) => (
                  <div key={category.name} className="mb-6 last:mb-0">
                    <h3 className="text-xs font-medium text-zinc-400 uppercase tracking-wider mb-3">
                      {category.name}
                    </h3>
                    <div className="grid grid-cols-2 gap-2">
                      {category.products.map((productId) => {
                        const product = products.find(p => p.id === productId)
                        if (!product) return null
                        
                        const isActive = pathname === product.href || 
                          (product.href !== "/" && pathname.startsWith(product.href))
                        
                        return (
                          <motion.button
                            key={product.id}
                            onClick={() => {
                              router.push(product.href)
                              setIsExpanded(false)
                            }}
                            onMouseEnter={() => setHoveredProduct(product.id)}
                            onMouseLeave={() => setHoveredProduct(null)}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className={cn(
                              "relative p-3 rounded-xl border transition-all duration-200 text-left",
                              isActive 
                                ? "bg-gradient-to-br " + product.bgGradient + " border-[var(--color-primary)]/30"
                                : "bg-white/5 border-[var(--border-subtle)] hover:bg-white/10"
                            )}
                          >
                            {/* New Badge */}
                            {product.isNew && (
                              <div className="absolute -top-1 -right-1 px-1.5 py-0.5 bg-[var(--color-primary)] rounded-full">
                                <span className="text-[10px] font-medium text-black">NEW</span>
                              </div>
                            )}

                            {/* Badge */}
                            {product.badge && (
                              <div className="absolute -top-1 -right-1 px-2 py-0.5 bg-orange-500 rounded-full">
                                <span className="text-[10px] font-medium text-black">{product.badge}</span>
                              </div>
                            )}

                            <div className="flex items-start gap-3">
                              <div className={cn(
                                "w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0",
                                "bg-gradient-to-br", product.bgGradient
                              )}>
                                <product.icon className="h-5 w-5" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium mb-0.5">{product.name}</p>
                                <p className="text-xs text-zinc-500 mb-2">{product.description}</p>
                                
                                {/* Quick Stats */}
                                <div className="space-y-1">
                                  {product.stats.slice(0, hoveredProduct === product.id ? 2 : 1).map((stat, idx) => (
                                    <div key={idx} className="flex items-center justify-between text-xs">
                                      <span className="text-zinc-400">{stat.label}</span>
                                      <div className="flex items-center gap-1">
                                        <span className="font-medium">{stat.value}</span>
                                        {stat.trend && stat.trendValue && (
                                          <span className={cn(
                                            "text-[10px]",
                                            stat.trend === "up" && "text-green-400",
                                            stat.trend === "down" && "text-red-400",
                                            stat.trend === "neutral" && "text-zinc-400"
                                          )}>
                                            {stat.trendValue}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>

                            {/* Hover Quick Actions */}
                            <AnimatePresence>
                              {hoveredProduct === product.id && product.quickActions && (
                                <motion.div
                                  initial={{ opacity: 0, height: 0 }}
                                  animate={{ opacity: 1, height: "auto" }}
                                  exit={{ opacity: 0, height: 0 }}
                                  className="mt-2 pt-2 border-t border-[var(--border-subtle)]"
                                >
                                  <div className="flex gap-1">
                                    {product.quickActions.map((action, idx) => (
                                      <button
                                        key={idx}
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          action.action()
                                        }}
                                        className="flex-1 px-2 py-1 text-[10px] bg-white/5 hover:bg-white/10 rounded transition-colors"
                                      >
                                        {action.label}
                                      </button>
                                    ))}
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </motion.button>
                        )
                      })}
                    </div>
                  </div>
                ))}

                {/* Quick Stats Summary */}
                <div className="mt-6 p-3 bg-white/5 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-zinc-400">System Overview</span>
                    <BellIcon className="h-3.5 w-3.5 text-zinc-400" />
                  </div>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div>
                      <p className="text-lg font-semibold">89%</p>
                      <p className="text-[10px] text-zinc-500">Capacity</p>
                    </div>
                    <div>
                      <p className="text-lg font-semibold">24</p>
                      <p className="text-[10px] text-zinc-500">Active Tasks</p>
                    </div>
                    <div>
                      <p className="text-lg font-semibold">1.2K</p>
                      <p className="text-[10px] text-zinc-500">Members</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}

export { EnhancedProductSwitcher }