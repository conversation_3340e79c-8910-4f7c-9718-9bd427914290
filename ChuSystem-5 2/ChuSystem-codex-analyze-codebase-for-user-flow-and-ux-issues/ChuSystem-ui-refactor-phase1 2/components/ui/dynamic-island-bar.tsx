"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence, MotionConfig } from "framer-motion"
import { cn } from "@/lib/utils"
import { ActivityIcon, XMarkIcon } from "@heroicons/react/16/solid"

interface QuickAction {
  id: string
  icon: React.ReactNode
  label: string
  onClick: () => void
  color?: string
  badge?: string | number
}

interface DynamicIslandBarProps {
  actions?: QuickAction[]
  selectedCount?: number
  showActivity?: boolean
  activityText?: string
  className?: string
  // Product-specific content
  productType?: 'events' | 'people' | 'services' | 'giving' | 'groups' | 'check-in'
  quickPreview?: {
    title: string
    stats?: Array<{ label: string; value: string | number }>
    recentItems?: Array<{ name: string; time: string }>
  }
}

const TRANSITION = {
  type: 'spring' as const,
  bounce: 0.1,
  duration: 0.4,
}

export default function DynamicIslandBar({
  actions = [],
  selectedCount = 0,
  showActivity = false,
  activityText = "Processing...",
  className,
  productType = 'events',
  quickPreview,
}: DynamicIslandBarProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const uniqueId = useRef(`island-${Date.now()}`).current
  const containerRef = useRef<HTMLDivElement>(null)

  // Only show different content based on state, don't auto-expand
  const getCompactContent = () => {
    if (showActivity) return "Active"
    if (selectedCount > 0) return `${selectedCount}`
    return "Actions"
  }

  // Click outside to collapse
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        setIsExpanded(false)
      }
    }

    document.addEventListener('click', handleClick)
    return () => document.removeEventListener('click', handleClick)
  }, [])

  if (actions.length === 0 && selectedCount === 0 && !showActivity) {
    return null
  }

  const handleToggle = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <MotionConfig transition={TRANSITION}>
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-[100]" ref={containerRef}>
        <motion.div
          layoutId={`island-${uniqueId}`}
          className={cn(
            "bg-black/80 backdrop-blur-xl border border-white/10 shadow-2xl overflow-hidden cursor-pointer",
            className
          )}
          animate={{
            borderRadius: isExpanded ? 20 : 28,
            width: isExpanded ? 280 : 90,
            height: isExpanded ? 'auto' : 36,
          }}
          onClick={!isExpanded ? handleToggle : undefined}
          whileHover={!isExpanded ? { scale: 1.05 } : {}}
          whileTap={!isExpanded ? { scale: 0.95 } : {}}
        >
          <AnimatePresence mode="wait">
            {!isExpanded ? (
              <motion.div
                key="compact"
                layoutId={`content-${uniqueId}`}
                className="flex items-center justify-center h-full px-4 gap-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {showActivity && (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <ActivityIcon className="h-3 w-3 text-blue-400" />
                  </motion.div>
                )}
                <span className="text-white text-xs font-medium">
                  {getCompactContent()}
                </span>
              </motion.div>
            ) : (
              <motion.div
                key="expanded"
                layoutId={`content-${uniqueId}`}
                className="p-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {showActivity && (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <ActivityIcon className="h-4 w-4 text-blue-400" />
                      </motion.div>
                    )}
                    <span className="text-white text-sm font-medium">
                      {showActivity 
                        ? activityText 
                        : selectedCount > 0 
                          ? `${selectedCount} selected` 
                          : "Quick Actions"
                      }
                    </span>
                  </div>
                  <button
                    onClick={() => setIsExpanded(false)}
                    className="p-1 rounded-full hover:bg-white/10 transition-colors"
                  >
                    <XMarkIcon className="h-3 w-3 text-white/60" />
                  </button>
                </div>

                {/* Activity Bar */}
                {showActivity && (
                  <div className="mb-3">
                    <div className="w-full bg-white/10 rounded-full h-1">
                      <motion.div
                        className="bg-blue-400 h-1 rounded-full"
                        initial={{ width: "0%" }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 2, ease: "easeInOut" }}
                      />
                    </div>
                  </div>
                )}

                {/* Product-specific Quick Preview */}
                {quickPreview && (
                  <motion.div
                    className="mb-3 space-y-2"
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <div className="text-center">
                      <div className="text-white/60 text-xs mb-1">{quickPreview.title}</div>
                      {quickPreview.stats && (
                        <div className="flex justify-center gap-4">
                          {quickPreview.stats.slice(0, 3).map((stat, i) => (
                            <div key={i} className="text-center">
                              <div className="text-white text-sm font-medium">{stat.value}</div>
                              <div className="text-white/40 text-xs">{stat.label}</div>
                            </div>
                          ))}
                        </div>
                      )}
                      {quickPreview.recentItems && (
                        <div className="mt-2 space-y-1">
                          {quickPreview.recentItems.slice(0, 2).map((item, i) => (
                            <div key={i} className="flex justify-between text-xs">
                              <span className="text-white/60 truncate">{item.name}</span>
                              <span className="text-white/40">{item.time}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}

                {/* Actions - More compact */}
                {actions.length > 0 && (
                  <div className="flex gap-1.5 justify-center">
                    {actions.slice(0, 5).map((action, index) => (
                      <motion.button
                        key={action.id}
                        className={cn(
                          "relative w-10 h-10 rounded-lg transition-colors",
                          "bg-white/5 hover:bg-white/10 border border-white/5"
                        )}
                        onClick={(e) => {
                          e.stopPropagation()
                          action.onClick()
                        }}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.1 + index * 0.03 }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <div className="text-white/70">
                          {React.cloneElement(action.icon as React.ReactElement, {
                            className: "h-3.5 w-3.5 mx-auto"
                          })}
                        </div>
                        {action.badge && (
                          <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-[8px] font-bold">{action.badge}</span>
                          </div>
                        )}
                      </motion.button>
                    ))}
                  </div>
                )}

                {/* Selection Preview */}
                {selectedCount > 0 && (
                  <motion.div
                    className="mt-3 flex items-center justify-center gap-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <div className="flex -space-x-1">
                      {[...Array(Math.min(selectedCount, 4))].map((_, i) => (
                        <div
                          key={i}
                          className="w-4 h-4 bg-blue-500 border border-white/20 rounded-full"
                        />
                      ))}
                      {selectedCount > 4 && (
                        <div className="w-4 h-4 bg-white/20 border border-white/20 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">+</span>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </MotionConfig>
  )
}

export { DynamicIslandBar }