"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface GlassInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: "default" | "glass" | "compact"
  inputSize?: "sm" | "md" | "lg"
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  error?: boolean
  helperText?: string
}

const GlassInput = React.forwardRef<HTMLInputElement, GlassInputProps>(
  ({ 
    className, 
    type, 
    variant = "glass",
    inputSize = "md",
    leftIcon,
    rightIcon,
    error = false,
    helperText,
    ...props 
  }, ref) => {
    
    const variants = {
      default: "bg-[var(--bg-card)] border border-[var(--border-subtle)]",
      glass: "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] shadow-glass",
      compact: "bg-[var(--bg-glass)] border border-[var(--border-subtle)]"
    }

    const sizes = {
      sm: "h-9 px-3 text-sm",
      md: "h-11 px-4 text-base", 
      lg: "h-13 px-5 text-lg"
    }

    const inputClasses = cn(
      "flex w-full rounded-xl transition-all duration-200",
      "placeholder:text-zinc-500 text-white",
      "focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20 focus:border-[var(--color-primary)]/30",
      "hover:bg-[var(--bg-glass-hover)] hover:border-[var(--border-light)]",
      "disabled:cursor-not-allowed disabled:opacity-50",
      variants[variant],
      sizes[inputSize],
      error && "border-red-500/50 focus:border-red-500/70 focus:ring-red-500/20",
      leftIcon && "pl-11",
      rightIcon && "pr-11",
      className
    )

    return (
      <div className="w-full">
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-zinc-400">
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            className={inputClasses}
            ref={ref}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-zinc-400">
              {rightIcon}
            </div>
          )}
        </div>
        
        {helperText && (
          <p className={cn(
            "mt-2 text-sm",
            error ? "text-red-400" : "text-zinc-400"
          )}>
            {helperText}
          </p>
        )}
      </div>
    )
  }
)

GlassInput.displayName = "GlassInput"

export { GlassInput }
export default GlassInput