"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import * as ContextMenuPrimitive from "@/components/ui/context-menu" // Use aliased import

// Re-exporting the necessary parts from shadcn/ui's context menu
// This makes design-system/context-menu.tsx a thin wrapper or direct re-export.

const ContextMenuTrigger = ContextMenuPrimitive.ContextMenuTrigger
const ContextMenuGroup = ContextMenuPrimitive.ContextMenuGroup
const ContextMenuPortal = ContextMenuPrimitive.ContextMenuPortal
const ContextMenuSub = ContextMenuPrimitive.ContextMenuSub
const ContextMenuRadioGroup = ContextMenuPrimitive.ContextMenuRadioGroup

const ContextMenu = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenu>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenu>
>(({ ...props }, ref) => <ContextMenuPrimitive.ContextMenu ref={ref} {...props} />)
ContextMenu.displayName = ContextMenuPrimitive.ContextMenu.displayName

const ContextMenuContent = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenuContent>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenuContent>
>(({ className, ...props }, ref) => (
  <ContextMenuPrimitive.ContextMenuContent
    ref={ref}
    className={cn(
      "z-50 min-w-[12rem] overflow-hidden rounded-xl border border-[var(--border-subtle)] bg-[var(--bg-glass)] p-1.5 text-white shadow-xl backdrop-blur-xl",
      // Animations (optional, can be customized or use shadcn's default)
      "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
      "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className,
    )}
    {...props}
  />
))
ContextMenuContent.displayName = ContextMenuPrimitive.ContextMenuContent.displayName

const ContextMenuItem = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenuItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenuItem> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <ContextMenuPrimitive.ContextMenuItem
    ref={ref}
    className={cn(
      "relative flex cursor-pointer select-none items-center gap-2 rounded-lg px-3 py-2 text-sm outline-none transition-colors",
      "hover:bg-[var(--bg-glass-hover)] focus:bg-[var(--bg-glass-hover)]",
      "data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      inset && "pl-8",
      className,
    )}
    {...props}
  />
))
ContextMenuItem.displayName = ContextMenuPrimitive.ContextMenuItem.displayName

const ContextMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenuCheckboxItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenuCheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <ContextMenuPrimitive.ContextMenuCheckboxItem
    ref={ref}
    className={cn(
      "relative flex cursor-pointer select-none items-center rounded-lg py-2 pl-8 pr-3 text-sm outline-none transition-colors",
      "hover:bg-[var(--bg-glass-hover)] focus:bg-[var(--bg-glass-hover)]",
      "data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className,
    )}
    checked={checked}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <ContextMenuPrimitive.ContextMenuPrimitiveItemIndicator>
        <ContextMenuPrimitive.Check className="h-4 w-4 text-[var(--color-primary)]" />
      </ContextMenuPrimitive.ContextMenuPrimitiveItemIndicator>
    </span>
    {children}
  </ContextMenuPrimitive.ContextMenuCheckboxItem>
))
ContextMenuCheckboxItem.displayName = ContextMenuPrimitive.ContextMenuCheckboxItem.displayName

const ContextMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenuRadioItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenuRadioItem>
>(({ className, children, ...props }, ref) => (
  <ContextMenuPrimitive.ContextMenuRadioItem
    ref={ref}
    className={cn(
      "relative flex cursor-pointer select-none items-center rounded-lg py-2 pl-8 pr-3 text-sm outline-none transition-colors",
      "hover:bg-[var(--bg-glass-hover)] focus:bg-[var(--bg-glass-hover)]",
      "data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className,
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <ContextMenuPrimitive.ContextMenuPrimitiveItemIndicator>
        <ContextMenuPrimitive.Circle className="h-2 w-2 fill-current text-[var(--color-primary)]" />
      </ContextMenuPrimitive.ContextMenuPrimitiveItemIndicator>
    </span>
    {children}
  </ContextMenuPrimitive.ContextMenuRadioItem>
))
ContextMenuRadioItem.displayName = ContextMenuPrimitive.ContextMenuRadioItem.displayName

const ContextMenuLabel = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenuLabel>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenuLabel> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <ContextMenuPrimitive.ContextMenuLabel
    ref={ref}
    className={cn("px-3 py-1.5 text-xs font-semibold text-zinc-400", inset && "pl-8", className)}
    {...props}
  />
))
ContextMenuLabel.displayName = ContextMenuPrimitive.ContextMenuLabel.displayName

const ContextMenuSeparator = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenuSeparator>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenuSeparator>
>(({ className, ...props }, ref) => (
  <ContextMenuPrimitive.ContextMenuSeparator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-[var(--border-subtle)]", className)}
    {...props}
  />
))
ContextMenuSeparator.displayName = ContextMenuPrimitive.ContextMenuSeparator.displayName

const ContextMenuShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn("ml-auto text-xs tracking-widest text-zinc-500", className)}
      {...props}
    />
  )
}
ContextMenuShortcut.displayName = "ContextMenuShortcut"

const ContextMenuSubTrigger = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenuSubTrigger>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenuSubTrigger> & {
    inset?: boolean
  }
>(({ className, inset, children, ...props }, ref) => (
  <ContextMenuPrimitive.ContextMenuSubTrigger
    ref={ref}
    className={cn(
      "flex cursor-pointer select-none items-center gap-2 rounded-lg px-3 py-2 text-sm outline-none transition-colors",
      "hover:bg-[var(--bg-glass-hover)] focus:bg-[var(--bg-glass-hover)] data-[state=open]:bg-[var(--bg-glass-hover)]",
      inset && "pl-8",
      className,
    )}
    {...props}
  >
    {children}
    <ContextMenuPrimitive.ChevronRight className="ml-auto h-4 w-4 text-zinc-400" />
  </ContextMenuPrimitive.ContextMenuSubTrigger>
))
ContextMenuSubTrigger.displayName = ContextMenuPrimitive.ContextMenuSubTrigger.displayName

const ContextMenuSubContent = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.ContextMenuSubContent>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.ContextMenuSubContent>
>(({ className, ...props }, ref) => (
  <ContextMenuPrimitive.ContextMenuSubContent
    ref={ref}
    className={cn(
      "z-50 min-w-[12rem] overflow-hidden rounded-xl border border-[var(--border-subtle)] bg-[var(--bg-glass)] p-1.5 text-white shadow-xl backdrop-blur-xl",
      // Animations
      "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
      "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className,
    )}
    {...props}
  />
))
ContextMenuSubContent.displayName = ContextMenuPrimitive.ContextMenuSubContent.displayName

export {
  ContextMenu,
  ContextMenuTrigger,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuCheckboxItem,
  ContextMenuRadioItem,
  ContextMenuLabel,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuGroup,
  ContextMenuPortal,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuRadioGroup,
}

// Default export for convenience
export default ContextMenuPrimitive.ContextMenu
