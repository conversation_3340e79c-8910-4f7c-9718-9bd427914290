"use client"

import React, { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence, MotionConfig } from "framer-motion" 
import Link from "next/link"
import { EllipsisHorizontalIcon, XMarkIcon, ActivityIcon } from "@heroicons/react/16/solid"
import { Button } from "@/components/ui/button"
import GlassTooltip from "./glass-tooltip"
import { cn } from "@/lib/utils"

interface QuickAction {
  id: string
  icon: React.ReactNode
  label: string
  onClick: () => void
  color?: string
  href?: string
  shortcut?: string
  expandedInfo?: {
    title?: string
    description?: string
    stats?: Array<{
      label: string
      value: string | number
    }>
    recentActivity?: Array<{
      text: string
      time: string
    }>
  }
  badge?: string | number
}

interface BottomActionBarProps {
  actions?: QuickAction[]
  selectedCount?: number
  maxVisibleActions?: number
  className?: string
  showActivity?: boolean
  activityText?: string
  // Compact expandable mode
  compactMode?: boolean
  productPreview?: {
    title: string
    stats: Array<{ label: string; value: string | number }>
  }
}

const TRANSITION = {
  type: 'spring' as const,
  bounce: 0.05,
  duration: 0.3,
}

export default function BottomActionBar({
  actions = [],
  selectedCount = 0,
  maxVisibleActions = 5,
  className,
  showActivity = false,
  activityText = "Processing...",
  compactMode = false,
  productPreview,
}: BottomActionBarProps) {
  const [isActionsExpanded, setIsActionsExpanded] = useState(false)
  const [isCompactExpanded, setIsCompactExpanded] = useState(false)
  const uniqueId = useRef(`bar-${Math.random().toString(36).substr(2, 9)}`).current
  const containerRef = useRef<HTMLDivElement>(null)

  // Click outside to collapse compact mode
  useEffect(() => {
    if (!compactMode) return
    
    const handleClick = (e: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        setIsCompactExpanded(false)
      }
    }

    document.addEventListener('click', handleClick)
    return () => document.removeEventListener('click', handleClick)
  }, [compactMode])

  const visibleActions = isActionsExpanded ? actions : actions.slice(0, maxVisibleActions)
  const hiddenActionCount = actions.length > maxVisibleActions && !isActionsExpanded ? actions.length - maxVisibleActions : 0

  // Traditional bottom bar (existing functionality)
  if (!compactMode) {
    if (actions.length === 0 && selectedCount === 0) {
      return null
    }

    return (
      <div
        className={cn(
          "fixed bottom-4 left-1/2 transform -translate-x-1/2 z-[100] px-3 py-2 flex items-center gap-3",
          "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] shadow-2xl",
          "rounded-2xl",
          className,
        )}
      >
        <AnimatePresence mode="wait" initial={false}>
          {selectedCount > 0 && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="text-sm text-zinc-300 px-3 whitespace-nowrap"
            >
              {selectedCount} item{selectedCount !== 1 ? "s" : ""} selected
            </motion.div>
          )}
        </AnimatePresence>
        
        <motion.div
          layout
          transition={{ type: "spring", damping: 20, stiffness: 200 }}
          className="flex items-center gap-1.5"
        >
          <AnimatePresence mode="popLayout">
            {visibleActions.map((action) => (
              <motion.div
                key={action.id}
                layout
                initial={{ opacity: 0, scale: 0.7 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.7, transition: { duration: 0.15 } }}
                transition={{ type: "spring", damping: 15, stiffness: 200 }}
              >
                <ActionButton action={action} />
              </motion.div>
            ))}
          </AnimatePresence>

          {hiddenActionCount > 0 && (
            <motion.div layout initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
              <GlassTooltip content={`${hiddenActionCount} more actions`} side="top">
                <Button
                  variant="icon"
                  size="icon"
                  className="icon-button rounded-xl"
                  onClick={() => setIsActionsExpanded(true)}
                >
                  <EllipsisHorizontalIcon className="h-5 w-5 text-zinc-300" />
                </Button>
              </GlassTooltip>
            </motion.div>
          )}

          {isActionsExpanded && (
             <motion.div layout initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
              <GlassTooltip content="Collapse actions" side="top">
                <Button
                  variant="icon"
                  size="icon"
                  className="icon-button rounded-xl bg-[rgba(var(--color-primary),0.1)] hover:bg-[rgba(var(--color-primary),0.2)]"
                  onClick={() => setIsActionsExpanded(false)}
                >
                  <XMarkIcon className="h-5 w-5 text-yellow-400" />
                </Button>
              </GlassTooltip>
            </motion.div>
          )}
        </motion.div>
      </div>
    )
  }

  // Compact expandable mode
  const getCompactContent = () => {
    if (showActivity) return { text: "Active", icon: true }
    if (selectedCount > 0) return { text: `${selectedCount}`, icon: false }
    return { text: "Quick", icon: false }
  }

  const compactContent = getCompactContent()

  return (
    <MotionConfig transition={TRANSITION}>
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-[100]" ref={containerRef}>
        <motion.div
          layoutId={`compact-bar-${uniqueId}`}
          className={cn(
            "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] cursor-pointer overflow-hidden shadow-glow-lg",
            className
          )}
          animate={{
            borderRadius: isCompactExpanded ? 20 : 25,
            width: isCompactExpanded ? 360 : 120,
            height: isCompactExpanded ? 'auto' : 50,
          }}
          onClick={!isCompactExpanded ? () => setIsCompactExpanded(true) : undefined}
          whileHover={!isCompactExpanded ? { scale: 1.02 } : {}}
          whileTap={!isCompactExpanded ? { scale: 0.98 } : {}}
        >
          <AnimatePresence mode="wait">
            {!isCompactExpanded ? (
              <motion.div
                key="compact"
                layoutId={`content-${uniqueId}`}
                className="flex items-center justify-center h-full gap-2 px-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {compactContent.icon && showActivity && (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <ActivityIcon className="h-3 w-3 text-[var(--color-primary)]" />
                  </motion.div>
                )}
                <span className="text-zinc-300 text-sm font-medium">
                  {compactContent.text}
                </span>
              </motion.div>
            ) : (
              <motion.div
                key="expanded"
                layoutId={`content-${uniqueId}`}
                className="p-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ delay: 0.1 }}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {showActivity && (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <ActivityIcon className="h-4 w-4 text-[var(--color-primary)]" />
                      </motion.div>
                    )}
                    <span className="text-white text-sm font-medium">
                      {showActivity ? activityText : selectedCount > 0 ? `${selectedCount} selected` : productPreview?.title || "Quick Actions"}
                    </span>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      setIsCompactExpanded(false)
                    }}
                    className="p-1 rounded-full hover:bg-[var(--bg-glass-hover)] transition-colors"
                  >
                    <XMarkIcon className="h-3 w-3 text-zinc-400" />
                  </button>
                </div>

                {/* Activity Progress */}
                {showActivity && (
                  <div className="mb-3">
                    <div className="w-full bg-[var(--bg-glass-hover)] rounded-full h-1">
                      <motion.div
                        className="bg-[var(--color-primary)] h-1 rounded-full"
                        initial={{ width: "0%" }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 2, ease: "easeInOut" }}
                      />
                    </div>
                  </div>
                )}

                {/* Product Preview Stats */}
                {productPreview && !showActivity && selectedCount === 0 && (
                  <div className="mb-3 text-center">
                    <div className="flex justify-center gap-6">
                      {productPreview.stats.map((stat, i) => (
                        <div key={i} className="text-center">
                          <div className="text-white text-lg font-bold">{stat.value}</div>
                          <div className="text-zinc-400 text-xs">{stat.label}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Selection Preview */}
                {selectedCount > 0 && (
                  <div className="mb-3 flex items-center justify-center gap-2">
                    <div className="flex -space-x-1">
                      {[...Array(Math.min(selectedCount, 4))].map((_, i) => (
                        <div
                          key={i}
                          className="w-5 h-5 bg-[var(--color-primary)] border-2 border-[var(--bg-glass)] rounded-full"
                        />
                      ))}
                      {selectedCount > 4 && (
                        <div className="w-5 h-5 bg-zinc-600 border-2 border-[var(--bg-glass)] rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">+</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Quick Actions */}
                {actions.length > 0 && (
                  <div className="flex gap-2 justify-center">
                    {actions.slice(0, 6).map((action, index) => (
                      <motion.button
                        key={action.id}
                        className="relative w-11 h-11 bg-[var(--bg-glass-hover)] hover:bg-[var(--bg-glass)] rounded-xl flex items-center justify-center transition-colors border border-[var(--border-subtle)]"
                        onClick={(e) => {
                          e.stopPropagation()
                          action.onClick()
                        }}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 + index * 0.03 }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <div className="text-zinc-300">
                          {React.cloneElement(action.icon as React.ReactElement, {
                            className: "h-4 w-4"
                          })}
                        </div>
                        {action.badge && (
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-[var(--color-primary)] rounded-full flex items-center justify-center">
                            <span className="text-black text-xs font-bold">{action.badge}</span>
                          </div>
                        )}
                      </motion.button>
                    ))}
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </MotionConfig>
  )
}

function ActionButton({ action }: { action: QuickAction }) {
  const [showExpandedInfo, setShowExpandedInfo] = useState(false)

  const handleClick = () => {
    if (action.expandedInfo) {
      setShowExpandedInfo(!showExpandedInfo)
    }
    action.onClick()
  }

  const buttonContent = (
    <>
      <div className="relative">
        {React.cloneElement(action.icon as React.ReactElement, { className: "h-5 w-5 text-zinc-300" })}
        
        {action.badge && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-[var(--color-primary)] rounded-full flex items-center justify-center">
            <span className="text-[10px] font-medium text-black">{action.badge}</span>
          </div>
        )}
      </div>
      
      {action.shortcut && (
        <span
          className={cn(
            "absolute -top-1.5 -right-1.5 text-[10px] py-0.5 px-1 rounded",
            "bg-[var(--bg-card)] border border-[var(--border-subtle)] text-zinc-300",
            "opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none",
          )}
        >
          {action.shortcut}
        </span>
      )}
    </>
  )

  const commonButtonProps = {
    variant: "icon" as const,
    size: "icon" as const,
    className: "w-10 h-10 rounded-xl bg-white/5 hover:bg-white/10 transition-all group relative border border-white/5 hover:border-white/10",
    onClick: handleClick,
  }

  return (
    <div className="relative">
      <GlassTooltip content={action.label} side="top">
        {action.href ? (
          <Link href={action.href} passHref>
            <Button {...commonButtonProps} asChild>
              <a>{buttonContent}</a>
            </Button>
          </Link>
        ) : (
          <Button {...commonButtonProps}>{buttonContent}</Button>
        )}
      </GlassTooltip>

      {/* Expandable Info Panel */}
      <AnimatePresence>
        {showExpandedInfo && action.expandedInfo && (
          <>
            <div 
              className="fixed inset-0 z-40" 
              onClick={() => setShowExpandedInfo(false)}
            />
            
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.15, ease: "easeOut" }}
              className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 z-50"
            >
              <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-lg p-4 min-w-64 max-w-80">
                {action.expandedInfo.title && (
                  <div className="text-sm font-semibold text-white mb-2">
                    {action.expandedInfo.title}
                  </div>
                )}
                
                {action.expandedInfo.description && (
                  <div className="text-sm text-zinc-300 mb-3">
                    {action.expandedInfo.description}
                  </div>
                )}
                
                {action.expandedInfo.stats && action.expandedInfo.stats.length > 0 && (
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    {action.expandedInfo.stats.map((stat, index) => (
                      <div key={index} className="bg-[var(--bg-glass-hover)] rounded-lg p-2">
                        <div className="text-xs text-zinc-400">{stat.label}</div>
                        <div className="text-sm font-medium text-white">{stat.value}</div>
                      </div>
                    ))}
                  </div>
                )}
                
                {action.expandedInfo.recentActivity && action.expandedInfo.recentActivity.length > 0 && (
                  <div className="border-t border-[var(--border-subtle)] pt-3">
                    <div className="text-xs font-medium text-zinc-400 mb-2">Recent Activity</div>
                    <div className="space-y-2">
                      {action.expandedInfo.recentActivity.map((activity, index) => (
                        <div key={index} className="flex justify-between items-start text-xs">
                          <span className="text-zinc-300 flex-1">{activity.text}</span>
                          <span className="text-zinc-500 ml-2">{activity.time}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 -translate-y-1">
                <div className="w-2 h-2 bg-[var(--bg-glass)] border-r border-b border-[var(--border-subtle)] transform rotate-45" />
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}