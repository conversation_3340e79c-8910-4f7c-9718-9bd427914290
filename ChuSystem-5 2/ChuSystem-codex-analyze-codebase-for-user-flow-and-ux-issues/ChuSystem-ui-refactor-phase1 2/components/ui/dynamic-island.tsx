"use client"

import { useState, useRef } from "react"
import { motion, AnimatePresence, MotionConfig } from "framer-motion"
import { cn } from "@/lib/utils"

interface DynamicIslandProps {
  children?: React.ReactNode
  className?: string
}

const TRANSITION = {
  type: 'spring' as const,
  bounce: 0.05,
  duration: 0.3,
}

export default function DynamicIsland({ children, className }: DynamicIslandProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const uniqueId = useRef(`island-${Date.now()}`).current

  return (
    <MotionConfig transition={TRANSITION}>
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
        <motion.div
          layoutId={`island-${uniqueId}`}
          className={cn(
            "bg-black/90 backdrop-blur-xl border border-white/20 cursor-pointer overflow-hidden",
            className
          )}
          style={{
            borderRadius: isExpanded ? 20 : 25,
          }}
          animate={{
            width: isExpanded ? 320 : 120,
            height: isExpanded ? 'auto' : 50,
          }}
          onClick={() => setIsExpanded(!isExpanded)}
          whileHover={!isExpanded ? { scale: 1.02 } : {}}
          whileTap={!isExpanded ? { scale: 0.98 } : {}}
        >
          <AnimatePresence mode="wait">
            {!isExpanded ? (
              <motion.div
                key="compact"
                className="flex items-center justify-center h-full"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <span className="text-white text-sm font-medium">Quick Actions</span>
              </motion.div>
            ) : (
              <motion.div
                key="expanded"
                className="p-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ delay: 0.1 }}
              >
                {children}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </MotionConfig>
  )
}

export { DynamicIsland }