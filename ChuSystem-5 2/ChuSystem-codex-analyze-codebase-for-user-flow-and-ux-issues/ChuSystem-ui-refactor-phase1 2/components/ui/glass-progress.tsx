"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cn } from "@/lib/utils"

interface GlassProgressProps 
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  variant?: "default" | "glass" | "compact"
  size?: "sm" | "md" | "lg"
  color?: "primary" | "success" | "warning" | "danger" | "purple" | "blue"
  showValue?: boolean
  label?: string
}

const GlassProgress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  GlassProgressProps
>(({ 
  className, 
  value, 
  variant = "glass",
  size = "md",
  color = "primary",
  showValue = false,
  label,
  ...props 
}, ref) => {
  const variants = {
    default: "bg-[var(--bg-card)]",
    glass: "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] shadow-glass",
    compact: "bg-[var(--bg-glass)]"
  }

  const sizes = {
    sm: "h-2",
    md: "h-3",
    lg: "h-4"
  }

  const colors = {
    primary: "bg-[rgba(var(--color-primary),1)]",
    success: "bg-[rgba(var(--color-green),1)]", 
    warning: "bg-[rgba(var(--color-amber),1)]",
    danger: "bg-[rgba(var(--color-red),1)]",
    purple: "bg-[rgba(var(--color-purple),1)]",
    blue: "bg-blue-500"
  }

  return (
    <div className="w-full space-y-2">
      {(label || showValue) && (
        <div className="flex justify-between items-center">
          {label && (
            <span className="text-sm font-medium text-zinc-300">{label}</span>
          )}
          {showValue && (
            <span className="text-sm text-zinc-400">{value}%</span>
          )}
        </div>
      )}
      
      <ProgressPrimitive.Root
        ref={ref}
        className={cn(
          "relative w-full overflow-hidden rounded-full",
          variants[variant],
          sizes[size],
          className
        )}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className={cn(
            "h-full w-full flex-1 transition-all duration-500 ease-out",
            "shadow-lg",
            colors[color]
          )}
          style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
        />
      </ProgressPrimitive.Root>
    </div>
  )
})

GlassProgress.displayName = ProgressPrimitive.Root.displayName

export { GlassProgress }
export default GlassProgress