import React from 'react'
import { render, fireEvent, screen } from '@testing-library/react'
import QuickActions from '../quick-actions'
import { describe, it, expect, vi } from 'vitest'

const actions = [
  { id: 'a1', icon: <span />, label: 'Action 1', onClick: vi.fn() },
]

describe('QuickActions', () => {
  it('toggles actions when button clicked', () => {
    render(<QuickActions actions={actions} />)
    const toggle = screen.getByLabelText(/open quick actions/i)
    fireEvent.click(toggle)
    expect(toggle).toHaveAttribute('aria-expanded', 'true')
    const item = screen.getByRole('menuitem')
    fireEvent.click(item)
    expect(actions[0].onClick).toHaveBeenCalled()
  })

  it('closes menu on Escape key', () => {
    render(<QuickActions actions={actions} />)
    const toggle = screen.getByLabelText(/open quick actions/i)
    fireEvent.click(toggle)
    expect(toggle).toHaveAttribute('aria-expanded', 'true')
    fireEvent.keyDown(document, { key: 'Escape' })
    expect(toggle).toHaveAttribute('aria-expanded', 'false')
  })
})
