"use client"

import React, { useState } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { 
  HomeIcon,
  UsersIcon,
  CalendarIcon,
  MusicalNoteIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon,
  UserGroupIcon,
  CheckIcon
} from "@heroicons/react/24/outline"
import GlassTooltip from "./glass-tooltip"

interface Product {
  id: string
  name: string
  href: string
  icon: React.ComponentType<any>
  color: string
}

const products: Product[] = [
  {
    id: "dashboard",
    name: "Dashboard",
    href: "/",
    icon: HomeIcon,
    color: "blue"
  },
  {
    id: "people",
    name: "People",
    href: "/people",
    icon: UsersIcon,
    color: "green"
  },
  {
    id: "services",
    name: "Services",
    href: "/services",
    icon: MusicalNoteIcon,
    color: "purple"
  },
  {
    id: "events",
    name: "Events",
    href: "/events",
    icon: CalendarIcon,
    color: "amber"
  },
  {
    id: "groups",
    name: "Groups",
    href: "/groups",
    icon: UserGroupIcon,
    color: "indigo"
  },
  {
    id: "giving",
    name: "Giving",
    href: "/giving",
    icon: CurrencyDollarIcon,
    color: "emerald"
  },
  {
    id: "check-in",
    name: "Check-in",
    href: "/check-in",
    icon: CheckIcon,
    color: "orange"
  }
]

interface SimpleProductSwitcherProps {
  className?: string
}

export default function SimpleProductSwitcher({ className }: SimpleProductSwitcherProps) {
  const pathname = usePathname()
  
  return (
    <div className={cn("border-t border-[var(--border-subtle)] p-3", className)}>
      {/* Product Icons - Workspace Style */}
      <div className="flex items-center justify-center gap-1">
        {products.map((product) => {
          const isActive = pathname === product.href || 
            (product.href !== "/" && pathname.startsWith(product.href))
          
          return (
            <GlassTooltip key={product.id} content={product.name} side="top">
              <Link
                href={product.href}
                className={cn(
                  "w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200 relative",
                  "hover:bg-white/10",
                  isActive 
                    ? "bg-[var(--color-primary)]/20 border border-[var(--color-primary)]/30" 
                    : "hover:bg-white/5"
                )}
              >
                <product.icon 
                  className={cn(
                    "h-4 w-4 transition-colors",
                    isActive ? "text-[var(--color-primary)]" : "text-zinc-400 hover:text-white"
                  )} 
                />
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-[var(--color-primary)] rounded-full" />
                )}
              </Link>
            </GlassTooltip>
          )
        })}
      </div>
    </div>
  )
}

export { SimpleProductSwitcher }