import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--color-primary)]/20 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        primary: "bg-[var(--color-primary)] text-white hover:bg-[var(--color-primary)]/90 shadow-glow",
        glass: "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] text-white hover:bg-[var(--bg-glass-hover)] hover:border-[var(--border-light)] shadow-glow",
        destructive: "bg-red-500/20 text-red-400 border border-red-500/20 backdrop-blur-xl hover:bg-red-500/30 hover:border-red-500/30",
        outline: "border border-[var(--border-subtle)] bg-transparent text-white hover:bg-[var(--bg-glass)] hover:border-[var(--border-light)] backdrop-blur-xl",
        secondary: "bg-[var(--bg-card)] text-white border border-[var(--border-subtle)] hover:bg-[var(--bg-glass)] hover:border-[var(--border-light)]",
        ghost: "text-zinc-300 hover:bg-[var(--bg-glass)] hover:text-white",
        link: "text-[var(--color-primary)] underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 px-3",
        lg: "h-11 px-8",
        icon: "h-10 w-10",
        md: "h-10 px-6 py-2",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, leftIcon, rightIcon, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    // When using asChild, we can't add icons - the child element should handle its own content
    if (asChild) {
      return (
        <Comp
          className={cn(buttonVariants({ variant, size, className }))}
          ref={ref}
          {...props}
        >
          {children}
        </Comp>
      )
    }
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {leftIcon && <span className="mr-1">{leftIcon}</span>}
        {children}
        {rightIcon && <span className="ml-1">{rightIcon}</span>}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
