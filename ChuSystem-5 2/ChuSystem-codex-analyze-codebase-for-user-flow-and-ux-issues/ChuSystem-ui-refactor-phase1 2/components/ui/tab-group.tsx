"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import {
  Tabs as ShadcnTabs,
  <PERSON>bsList as <PERSON>hadcn<PERSON><PERSON>sList,
  Ta<PERSON><PERSON>rigger as ShadcnTabsTrigger,
  TabsContent as ShadcnTabsContent,
} from "@/components/ui/tabs" // Using shadcn/ui components

interface TabItemConfig {
  name: string // Used as value for TabsTrigger and key
  label: string // Display label for the tab
  content: React.ReactNode
  disabled?: boolean
}

interface TabGroupProps {
  tabs: TabItemConfig[]
  defaultValue?: string // Optional: name of the tab to be active by default
  className?: string // Class for the root Tabs component
  tabsListClassName?: string // Class for the TabsList component
  tabTriggerClassName?: string // Class for individual TabsTrigger components
  tabContentClassName?: string // Class for individual TabsContent components
  orientation?: "horizontal" | "vertical"
  activationMode?: "automatic" | "manual"
}

export function TabGroup({
  tabs,
  defaultValue,
  className,
  tabsListClassName,
  tabTriggerClassName,
  tabContentClassName,
  orientation = "horizontal",
  activationMode = "automatic",
}: TabGroupProps) {
  // Determine the default value; if not provided, use the name of the first non-disabled tab
  const effectiveDefaultValue =
    defaultValue || tabs.find((tab) => !tab.disabled)?.name || (tabs.length > 0 ? tabs[0].name : undefined)

  return (
    <ShadcnTabs
      defaultValue={effectiveDefaultValue}
      className={cn("w-full", className)}
      orientation={orientation}
      activationMode={activationMode}
    >
      <ShadcnTabsList
        className={cn(
          "inline-flex items-center justify-center rounded-xl p-1.5", // Base styling for the list
          "bg-[var(--bg-glass)] border border-[var(--border-subtle)]", // Glass effect for the list
          tabsListClassName,
        )}
      >
        {tabs.map((tab) => (
          <ShadcnTabsTrigger
            key={tab.name}
            value={tab.name}
            disabled={tab.disabled}
            className={cn(
              "tab-modern", // Base class for individual tabs (from globals.css)
              // Active state styling is handled by .tab-modern-active via data-[state=active] selector in globals.css
              // Or, if .tab-modern-active is not automatically applied by shadcn on active:
              "data-[state=active]:tab-modern-active data-[state=active]:text-white",
              tabTriggerClassName,
            )}
          >
            {tab.label}
          </ShadcnTabsTrigger>
        ))}
      </ShadcnTabsList>

      {tabs.map((tab) => (
        <ShadcnTabsContent
          key={tab.name}
          value={tab.name}
          className={cn(
            "mt-3 rounded-xl p-4", // Default padding and margin-top for content area
            // "bg-[var(--bg-card)] border border-[var(--border-subtle)]", // Optional: if content needs a card-like bg
            tabContentClassName,
          )}
        >
          {tab.content}
        </ShadcnTabsContent>
      ))}
    </ShadcnTabs>
  )
}

// Also export as default for convenience
export default TabGroup
