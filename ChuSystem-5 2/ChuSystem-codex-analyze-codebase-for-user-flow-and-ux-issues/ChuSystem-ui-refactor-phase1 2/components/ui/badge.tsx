import type React from "react"
import { cn } from "@/lib/utils"

// Map design system color names to global CSS theme color names if they differ
// For this example, assuming they are mostly the same or can be mapped directly.
// The global CSS classes are like: .badge-primary, .badge-success, etc.
// The 'default' color in global CSS seems to be just '.badge' without a color suffix.

type BadgeColor = "primary" | "success" | "warning" | "danger" | "purple" | "amber" | "default"
// Removed blue, green, red, orange, yellow as they are covered by more semantic names or primary/amber

interface BadgeProps {
  children: React.ReactNode
  variant?: "default" // Assuming global .badge handles shape and base styling. Outline might need specific handling if required.
  color?: BadgeColor
  size?: "sm" | "md" | "lg" // Sizes are not explicitly in global CSS, so these might add padding/font-size if needed
  className?: string
  asChild?: boolean // To allow rendering as a child component, useful for integration with shadcn ui if needed later
}

export function Badge({
  children,
  variant = "default", // 'variant' might be less relevant if global CSS handles the main look.
  color = "default",
  size = "sm", // This can be used for minor text/padding adjustments if necessary.
  className,
  asChild = false,
  ...props // Pass through any other HTML attributes
}: BadgeProps & React.HTMLAttributes<HTMLElement>) {
  const colorClass = color === "default" ? "" : `badge-${color}`

  // Size classes - these are examples, adjust as per actual design needs
  // Global .badge might already define a base size.
  const sizeClasses = {
    sm: "text-xs px-3 py-1", // Default from globals.css .badge
    md: "text-sm px-3 py-1.5",
    lg: "text-base px-4 py-2",
  }

  const Component = asChild ? "span" : "div" // Or 'span' by default if more appropriate semantically

  return (
    <Component
      className={cn(
        "badge", // Base badge class from globals.css
        colorClass, // Specific color class like badge-primary
        sizeClasses[size], // Apply size specific styling
        className, // Allow additional custom classes
      )}
      {...props}
    >
      {children}
    </Component>
  )
}

// Also export as default
export default Badge
