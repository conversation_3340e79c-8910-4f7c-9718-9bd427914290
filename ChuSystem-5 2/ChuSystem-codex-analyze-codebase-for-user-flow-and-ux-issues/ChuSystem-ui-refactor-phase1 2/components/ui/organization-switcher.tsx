"use client"

import React, { useState } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { 
  ChevronUpDownIcon, 
  CheckIcon, 
  PlusIcon,
  BuildingOfficeIcon,
  ChevronDownIcon
} from "@heroicons/react/24/outline"
import GlassTooltip from "./glass-tooltip"

interface Organization {
  id: string
  name: string
  plan: string
  avatar?: string
  isActive?: boolean
}

interface OrganizationSwitcherProps {
  organizations?: Organization[]
  currentOrganization?: Organization
  onOrganizationChange?: (org: Organization) => void
  onAddOrganization?: () => void
  isCollapsed?: boolean
}

const defaultOrganizations: Organization[] = [
  {
    id: "1",
    name: "Orbital Church",
    plan: "Pro",
    avatar: "/orbital-logo.png",
    isActive: true
  },
  {
    id: "2", 
    name: "Downtown Community",
    plan: "Starter",
    avatar: "/placeholder-logo.png",
    isActive: false
  },
  {
    id: "3",
    name: "Grace Fellowship",
    plan: "Pro",
    avatar: "/placeholder-logo.svg",
    isActive: false
  }
]

const OrganizationSwitcher: React.FC<OrganizationSwitcherProps> = ({
  organizations = defaultOrganizations,
  currentOrganization = defaultOrganizations[0],
  onOrganizationChange,
  onAddOrganization,
  isCollapsed = false
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleOrganizationSelect = (org: Organization) => {
    setIsOpen(false)
    onOrganizationChange?.(org)
  }

  const triggerContent = (
    <button
      onClick={() => setIsOpen(!isOpen)}
      className={cn(
        "w-full flex items-center gap-3 p-3 rounded-xl transition-all duration-200",
        "hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20",
        "group cursor-pointer",
        isCollapsed && "justify-center"
      )}
    >
      <div className="relative">
        <Image
          src={currentOrganization.avatar || "/orbital-logo.png"}
          alt={currentOrganization.name}
          width={32}
          height={32}
          className="rounded-lg object-cover border border-[var(--border-subtle)]"
        />
        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-[var(--bg-sidebar)]" />
      </div>
      
      {!isCollapsed && (
        <>
          <div className="flex-1 text-left min-w-0">
            <p className="text-sm font-medium text-white truncate">
              {currentOrganization.name}
            </p>
            <p className="text-xs text-zinc-400">
              {currentOrganization.plan} Plan
            </p>
          </div>
          
          <ChevronDownIcon 
            className={cn(
              "h-4 w-4 text-zinc-400 transition-transform duration-200",
              isOpen && "rotate-180"
            )} 
          />
        </>
      )}
    </button>
  )

  if (isCollapsed) {
    return (
      <div className="p-3 border-t border-[var(--border-subtle)]">
        <GlassTooltip 
          content={`${currentOrganization.name} (${currentOrganization.plan} Plan)`}
          side="right"
        >
          {triggerContent}
        </GlassTooltip>
      </div>
    )
  }

  return (
    <div className="p-3 border-t border-[var(--border-subtle)] relative">
      {triggerContent}
      
      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute bottom-full left-3 right-3 mb-2 z-50">
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-lg animate-in slide-in-from-bottom-2 duration-200">
            {/* Header */}
            <div className="p-3 border-b border-[var(--border-subtle)]">
              <p className="text-xs font-medium text-zinc-400 uppercase tracking-wider">
                Organizations
              </p>
            </div>
            
            {/* Organization List */}
            <div className="p-2 space-y-1 max-h-64 overflow-y-auto custom-scrollbar">
              {organizations.map((org) => (
                <button
                  key={org.id}
                  onClick={() => handleOrganizationSelect(org)}
                  className={cn(
                    "w-full flex items-center gap-3 p-2 rounded-lg transition-colors",
                    "hover:bg-white/5 focus:outline-none focus:bg-white/5",
                    org.id === currentOrganization.id && "bg-[var(--color-primary)]/10 border border-[var(--color-primary)]/20"
                  )}
                >
                  <div className="relative">
                    <Image
                      src={org.avatar || "/orbital-logo.png"}
                      alt={org.name}
                      width={28}
                      height={28}
                      className="rounded-lg object-cover border border-[var(--border-subtle)]"
                    />
                    {org.isActive && (
                      <div className="absolute -bottom-1 -right-1 w-2.5 h-2.5 bg-green-500 rounded-full border border-[var(--bg-glass)]" />
                    )}
                  </div>
                  
                  <div className="flex-1 text-left min-w-0">
                    <p className="text-sm font-medium text-white truncate">
                      {org.name}
                    </p>
                    <p className="text-xs text-zinc-400">
                      {org.plan} Plan
                    </p>
                  </div>
                  
                  {org.id === currentOrganization.id && (
                    <CheckIcon className="h-4 w-4 text-[var(--color-primary)]" />
                  )}
                </button>
              ))}
            </div>
            
            {/* Add Organization */}
            <div className="p-2 border-t border-[var(--border-subtle)]">
              <button
                onClick={() => {
                  setIsOpen(false)
                  onAddOrganization?.()
                }}
                className="w-full flex items-center gap-3 p-2 rounded-lg transition-colors hover:bg-white/5 focus:outline-none focus:bg-white/5"
              >
                <div className="w-7 h-7 rounded-lg border-2 border-dashed border-zinc-600 flex items-center justify-center">
                  <PlusIcon className="h-4 w-4 text-zinc-400" />
                </div>
                <span className="text-sm font-medium text-zinc-300">
                  Add Organization
                </span>
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Backdrop to close dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export { OrganizationSwitcher }
export default OrganizationSwitcher