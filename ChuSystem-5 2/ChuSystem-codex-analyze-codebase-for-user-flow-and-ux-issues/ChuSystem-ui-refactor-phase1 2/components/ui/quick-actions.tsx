"use client"

import type React from "react"

import { cn } from "@/lib/utils"
import { Button } from "../ui/button"
import { PlusIcon } from "@heroicons/react/16/solid"
import { useState, useEffect } from "react"

interface QuickAction {
  id: string
  icon: React.ReactNode
  label: string
  onClick: () => void
  color?: string
  href?: string
}

interface QuickActionsProps {
  actions: QuickAction[]
  title?: string
  className?: string
}

export default function QuickActions({ actions, title, className }: QuickActionsProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Close menu when Escape is pressed
  useEffect(() => {
    if (!isExpanded) return
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsExpanded(false)
      }
    }
    document.addEventListener('keydown', handleKey)
    return () => document.removeEventListener('keydown', handleKey)
  }, [isExpanded])

  return (
    <div className={cn("relative", className)}>
      <div className="flex flex-col items-end gap-3">
        {isExpanded && (
          <div className="flex flex-col-reverse gap-3 items-end mb-3" role="menu" aria-label={title || 'Quick actions'}>
            {actions.map((action) => (
              <Button
                key={action.id}
                variant="glass"
                size="md"
                className={cn(
                  "flex items-center gap-2 shadow-ambient hover:shadow-ambient-hover transition-all rounded-xl", // Applied rounded-xl
                  "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)]", // Used theme variables
                  "hover:translate-y-[-2px] hover:bg-[var(--bg-glass-hover)]", // Added hover background
                )}
                onClick={action.onClick}
                role="menuitem"
              >
                <div
                  className={cn(
                    "w-8 h-8 rounded-xl flex items-center justify-center", // Applied rounded-xl
                    action.color ? action.color : "bg-[rgba(var(--color-primary),0.2)]", // Default to themed primary, allow override
                  )}
                >
                  {action.icon}
                </div>
                <span>{action.label}</span>
              </Button>
            ))}
          </div>
        )}

        <Button
          variant="glass"
          size="icon-lg"
          className={cn(
            "rounded-full shadow-ambient hover:shadow-ambient-hover transition-all",
            "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)]", // Used theme variables
            "hover:translate-y-[-2px] hover:bg-[var(--bg-glass-hover)]", // Added hover background
            isExpanded && "rotate-45",
          )}
          onClick={() => setIsExpanded(!isExpanded)}
          aria-label={isExpanded ? 'Close quick actions' : 'Open quick actions'}
          aria-expanded={isExpanded}
        >
          <PlusIcon className="h-6 w-6" />
        </Button>
      </div>
    </div>
  )
}
