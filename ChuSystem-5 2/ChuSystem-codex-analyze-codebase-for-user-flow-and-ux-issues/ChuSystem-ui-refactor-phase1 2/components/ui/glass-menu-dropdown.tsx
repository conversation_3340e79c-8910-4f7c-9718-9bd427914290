"use client"

import React, { useState } from "react"
import { ChevronDownIcon } from "@heroicons/react/16/solid"
import { cn } from "@/lib/utils"

interface GlassMenuDropdownProps {
  trigger: React.ReactNode
  children: React.ReactNode
  align?: "left" | "right"
  className?: string
  variant?: "default" | "glass" | "compact"
}

export default function GlassMenuDropdown({ 
  trigger, 
  children, 
  align = "left",
  className,
  variant = "glass"
}: GlassMenuDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)

  const triggerStyles = {
    default: "flex items-center gap-1 p-2 rounded-xl hover:bg-white/5 transition-all duration-200",
    glass: "flex items-center gap-1 p-2 rounded-xl bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] hover:bg-white/5 transition-all duration-200 shadow-glass",
    compact: "flex items-center gap-1 p-1 rounded-lg hover:bg-white/5 transition-colors"
  }

  const dropdownStyles = {
    default: "absolute top-full z-20 mt-1 min-w-[200px] bg-[var(--bg-card)] border border-[var(--border-subtle)] rounded-xl shadow-lg",
    glass: "absolute top-full z-20 mt-1 min-w-[200px] bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-lg animate-in slide-in-from-top-2 duration-200",
    compact: "absolute top-full z-20 mt-1 min-w-[180px] bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-lg shadow-lg"
  }

  return (
    <div className={cn("relative", className)}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={triggerStyles[variant]}
      >
        {trigger}
        <ChevronDownIcon className={cn(
          "h-4 w-4 text-zinc-400 transition-transform duration-200",
          isOpen && "rotate-180"
        )} />
      </button>
      
      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)} 
          />
          <div className={cn(
            dropdownStyles[variant],
            align === "right" ? "right-0" : "left-0"
          )}>
            {children}
          </div>
        </>
      )}
    </div>
  )
}

export { GlassMenuDropdown }