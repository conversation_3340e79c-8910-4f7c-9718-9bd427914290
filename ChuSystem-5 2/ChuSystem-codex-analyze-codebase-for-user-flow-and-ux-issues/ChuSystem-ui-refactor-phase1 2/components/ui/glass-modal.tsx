"use client"

import React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { cn } from "@/lib/utils"
import { XMarkIcon } from "@heroicons/react/24/outline"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface GlassModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  description?: string
  children: React.ReactNode
  size?: "sm" | "md" | "lg" | "xl" | "full"
  blur?: "sm" | "md" | "lg"
  showCloseButton?: boolean
  className?: string
}

const GlassModal: React.FC<GlassModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  size = "md",
  blur = "lg",
  showCloseButton = true,
  className
}) => {
  const sizeStyles = {
    sm: "max-w-sm",
    md: "max-w-md", 
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full mx-4"
  }

  const blurStyles = {
    sm: "backdrop-blur-sm",
    md: "backdrop-blur-md",
    lg: "backdrop-blur-lg"
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className={cn(
          "bg-[var(--bg-glass)] border border-[var(--border-subtle)]",
          blurStyles[blur],
          "shadow-2xl shadow-black/20",
          "rounded-2xl",
          sizeStyles[size],
          className
        )}
      >
        {/* Custom Close Button */}
        {showCloseButton && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4 rounded-full hover:bg-white/10"
            onClick={onClose}
          >
            <XMarkIcon className="h-4 w-4" />
          </Button>
        )}

        <DialogHeader className={showCloseButton ? "pr-12" : ""}>
          {title && (
            <DialogTitle className="text-xl font-semibold text-white">
              {title}
            </DialogTitle>
          )}
          {description && (
            <DialogDescription className="text-zinc-400">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="mt-6">
          {children}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export { GlassModal }
