"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"

interface ExpandableToolButtonProps {
  icon: React.ReactNode
  label: string
  expandedContent?: React.ReactNode
  onClick?: () => void
  className?: string
  variant?: "default" | "primary" | "secondary"
  size?: "sm" | "md" | "lg"
  disabled?: boolean
  badge?: string | number
}

export default function ExpandableToolButton({
  icon,
  label,
  expandedContent,
  onClick,
  className,
  variant = "default",
  size = "md",
  disabled = false,
  badge
}: ExpandableToolButtonProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const variants = {
    default: "bg-[var(--bg-glass)] border border-[var(--border-subtle)] hover:bg-[var(--bg-glass-hover)]",
    primary: "bg-[var(--color-primary)]/20 border border-[var(--color-primary)]/30 hover:bg-[var(--color-primary)]/30",
    secondary: "bg-white/5 border border-white/10 hover:bg-white/10"
  }

  const sizes = {
    sm: "w-10 h-10",
    md: "w-12 h-12", 
    lg: "w-14 h-14"
  }

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  }

  const handleClick = () => {
    if (expandedContent && !disabled) {
      setIsExpanded(!isExpanded)
    }
    onClick?.()
  }

  return (
    <div className={cn("relative", className)}>
      {/* Main Button */}
      <button
        onClick={handleClick}
        disabled={disabled}
        className={cn(
          "rounded-xl flex items-center justify-center transition-all duration-200 relative group",
          "focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20",
          variants[variant],
          sizes[size],
          disabled && "opacity-50 cursor-not-allowed",
          isExpanded && "bg-[var(--bg-glass-hover)]"
        )}
      >
        {icon && React.cloneElement(icon as React.ReactElement, {
          className: cn(iconSizes[size], "text-current")
        })}
        
        {/* Badge */}
        {badge && (
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-[var(--color-primary)] rounded-full flex items-center justify-center">
            <span className="text-xs font-medium text-black">{badge}</span>
          </div>
        )}

        {/* Expansion Indicator */}
        {expandedContent && (
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="w-1 h-1 bg-zinc-400 rounded-full" />
          </div>
        )}
      </button>

      {/* Expanded Content */}
      <AnimatePresence>
        {isExpanded && expandedContent && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-40" 
              onClick={() => setIsExpanded(false)}
            />
            
            {/* Expanded Panel */}
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.15, ease: "easeOut" }}
              className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 z-50"
            >
              <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-lg p-3 min-w-48">
                {/* Label */}
                <div className="text-sm font-medium text-white mb-2">{label}</div>
                
                {/* Content */}
                <div className="text-sm text-zinc-300">
                  {expandedContent}
                </div>
              </div>
              
              {/* Arrow */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 -translate-y-1">
                <div className="w-2 h-2 bg-[var(--bg-glass)] border-r border-b border-[var(--border-subtle)] transform rotate-45" />
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}

export { ExpandableToolButton }