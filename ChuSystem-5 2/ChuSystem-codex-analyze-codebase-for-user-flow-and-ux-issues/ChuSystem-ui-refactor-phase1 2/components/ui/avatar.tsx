"use client"

import * as React from "react"
import * as AvatarPrimitive from "@radix-ui/react-avatar"
import { cn } from "@/lib/utils"

const AvatarRoot = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Root
    ref={ref}
    className={cn("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full", className)}
    {...props}
  />
))
AvatarRoot.displayName = AvatarPrimitive.Root.displayName

const AvatarImage = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Image>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Image
    ref={ref}
    className={cn("aspect-square h-full w-full", className)}
    {...props}
  />
))
AvatarImage.displayName = AvatarPrimitive.Image.displayName

const AvatarFallback = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Fallback>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Fallback
    ref={ref}
    className={cn(
      "flex h-full w-full items-center justify-center rounded-full bg-muted",
      className
    )}
    {...props}
  />
))
AvatarFallback.displayName = AvatarPrimitive.Fallback.displayName

interface AvatarProps {
  src?: string // Made src optional to allow for fallback by initials
  alt?: string
  fallbackText?: string // Text to display in fallback, e.g., initials
  size?: "xs" | "sm" | "md" | "lg" | "xl"
  className?: string
  ring?: boolean
  ringColor?: string
  online?: boolean
  onlineStatusPosition?: "top-right" | "bottom-right" | "top-left" | "bottom-left"
}

const Avatar = React.forwardRef<React.ElementRef<typeof AvatarRoot>, AvatarProps>(
  (
    {
      src,
      alt = "Avatar",
      fallbackText = "??",
      size = "md",
      className,
      ring = false,
      ringColor = "ring-[var(--border-light)]", // Themed ring color
      online,
      onlineStatusPosition = "bottom-right",
      ...props
    },
    ref,
  ) => {
    const sizeStyles = {
      xs: "w-6 h-6 text-xs",
      sm: "w-8 h-8 text-sm",
      md: "w-10 h-10 text-base",
      lg: "w-12 h-12 text-lg",
      xl: "w-16 h-16 text-xl",
    }

    const onlineIndicatorSizeStyles = {
      xs: "w-2 h-2",
      sm: "w-2.5 h-2.5",
      md: "w-3 h-3",
      lg: "w-3.5 h-3.5",
      xl: "w-4 h-4",
    }
    
    const onlineIndicatorPositionStyles = {
        "top-right": "top-0 right-0",
        "bottom-right": "bottom-0 right-0",
        "top-left": "top-0 left-0",
        "bottom-left": "bottom-0 left-0",
    }

    return (
      <div className={cn("relative inline-block", className)}>
        <AvatarRoot
          ref={ref}
          className={cn(
            "border border-[var(--border-subtle)]", // Added subtle border
            sizeStyles[size],
            ring && `ring-2 ${ringColor} shadow-glow`, // Themed ring and shadow
            className, // Allow overriding from props
          )}
          {...props}
        >
          <AvatarImage src={src} alt={alt} className="object-cover" />
          <AvatarFallback
            className={cn(
              "bg-[var(--bg-glass)] text-white font-medium", // Themed fallback
              sizeStyles[size], // Ensure fallback text size matches avatar size
            )}
          >
            {fallbackText
              .split(" ")
              .map((n) => n[0])
              .join("")
              .toUpperCase()
              .substring(0, 2)}
          </AvatarFallback>
        </AvatarRoot>
        {online !== undefined && (
          <span
            className={cn(
              "absolute rounded-full border-2 border-[var(--bg-card)]", // Border to match card background for contrast
              onlineIndicatorPositionStyles[onlineStatusPosition],
              onlineIndicatorSizeStyles[size],
              online ? "bg-[var(--color-green)]" : "bg-zinc-500", // Themed online/offline status
            )}
          />
        )}
      </div>
    )
  },
)

Avatar.displayName = "Avatar"

// Export base shadcn components
export { AvatarRoot, AvatarImage, AvatarFallback }

// Export as default
export default Avatar

// Also export as named export
export { Avatar }
