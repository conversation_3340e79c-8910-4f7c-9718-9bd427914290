"use client"

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence, useScroll, useTransform } from "framer-motion"
import { 
  CommandLineIcon,
  Squares2X2Icon,
  BoltIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  BellIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  XMarkIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  RocketLaunchIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { usePathname } from "next/navigation"

interface FloatingCommandCenterProps {
  children?: React.ReactNode
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"
  alwaysExpanded?: boolean
}

export default function FloatingCommandCenter({ 
  children,
  position = "bottom-right",
  alwaysExpanded = false
}: FloatingCommandCenterProps) {
  const [isExpanded, setIsExpanded] = useState(alwaysExpanded)
  const [isDocked, setIsDocked] = useState(false)
  const [miniMode, setMiniMode] = useState(false)
  const pathname = usePathname()
  const { scrollY } = useScroll()
  
  // Transform based on scroll
  const floatY = useTransform(scrollY, [0, 100], [0, -10])
  const opacity = useTransform(scrollY, [0, 50], [1, 0.95])

  // Get context-aware actions based on current path
  const getContextActions = () => {
    if (pathname.includes('/services')) {
      return [
        { icon: PlusIcon, label: "New Service", color: "bg-blue-500" },
        { icon: MagnifyingGlassIcon, label: "Find Song", color: "bg-purple-500" },
        { icon: BoltIcon, label: "Go Live", color: "bg-red-500" }
      ]
    } else if (pathname.includes('/people')) {
      return [
        { icon: PlusIcon, label: "Add Person", color: "bg-green-500" },
        { icon: MagnifyingGlassIcon, label: "Search", color: "bg-blue-500" },
        { icon: ChartBarIcon, label: "Reports", color: "bg-purple-500" }
      ]
    }
    // Default actions
    return [
      { icon: CommandLineIcon, label: "Command", color: "bg-zinc-500" },
      { icon: MagnifyingGlassIcon, label: "Search", color: "bg-zinc-500" },
      { icon: BellIcon, label: "Notifications", color: "bg-zinc-500" }
    ]
  }

  const positionClasses = {
    "bottom-right": "bottom-6 right-6",
    "bottom-left": "bottom-6 left-6",
    "top-right": "top-20 right-6",
    "top-left": "top-20 left-6"
  }

  // Safe window dimensions
  const [windowDimensions, setWindowDimensions] = useState({ width: 1920, height: 1080 })
  
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setWindowDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      })
      
      const handleResize = () => {
        setWindowDimensions({
          width: window.innerWidth,
          height: window.innerHeight
        })
      }
      
      window.addEventListener('resize', handleResize, { passive: true })
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <>
      {/* Main Floating Panel */}
      <motion.div
        style={{ y: floatY, opacity }}
        className={cn(
          "fixed z-50",
          positionClasses[position],
          isDocked && "transition-all duration-300"
        )}
        drag={!isDocked}
        dragConstraints={{
          left: -windowDimensions.width + 100,
          right: windowDimensions.width - 100,
          top: -windowDimensions.height + 100,
          bottom: windowDimensions.height - 100
        }}
        dragElastic={0.2}
        whileDrag={{ scale: 1.05 }}
      >
        <AnimatePresence mode="wait">
          {miniMode ? (
            // Mini Mode - Just the fab button
            <motion.div
              key="mini"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              className="relative"
            >
              <button
                onClick={() => setMiniMode(false)}
                className="w-14 h-14 bg-[var(--color-primary)] hover:bg-[var(--color-primary-hover)] rounded-full shadow-glow-xl flex items-center justify-center group"
              >
                <RocketLaunchIcon className="h-6 w-6 text-black group-hover:rotate-12 transition-transform" />
              </button>
              
              {/* Quick Action Ring */}
              <div className="absolute inset-0 -m-2">
                {getContextActions().slice(0, 3).map((action, idx) => {
                  const angle = (idx * 120) - 90
                  const x = Math.cos(angle * Math.PI / 180) * 40
                  const y = Math.sin(angle * Math.PI / 180) * 40
                  
                  return (
                    <motion.button
                      key={idx}
                      initial={{ scale: 0, x: 0, y: 0 }}
                      animate={{ scale: 1, x, y }}
                      transition={{ delay: idx * 0.1 }}
                      className={cn(
                        "absolute w-10 h-10 rounded-full flex items-center justify-center",
                        action.color,
                        "shadow-lg hover:shadow-xl transition-all"
                      )}
                      style={{
                        left: '50%',
                        top: '50%',
                        marginLeft: '-20px',
                        marginTop: '-20px'
                      }}
                    >
                      <action.icon className="h-5 w-5 text-white" />
                    </motion.button>
                  )
                })}
              </div>
            </motion.div>
          ) : (
            // Expanded Mode
            <motion.div
              key="expanded"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className={cn(
                "bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)]",
                "rounded-2xl shadow-glow-2xl overflow-hidden",
                isExpanded ? "w-80" : "w-auto"
              )}
            >
              {/* Header */}
              <div className="p-3 border-b border-[var(--border-subtle)] flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <RocketLaunchIcon className="h-5 w-5 text-[var(--color-primary)]" />
                  <span className="text-sm font-medium">Command Center</span>
                </div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => setIsDocked(!isDocked)}
                    className={cn(
                      "p-1 rounded hover:bg-white/10 transition-colors",
                      isDocked && "text-[var(--color-primary)]"
                    )}
                    title={isDocked ? "Undock" : "Dock"}
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="p-1 rounded hover:bg-white/10 transition-colors"
                  >
                    {isExpanded ? <ChevronDownIcon className="h-4 w-4" /> : <ChevronUpIcon className="h-4 w-4" />}
                  </button>
                  <button
                    onClick={() => setMiniMode(true)}
                    className="p-1 rounded hover:bg-white/10 transition-colors"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <AnimatePresence>
                {isExpanded && (
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: "auto" }}
                    exit={{ height: 0 }}
                  >
                    {children || (
                      <div className="p-4 space-y-3">
                        {/* Quick Search */}
                        <div className="relative">
                          <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-zinc-400" />
                          <input
                            type="text"
                            placeholder="Quick search..."
                            className="w-full pl-10 pr-3 py-2 bg-white/10 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                          />
                        </div>

                        {/* Context Actions */}
                        <div className="space-y-2">
                          {getContextActions().map((action, idx) => (
                            <button
                              key={idx}
                              className="w-full flex items-center gap-3 p-2 hover:bg-white/10 rounded-lg transition-colors"
                            >
                              <div className={cn("w-8 h-8 rounded-lg flex items-center justify-center", action.color)}>
                                <action.icon className="h-4 w-4 text-white" />
                              </div>
                              <span className="text-sm">{action.label}</span>
                            </button>
                          ))}
                        </div>

                        {/* Quick Stats */}
                        <div className="pt-3 border-t border-[var(--border-subtle)]">
                          <p className="text-xs text-zinc-400 mb-2">Quick Stats</p>
                          <div className="grid grid-cols-3 gap-2 text-center">
                            <div className="p-2 bg-white/5 rounded-lg">
                              <p className="text-lg font-bold">152</p>
                              <p className="text-xs text-zinc-400">Today</p>
                            </div>
                            <div className="p-2 bg-white/5 rounded-lg">
                              <p className="text-lg font-bold">89%</p>
                              <p className="text-xs text-zinc-400">Rate</p>
                            </div>
                            <div className="p-2 bg-white/5 rounded-lg">
                              <p className="text-lg font-bold">24</p>
                              <p className="text-xs text-zinc-400">Active</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Collapsed Quick Actions */}
              {!isExpanded && (
                <div className="p-2 flex gap-2">
                  {getContextActions().map((action, idx) => (
                    <button
                      key={idx}
                      className={cn(
                        "w-10 h-10 rounded-lg flex items-center justify-center transition-all",
                        action.color,
                        "hover:scale-110"
                      )}
                    >
                      <action.icon className="h-5 w-5 text-white" />
                    </button>
                  ))}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Docked Bar (when docked) */}
      <AnimatePresence>
        {isDocked && !miniMode && (
          <motion.div
            initial={{ y: 100 }}
            animate={{ y: 0 }}
            exit={{ y: 100 }}
            className="fixed bottom-0 left-0 right-0 h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-t border-[var(--border-subtle)] z-40"
          >
            <div className="h-full px-6 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <RocketLaunchIcon className="h-5 w-5 text-[var(--color-primary)]" />
                <span className="text-sm font-medium">Quick Actions</span>
              </div>
              <div className="flex items-center gap-2">
                {getContextActions().map((action, idx) => (
                  <Button
                    key={idx}
                    variant="glass"
                    size="sm"
                    leftIcon={<action.icon className="h-4 w-4" />}
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export { FloatingCommandCenter }