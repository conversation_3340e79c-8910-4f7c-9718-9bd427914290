"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { 
  MagnifyingGlassIcon, 
  XMarkIcon,
  ArrowRightIcon,
  CommandLineIcon,
  ClockIcon,
  StarIcon
} from "@heroicons/react/24/outline"
import { 
  HomeIcon,
  UsersIcon,
  CalendarIcon,
  MusicalNoteIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  CheckIcon,
  FolderIcon,
  DocumentTextIcon,
  VideoCameraIcon,
  EnvelopeIcon,
  PlusIcon,
  UserPlusIcon,
  QrCodeIcon,
  ChartBarIcon,
  CogIcon
} from "@heroicons/react/24/solid"

interface CommandItem {
  id: string
  title: string
  description?: string
  icon?: React.ReactNode
  category: string
  keywords: string[]
  action: () => void
  shortcut?: string
  badge?: string
  recent?: boolean
  favorite?: boolean
}

interface CommandPaletteProps {
  isOpen: boolean
  onClose: () => void
}

export default function CommandPalette({ isOpen, onClose }: CommandPaletteProps) {
  const router = useRouter()
  const [search, setSearch] = useState("")
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  // Command definitions
  const commands: CommandItem[] = useMemo(() => [
    // Navigation
    {
      id: "nav-dashboard",
      title: "Go to Dashboard",
      description: "View church overview and statistics",
      icon: <HomeIcon className="h-4 w-4" />,
      category: "Navigation",
      keywords: ["home", "dashboard", "overview", "stats"],
      action: () => {
        router.push("/")
        onClose()
      },
      shortcut: "⌘D"
    },
    {
      id: "nav-people",
      title: "Go to People",
      description: "Manage church members and visitors",
      icon: <UsersIcon className="h-4 w-4" />,
      category: "Navigation",
      keywords: ["people", "members", "directory", "contacts"],
      action: () => {
        router.push("/people")
        onClose()
      },
      shortcut: "⌘P"
    },
    {
      id: "nav-services",
      title: "Go to Services",
      description: "Plan and manage worship services",
      icon: <MusicalNoteIcon className="h-4 w-4" />,
      category: "Navigation",
      keywords: ["services", "worship", "planning", "music"],
      action: () => {
        router.push("/services")
        onClose()
      },
      shortcut: "⌘S"
    },
    {
      id: "nav-events",
      title: "Go to Events",
      description: "View and manage church events",
      icon: <CalendarIcon className="h-4 w-4" />,
      category: "Navigation",
      keywords: ["events", "calendar", "schedule", "activities"],
      action: () => {
        router.push("/events")
        onClose()
      },
      shortcut: "⌘E"
    },
    {
      id: "nav-giving",
      title: "Go to Giving",
      description: "Track donations and financial reports",
      icon: <CurrencyDollarIcon className="h-4 w-4" />,
      category: "Navigation",
      keywords: ["giving", "donations", "finance", "money", "tithes"],
      action: () => {
        router.push("/giving")
        onClose()
      }
    },

    // Quick Actions
    {
      id: "action-new-service",
      title: "Create New Service",
      description: "Plan a new worship service",
      icon: <PlusIcon className="h-4 w-4" />,
      category: "Actions",
      keywords: ["new", "create", "service", "worship", "plan"],
      action: () => {
        router.push("/services/new")
        onClose()
      },
      badge: "Popular",
      favorite: true
    },
    {
      id: "action-add-person",
      title: "Add New Person",
      description: "Add a member or visitor",
      icon: <UserPlusIcon className="h-4 w-4" />,
      category: "Actions",
      keywords: ["add", "new", "person", "member", "visitor"],
      action: () => {
        router.push("/people/new")
        onClose()
      }
    },
    {
      id: "action-quick-checkin",
      title: "Quick Check-in",
      description: "Start check-in process",
      icon: <QrCodeIcon className="h-4 w-4" />,
      category: "Actions",
      keywords: ["checkin", "attendance", "qr", "scan"],
      action: () => {
        router.push("/check-in")
        onClose()
      }
    },
    {
      id: "action-record-donation",
      title: "Record Donation",
      description: "Add a new contribution",
      icon: <CurrencyDollarIcon className="h-4 w-4" />,
      category: "Actions",
      keywords: ["donation", "giving", "contribution", "tithe", "offering"],
      action: () => {
        router.push("/giving/new")
        onClose()
      }
    },

    // Search
    {
      id: "search-people",
      title: "Search People",
      description: "Find members and visitors",
      icon: <MagnifyingGlassIcon className="h-4 w-4" />,
      category: "Search",
      keywords: ["search", "find", "people", "member", "lookup"],
      action: () => {
        router.push("/people?search=true")
        onClose()
      }
    },
    {
      id: "search-songs",
      title: "Search Songs",
      description: "Find songs in the library",
      icon: <MagnifyingGlassIcon className="h-4 w-4" />,
      category: "Search",
      keywords: ["search", "find", "songs", "music", "worship"],
      action: () => {
        router.push("/services/song-library?search=true")
        onClose()
      }
    },

    // Reports
    {
      id: "report-attendance",
      title: "Attendance Report",
      description: "View attendance trends",
      icon: <ChartBarIcon className="h-4 w-4" />,
      category: "Reports",
      keywords: ["report", "attendance", "analytics", "trends"],
      action: () => {
        router.push("/reports/attendance")
        onClose()
      }
    },
    {
      id: "report-giving",
      title: "Giving Report",
      description: "View financial reports",
      icon: <ChartBarIcon className="h-4 w-4" />,
      category: "Reports",
      keywords: ["report", "giving", "finance", "donations"],
      action: () => {
        router.push("/giving/reports")
        onClose()
      }
    },

    // Settings
    {
      id: "settings-general",
      title: "General Settings",
      description: "Configure church settings",
      icon: <CogIcon className="h-4 w-4" />,
      category: "Settings",
      keywords: ["settings", "preferences", "config", "setup"],
      action: () => {
        router.push("/settings")
        onClose()
      }
    }
  ], [router, onClose])

  // Filter commands based on search
  const filteredCommands = useMemo(() => {
    const searchLower = search.toLowerCase()
    
    return commands.filter(command => {
      // Filter by category if selected
      if (selectedCategory && command.category !== selectedCategory) {
        return false
      }
      
      // Filter by search term
      if (!searchLower) return true
      
      return (
        command.title.toLowerCase().includes(searchLower) ||
        command.description?.toLowerCase().includes(searchLower) ||
        command.keywords.some(keyword => keyword.toLowerCase().includes(searchLower))
      )
    })
  }, [search, selectedCategory, commands])

  // Group commands by category
  const groupedCommands = useMemo(() => {
    const groups: Record<string, CommandItem[]> = {}
    
    filteredCommands.forEach(command => {
      if (!groups[command.category]) {
        groups[command.category] = []
      }
      groups[command.category].push(command)
    })
    
    return groups
  }, [filteredCommands])

  // Get all categories
  const categories = useMemo(() => {
    return Array.from(new Set(commands.map(c => c.category)))
  }, [commands])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) {
        // Open command palette with Cmd+K
        if ((e.metaKey || e.ctrlKey) && e.key === "k") {
          e.preventDefault()
          // This would need to be handled by parent component
        }
        return
      }

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault()
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          )
          break
        case "ArrowUp":
          e.preventDefault()
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          )
          break
        case "Enter":
          e.preventDefault()
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action()
          }
          break
        case "Escape":
          e.preventDefault()
          onClose()
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [isOpen, filteredCommands, selectedIndex, onClose])

  // Reset selected index when search changes
  useEffect(() => {
    setSelectedIndex(0)
  }, [search, selectedCategory])

  if (!isOpen) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
          />

          {/* Command Palette */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -20 }}
            transition={{ duration: 0.15 }}
            className="fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl z-50"
          >
            <div className="bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)] rounded-2xl shadow-glow-2xl overflow-hidden">
              {/* Search Header */}
              <div className="p-4 border-b border-[var(--border-subtle)]">
                <div className="flex items-center gap-3">
                  <MagnifyingGlassIcon className="h-5 w-5 text-zinc-400 flex-shrink-0" />
                  <input
                    ref={(input) => input?.focus()}
                    type="text"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder="Search for commands, pages, or actions..."
                    className="flex-1 bg-transparent outline-none text-sm placeholder-zinc-500"
                  />
                  <div className="flex items-center gap-2">
                    <kbd className="px-2 py-1 bg-white/10 rounded text-xs">ESC</kbd>
                    <button
                      onClick={onClose}
                      className="p-1 rounded-lg hover:bg-white/10 transition-colors"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Category Pills */}
                <div className="flex items-center gap-2 mt-3">
                  <button
                    onClick={() => setSelectedCategory(null)}
                    className={cn(
                      "px-3 py-1 rounded-full text-xs font-medium transition-colors",
                      selectedCategory === null
                        ? "bg-[var(--color-primary)] text-black"
                        : "bg-white/10 hover:bg-white/20"
                    )}
                  >
                    All
                  </button>
                  {categories.map(category => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={cn(
                        "px-3 py-1 rounded-full text-xs font-medium transition-colors",
                        selectedCategory === category
                          ? "bg-[var(--color-primary)] text-black"
                          : "bg-white/10 hover:bg-white/20"
                      )}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>

              {/* Command List */}
              <div className="max-h-96 overflow-y-auto custom-scrollbar">
                {Object.entries(groupedCommands).map(([category, items]) => (
                  <div key={category}>
                    <div className="px-4 py-2 text-xs font-medium text-zinc-400 sticky top-0 bg-[var(--bg-glass)]">
                      {category}
                    </div>
                    {items.map((command, idx) => {
                      const globalIndex = filteredCommands.findIndex(c => c.id === command.id)
                      const isSelected = globalIndex === selectedIndex
                      
                      return (
                        <button
                          key={command.id}
                          onClick={command.action}
                          onMouseEnter={() => setSelectedIndex(globalIndex)}
                          className={cn(
                            "w-full px-4 py-3 flex items-center gap-3 transition-colors text-left",
                            isSelected ? "bg-white/10" : "hover:bg-white/5"
                          )}
                        >
                          {/* Icon */}
                          <div className={cn(
                            "w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0",
                            isSelected ? "bg-[var(--color-primary)]/20" : "bg-white/10"
                          )}>
                            {command.icon}
                          </div>

                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <p className="text-sm font-medium">{command.title}</p>
                              {command.badge && (
                                <span className="px-1.5 py-0.5 bg-[var(--color-primary)]/20 text-[var(--color-primary)] rounded text-[10px] font-medium">
                                  {command.badge}
                                </span>
                              )}
                              {command.favorite && (
                                <StarIcon className="h-3 w-3 text-amber-500" />
                              )}
                            </div>
                            {command.description && (
                              <p className="text-xs text-zinc-400 mt-0.5">{command.description}</p>
                            )}
                          </div>

                          {/* Shortcut or Arrow */}
                          {command.shortcut ? (
                            <kbd className="px-2 py-1 bg-white/10 rounded text-xs">
                              {command.shortcut}
                            </kbd>
                          ) : (
                            <ArrowRightIcon className="h-4 w-4 text-zinc-400" />
                          )}
                        </button>
                      )
                    })}
                  </div>
                ))}

                {filteredCommands.length === 0 && (
                  <div className="p-8 text-center">
                    <p className="text-sm text-zinc-400">No results found</p>
                    <p className="text-xs text-zinc-500 mt-1">Try a different search term</p>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="p-3 border-t border-[var(--border-subtle)] flex items-center justify-between text-xs text-zinc-400">
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <kbd className="px-1.5 py-0.5 bg-white/10 rounded">↑↓</kbd>
                    Navigate
                  </span>
                  <span className="flex items-center gap-1">
                    <kbd className="px-1.5 py-0.5 bg-white/10 rounded">↵</kbd>
                    Select
                  </span>
                </div>
                <span className="flex items-center gap-1">
                  <CommandLineIcon className="h-3 w-3" />
                  Command Palette
                </span>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

// Hook to use command palette
export function useCommandPalette() {
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault()
        setIsOpen(true)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  return {
    isOpen,
    setIsOpen,
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
    toggle: () => setIsOpen(prev => !prev)
  }
}

export { CommandPalette }