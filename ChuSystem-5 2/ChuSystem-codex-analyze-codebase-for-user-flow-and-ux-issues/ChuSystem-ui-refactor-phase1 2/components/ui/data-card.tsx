import type { ReactNode } from "react"
import { cn } from "@/lib/utils"
import { Card } from "./card" // Use the refactored design-system Card
import { Badge } from "./badge" // Use the refactored design-system Badge
import GlassTooltip from "./glass-tooltip" // Import the GlassTooltip

interface DataCardProps {
  title: string
  value?: string | number
  icon?: ReactNode
  trend?: {
    value: number // Percentage or absolute value
    label?: string // e.g., "vs last month"
    isPositive?: boolean // Determines color (green for positive, red for negative)
  }
  footer?: ReactNode
  className?: string
  valueClassName?: string
  iconContainerClassName?: string // Specific class for icon container
  variant?: "default" | "elevated" | "glass" | "clay" | "gradient" // Card variants from design-system/card.tsx
  badgeText?: string
  // Ensure BadgeColor matches the one in design-system/badge.tsx
  badgeColor?: "primary" | "success" | "warning" | "danger" | "purple" | "amber" | "default"
  children?: ReactNode
  actions?: ReactNode // For buttons or other interactive elements
  tooltipText?: string // Changed from `tooltip` to `tooltipText` to avoid conflict with potential HTML attributes
}

const DataCard = ({
  title,
  value,
  icon,
  trend,
  footer,
  className,
  valueClassName,
  iconContainerClassName,
  variant = "glass", // Default to 'glass' variant for card-modern look
  badgeText,
  badgeColor = "default",
  children,
  actions,
  tooltipText,
}: DataCardProps) => {
  const cardVariant = variant === "glass" ? "glass" : variant // Ensure 'glass' uses the glass variant from Card component
  
  return (
    <Card className={cn("h-full flex flex-col", className)} variant={cardVariant} padding="lg">
      <div className="flex items-start justify-between mb-4"> {/* Increased mb for spacing */}
        <div className="space-y-1.5"> {/* Increased space-y for title/value */}
          <div className="flex items-center gap-2">
            <h3 className="text-base font-medium text-zinc-300">{title}</h3> {/* Themed title */}
            {tooltipText && (
              <GlassTooltip content={tooltipText} side="top">
                <span className="text-xs text-zinc-500 cursor-help">ⓘ</span>
              </GlassTooltip>
            )}
            {badgeText && (
              // Using the refactored Badge component
              <Badge color={badgeColor} size="sm"> 
                {badgeText}
              </Badge>
            )}
          </div>
          {value !== undefined && ( // Check for undefined if value can be 0
            <p className={cn("text-4xl font-bold text-white", valueClassName)}>{value}</p> // Themed value
          )}
        </div>
        <div className="flex items-center gap-2">
          {actions}
          {icon && (
            <div
              className={cn(
                "flex-shrink-0 w-12 h-12 flex items-center justify-center rounded-2xl", // More rounded
                "bg-[var(--bg-glass-hover)] border border-[var(--border-subtle)] text-zinc-300", // Themed icon container
                iconContainerClassName,
              )}
            >
              {icon}
            </div>
          )}
        </div>
      </div>

      {trend && (
        <div className="flex items-center gap-2 mb-4"> {/* Increased mb */}
          <div
            className={cn(
              "inline-flex items-center gap-1 px-2.5 py-1 rounded-lg text-xs font-semibold", // Slightly larger padding
              trend.isPositive
                ? "bg-[rgba(var(--color-green),0.15)] text-[var(--color-green)]" // Themed positive trend
                : "bg-[rgba(var(--color-red),0.15)] text-[var(--color-red)]", // Themed negative trend
            )}
          >
            <svg
              width="14" // Adjusted size
              height="14"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className={cn("transform", trend.isPositive ? "rotate-0" : "rotate-180")}
            >
              {/* Simplified trend arrow, or use Heroicons if preferred */}
              <path d="M8 5.5L11.5 9.5L4.5 9.5L8 5.5Z" fill="currentColor" />
            </svg>
            <span>{Math.abs(trend.value)}%</span>
          </div>
          {trend.label && <span className="text-xs text-zinc-400">{trend.label}</span>} {/* Themed label */}
        </div>
      )}

      {children && <div className="flex-1 mb-4">{children}</div>} {/* Added mb to content if footer exists */}
      
      {footer && <div className="mt-auto pt-4 border-t border-[var(--border-subtle)] text-sm text-zinc-400">{footer}</div>} {/* Themed footer */}
    </Card>
  )
}

// Export as named export
export { DataCard }

// Also export as default
export default DataCard
