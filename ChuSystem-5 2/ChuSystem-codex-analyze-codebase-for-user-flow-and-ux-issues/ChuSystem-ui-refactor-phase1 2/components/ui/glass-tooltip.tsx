"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import * as TooltipPrimitive from "./tooltip" // Use relative import since both are in ui folder

interface GlassTooltipProps extends React.ComponentPropsWithoutRef<typeof TooltipPrimitive.TooltipContent> {
  content: React.ReactNode
  children: React.ReactNode
  delayDuration?: number
  // position, delay, contentClassName are implicitly handled by TooltipPrimitive.TooltipContent props (side, sideOffset, delayDuration, className)
}

const GlassTooltip = ({
  children,
  content,
  className,
  delayDuration = 200, // Default delay, shadcn's default is 700ms, this is closer to the original 300ms
  side = "top", // Default side
  sideOffset = 8, // Default offset (original had 8px)
  ...props
}: GlassTooltipProps) => {
  if (!content) {
    return <>{children}</> // Render children directly if no content for tooltip
  }

  return (
    <TooltipPrimitive.TooltipProvider delayDuration={delayDuration}>
      <TooltipPrimitive.Tooltip>
        <TooltipPrimitive.TooltipTrigger asChild>{children}</TooltipPrimitive.TooltipTrigger>
        <TooltipPrimitive.TooltipContent
          side={side}
          sideOffset={sideOffset}
          className={cn(
            "z-[1000] max-w-xs pointer-events-none", // Ensure high z-index and pointer-events none
            "px-3 py-2 rounded-xl text-sm text-white shadow-xl", // Padding, rounded corners, text style
            "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)]", // Glass effect
            // Animations (using shadcn's default, can be customized if needed)
            "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
            "data-[side=bottom]:slide-in-from-top-1 data-[side=left]:slide-in-from-right-1 data-[side=right]:slide-in-from-left-1 data-[side=top]:slide-in-from-bottom-1",
            className, // Allow overriding from props
          )}
          {...props}
        >
          {content}
        </TooltipPrimitive.TooltipContent>
      </TooltipPrimitive.Tooltip>
    </TooltipPrimitive.TooltipProvider>
  )
}

// Re-exporting other useful parts from shadcn/ui's tooltip for more advanced direct use if needed
export const TooltipProvider = TooltipPrimitive.TooltipProvider
export const TooltipRoot = TooltipPrimitive.Tooltip // Exporting Root as TooltipRoot to avoid conflict if 'Tooltip' is used as main component
export const TooltipTrigger = TooltipPrimitive.TooltipTrigger
export const TooltipContent = TooltipPrimitive.TooltipContent

export default GlassTooltip
