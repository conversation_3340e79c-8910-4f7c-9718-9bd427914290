"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronDownIcon, XMarkIcon } from "@heroicons/react/24/outline"

interface QuickGlanceCardProps {
  title: string
  subtitle?: string
  icon?: React.ReactNode
  preview: React.ReactNode
  expandedContent: React.ReactNode
  stats?: Array<{
    label: string
    value: string | number
    trend?: "up" | "down" | "neutral"
  }>
  actions?: Array<{
    label: string
    onClick: () => void
    variant?: "primary" | "secondary"
  }>
  className?: string
  defaultExpanded?: boolean
}

export default function QuickGlanceCard({
  title,
  subtitle,
  icon,
  preview,
  expandedContent,
  stats,
  actions,
  className,
  defaultExpanded = false
}: QuickGlanceCardProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      layout
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)]",
        "rounded-xl overflow-hidden transition-all duration-300",
        isExpanded ? "shadow-glow-xl" : "shadow-glow-sm hover:shadow-glow-lg",
        className
      )}
    >
      {/* Header - Always Visible */}
      <div 
        className={cn(
          "p-4 cursor-pointer transition-colors",
          isHovered && !isExpanded && "bg-white/5"
        )}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            {icon && (
              <div className="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center flex-shrink-0">
                {icon}
              </div>
            )}
            <div className="flex-1">
              <h3 className="text-sm font-medium">{title}</h3>
              {subtitle && (
                <p className="text-xs text-zinc-400 mt-0.5">{subtitle}</p>
              )}
            </div>
          </div>
          <motion.button
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
            className="p-1 rounded-lg hover:bg-white/10 transition-colors"
          >
            <ChevronDownIcon className="h-4 w-4 text-zinc-400" />
          </motion.button>
        </div>

        {/* Preview Content - Shown when collapsed */}
        {!isExpanded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="mt-3"
          >
            {preview}
          </motion.div>
        )}

        {/* Quick Stats - Always visible if provided */}
        {stats && stats.length > 0 && (
          <div className={cn(
            "grid gap-2 mt-3",
            stats.length === 2 ? "grid-cols-2" : "grid-cols-3"
          )}>
            {stats.map((stat, idx) => (
              <div key={idx} className="text-center p-2 bg-white/5 rounded-lg">
                <p className="text-xs text-zinc-400">{stat.label}</p>
                <p className="text-sm font-medium mt-0.5">
                  {stat.value}
                  {stat.trend && (
                    <span className={cn(
                      "ml-1 text-xs",
                      stat.trend === "up" && "text-green-400",
                      stat.trend === "down" && "text-red-400",
                      stat.trend === "neutral" && "text-zinc-400"
                    )}>
                      {stat.trend === "up" ? "↑" : stat.trend === "down" ? "↓" : "→"}
                    </span>
                  )}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Expanded Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            <div className="px-4 pb-4 border-t border-[var(--border-subtle)]">
              <div className="pt-4">
                {expandedContent}
              </div>

              {/* Action Buttons */}
              {actions && actions.length > 0 && (
                <div className="flex gap-2 mt-4 pt-4 border-t border-[var(--border-subtle)]">
                  {actions.map((action, idx) => (
                    <button
                      key={idx}
                      onClick={(e) => {
                        e.stopPropagation()
                        action.onClick()
                      }}
                      className={cn(
                        "px-3 py-1.5 rounded-lg text-sm font-medium transition-colors",
                        action.variant === "primary"
                          ? "bg-[var(--color-primary)] hover:bg-[var(--color-primary-hover)] text-black"
                          : "bg-white/5 hover:bg-white/10"
                      )}
                    >
                      {action.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

// Preset Quick Glance Cards for common use cases
export function ServiceQuickGlance({ service }: { service: any }) {
  return (
    <QuickGlanceCard
      title={service.name}
      subtitle={service.date}
      icon={<span className="text-lg">🎵</span>}
      preview={
        <div className="flex items-center justify-between text-xs">
          <span className="text-zinc-400">Team: {service.teamCount}/{service.teamNeeded}</span>
          <span className={cn(
            "px-2 py-0.5 rounded-full",
            service.status === "ready" ? "bg-green-500/20 text-green-400" : "bg-amber-500/20 text-amber-400"
          )}>
            {service.status}
          </span>
        </div>
      }
      expandedContent={
        <div className="space-y-3">
          <div>
            <p className="text-xs text-zinc-400 mb-1">Service Elements</p>
            <div className="space-y-1">
              {service.elements?.map((element: any, idx: number) => (
                <div key={idx} className="flex items-center justify-between p-2 bg-white/5 rounded">
                  <span className="text-sm">{element.name}</span>
                  <span className="text-xs text-zinc-400">{element.duration}</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <p className="text-xs text-zinc-400 mb-1">Team Members</p>
            <div className="flex -space-x-2">
              {service.team?.slice(0, 5).map((member: any, idx: number) => (
                <img
                  key={idx}
                  src={member.avatar}
                  alt={member.name}
                  className="w-8 h-8 rounded-full border-2 border-[var(--bg-glass)]"
                />
              ))}
              {service.team?.length > 5 && (
                <div className="w-8 h-8 rounded-full bg-white/10 border-2 border-[var(--bg-glass)] flex items-center justify-center">
                  <span className="text-xs">+{service.team.length - 5}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      }
      stats={[
        { label: "Duration", value: service.duration || "1h 30m" },
        { label: "Songs", value: service.songCount || 5 }
      ]}
      actions={[
        { label: "View Details", onClick: () => {}, variant: "primary" },
        { label: "Edit", onClick: () => {} }
      ]}
    />
  )
}

export function PersonQuickGlance({ person }: { person: any }) {
  return (
    <QuickGlanceCard
      title={person.name}
      subtitle={person.role}
      icon={
        <img 
          src={person.avatar || "/placeholder-user.jpg"} 
          alt={person.name}
          className="w-10 h-10 rounded-full object-cover"
        />
      }
      preview={
        <div className="flex items-center gap-3 text-xs">
          <span className="text-zinc-400">Member since {person.joinDate}</span>
          <span className="text-zinc-400">•</span>
          <span className={cn(
            person.status === "active" ? "text-green-400" : "text-zinc-400"
          )}>
            {person.status}
          </span>
        </div>
      }
      expandedContent={
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <div>
              <p className="text-xs text-zinc-400">Email</p>
              <p className="text-sm">{person.email}</p>
            </div>
            <div>
              <p className="text-xs text-zinc-400">Phone</p>
              <p className="text-sm">{person.phone}</p>
            </div>
          </div>
          <div>
            <p className="text-xs text-zinc-400 mb-1">Groups</p>
            <div className="flex flex-wrap gap-1">
              {person.groups?.map((group: string, idx: number) => (
                <span 
                  key={idx}
                  className="px-2 py-0.5 bg-white/10 rounded-full text-xs"
                >
                  {group}
                </span>
              ))}
            </div>
          </div>
          <div>
            <p className="text-xs text-zinc-400 mb-1">Recent Activity</p>
            <div className="space-y-1">
              {person.recentActivity?.map((activity: any, idx: number) => (
                <div key={idx} className="flex items-center justify-between text-xs">
                  <span>{activity.type}</span>
                  <span className="text-zinc-400">{activity.date}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      }
      stats={[
        { label: "Attendance", value: person.attendanceRate || "92%", trend: "up" },
        { label: "Giving", value: person.givingStatus || "Active" },
        { label: "Volunteer", value: person.volunteerHours || "24h" }
      ]}
      actions={[
        { label: "View Profile", onClick: () => {}, variant: "primary" },
        { label: "Send Message", onClick: () => {} },
        { label: "Add Note", onClick: () => {} }
      ]}
    />
  )
}

export { QuickGlanceCard }