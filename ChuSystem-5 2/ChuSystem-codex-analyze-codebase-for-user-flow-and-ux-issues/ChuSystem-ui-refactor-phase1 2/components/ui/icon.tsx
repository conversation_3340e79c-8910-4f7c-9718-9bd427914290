import type React from "react"
import { forwardRef } from "react"
import { cn } from "@/lib/utils"
import * as LucideIcons from "lucide-react"
import * as HeroiconsSolid from "@heroicons/react/16/solid"
import * as HeroiconsOutline from "@heroicons/react/24/outline"

export type IconSource = "lucide" | "heroicons-solid" | "heroicons-outline"

export interface IconProps extends React.HTMLAttributes<HTMLSpanElement> {
  name: string
  source?: IconSource
  size?: "xs" | "sm" | "md" | "lg" | "xl" | number
  color?: "primary" | "secondary" | "tertiary" | "brand" | string
  strokeWidth?: number
}

const sizeMap = {
  xs: 16,
  sm: 18,
  md: 20,
  lg: 24,
  xl: 32,
}

const colorMap = {
  primary: "text-white",
  secondary: "text-zinc-400",
  tertiary: "text-zinc-500",
  brand: "text-blue-500",
}

const Icon = forwardRef<HTMLSpanElement, IconProps>(
  ({ name, source = "lucide", size = "md", color = "secondary", strokeWidth = 2, className, ...props }, ref) => {
    // Convert size to pixels if it's a string
    const pixelSize = typeof size === "string" ? sizeMap[size] : size

    // Convert color to Tailwind class if it's a predefined color
    const colorClass = colorMap[color] || color

    // Find the appropriate icon component
    let IconComponent: React.ElementType | null = null

    if (source === "lucide") {
      IconComponent = LucideIcons[name as keyof typeof LucideIcons]
    } else if (source === "heroicons-solid") {
      IconComponent = HeroiconsSolid[name as keyof typeof HeroiconsSolid]
    } else if (source === "heroicons-outline") {
      IconComponent = HeroiconsOutline[name as keyof typeof HeroiconsOutline]
    }

    if (!IconComponent) {
      console.warn(`Icon "${name}" not found in source "${source}"`)
      return null
    }

    return (
      <span ref={ref} className={cn("inline-flex", colorClass, className)} {...props}>
        <IconComponent width={pixelSize} height={pixelSize} strokeWidth={strokeWidth} />
      </span>
    )
  },
)

Icon.displayName = "Icon"

export { Icon }
