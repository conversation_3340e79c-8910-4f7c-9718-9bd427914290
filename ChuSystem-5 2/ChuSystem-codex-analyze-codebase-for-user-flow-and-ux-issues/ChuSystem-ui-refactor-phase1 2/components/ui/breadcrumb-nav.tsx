"use client"

import React from "react"
import Link from "next/link"
import { ChevronRightIcon, HomeIcon } from "@heroicons/react/24/outline"
import { cn } from "@/lib/utils"

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
  current?: boolean
}

interface BreadcrumbNavProps {
  items: BreadcrumbItem[]
  showHome?: boolean
  className?: string
}

const BreadcrumbNav: React.FC<BreadcrumbNavProps> = ({
  items,
  showHome = true,
  className
}) => {
  const allItems = showHome 
    ? [{ label: "Home", href: "/", icon: HomeIcon }, ...items]
    : items

  return (
    <nav className={cn("flex items-center space-x-1", className)} aria-label="Breadcrumb">
      {allItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRightIcon className="h-3 w-3 text-zinc-500 mx-1" />
          )}
          
          <div className="flex items-center">
            {item.icon && (
              <item.icon className="h-4 w-4 mr-1.5 text-zinc-400" />
            )}
            
            {item.href && !item.current ? (
              <Link
                href={item.href}
                className="text-zinc-400 hover:text-zinc-300 transition-colors text-sm font-medium"
              >
                {item.label}
              </Link>
            ) : (
              <span className={cn(
                "text-sm font-medium",
                item.current ? "text-white" : "text-zinc-300"
              )}>
                {item.label}
              </span>
            )}
          </div>
        </React.Fragment>
      ))}
    </nav>
  )
}

export { BreadcrumbNav }
export default BreadcrumbNav