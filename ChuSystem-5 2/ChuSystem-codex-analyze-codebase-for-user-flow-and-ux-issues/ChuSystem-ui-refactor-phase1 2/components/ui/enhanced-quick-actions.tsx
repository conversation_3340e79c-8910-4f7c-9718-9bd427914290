"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { useActions } from "@/lib/actions-context"
import ExpandableToolButton from "./expandable-tool-button"
import { XMarkIcon, RocketLaunchIcon, SparklesIcon } from "@heroicons/react/24/outline"

interface QuickActionStat {
  label: string
  value: string | number
  subtext?: string
  trend?: "up" | "down" | "neutral"
}

interface EnhancedQuickAction {
  id: string
  icon: React.ReactNode
  label: string
  onClick: () => void
  color?: string
  href?: string
  shortcut?: string
  stats?: QuickActionStat[]
  description?: string
  quickInfo?: React.ReactNode
}

export default function EnhancedQuickActions() {
  const { actions } = useActions()
  const [isOpen, setIsOpen] = useState(false)
  const [expandedAction, setExpandedAction] = useState<string | null>(null)

  // Transform actions into enhanced actions with quick info
  const enhancedActions: EnhancedQuickAction[] = actions.map(action => {
    // Add context-specific stats and quick info based on action ID
    let stats: QuickActionStat[] = []
    let quickInfo: React.ReactNode = null
    let description = ""

    switch (action.id) {
      case "new-service":
        stats = [
          { label: "Next Service", value: "Sunday 10am" },
          { label: "Team Status", value: "15/18", subtext: "confirmed" }
        ]
        description = "Create a new worship service"
        quickInfo = (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-zinc-400">Recent Services</span>
              <span className="text-xs text-[var(--color-primary)]">View all</span>
            </div>
            <div className="space-y-1">
              <div className="p-2 bg-white/5 rounded hover:bg-white/10 transition-colors cursor-pointer">
                <p className="text-xs font-medium">Sunday Morning</p>
                <p className="text-[10px] text-zinc-400">Last Sunday • 423 attended</p>
              </div>
              <div className="p-2 bg-white/5 rounded hover:bg-white/10 transition-colors cursor-pointer">
                <p className="text-xs font-medium">Wednesday Night</p>
                <p className="text-[10px] text-zinc-400">Yesterday • 156 attended</p>
              </div>
            </div>
          </div>
        )
        break
      
      case "add-person":
        stats = [
          { label: "New This Month", value: "47", trend: "up" },
          { label: "Total Members", value: "1,234" }
        ]
        description = "Add a new member or visitor"
        quickInfo = (
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2 text-xs">
              <button className="p-2 bg-white/5 rounded hover:bg-white/10 transition-colors">
                <p className="font-medium">Quick Add</p>
                <p className="text-[10px] text-zinc-400">Basic info only</p>
              </button>
              <button className="p-2 bg-white/5 rounded hover:bg-white/10 transition-colors">
                <p className="font-medium">Full Profile</p>
                <p className="text-[10px] text-zinc-400">All details</p>
              </button>
            </div>
            <button className="w-full p-2 bg-[var(--color-primary)]/20 rounded hover:bg-[var(--color-primary)]/30 transition-colors text-xs font-medium">
              Import from CSV
            </button>
          </div>
        )
        break
      
      case "schedule-event":
        stats = [
          { label: "This Week", value: "8 events" },
          { label: "Registrations", value: "234", trend: "up" }
        ]
        description = "Schedule a new event"
        quickInfo = (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-zinc-400">Upcoming Events</span>
              <span className="text-xs text-[var(--color-primary)]">Calendar</span>
            </div>
            <div className="space-y-1 text-xs">
              <div className="flex items-center justify-between p-1">
                <span>Youth Night</span>
                <span className="text-[10px] text-zinc-400">Tomorrow</span>
              </div>
              <div className="flex items-center justify-between p-1">
                <span>Men's Breakfast</span>
                <span className="text-[10px] text-zinc-400">Saturday</span>
              </div>
            </div>
          </div>
        )
        break
      
      case "record-donation":
        stats = [
          { label: "Today", value: "$3,450", trend: "up" },
          { label: "This Month", value: "$45.2K" }
        ]
        description = "Record a new donation"
        quickInfo = (
          <div className="space-y-2">
            <div className="grid grid-cols-3 gap-1 text-center">
              <button className="p-2 bg-white/5 rounded hover:bg-white/10 transition-colors">
                <p className="text-sm font-medium">Cash</p>
              </button>
              <button className="p-2 bg-white/5 rounded hover:bg-white/10 transition-colors">
                <p className="text-sm font-medium">Check</p>
              </button>
              <button className="p-2 bg-white/5 rounded hover:bg-white/10 transition-colors">
                <p className="text-sm font-medium">Online</p>
              </button>
            </div>
            <div className="p-2 bg-amber-500/20 rounded">
              <p className="text-xs font-medium">Quick Tip</p>
              <p className="text-[10px] text-zinc-300">Use batch entry for multiple donations</p>
            </div>
          </div>
        )
        break

      case "add-song":
        stats = [
          { label: "Library Size", value: "1,245 songs" },
          { label: "This Sunday", value: "5 selected" }
        ]
        description = "Add a song to the library"
        quickInfo = (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-zinc-400">Popular This Month</span>
              <span className="text-xs text-[var(--color-primary)]">Browse</span>
            </div>
            <div className="space-y-1">
              <div className="flex items-center justify-between p-1 text-xs">
                <span>Way Maker</span>
                <span className="text-[10px] text-zinc-400">12 times</span>
              </div>
              <div className="flex items-center justify-between p-1 text-xs">
                <span>Goodness of God</span>
                <span className="text-[10px] text-zinc-400">10 times</span>
              </div>
            </div>
            <button className="w-full p-2 bg-purple-500/20 rounded hover:bg-purple-500/30 transition-colors text-xs font-medium">
              Import from CCLI
            </button>
          </div>
        )
        break
      
      default:
        description = action.label
    }

    return {
      ...action,
      stats,
      description,
      quickInfo
    }
  })

  return (
    <>
      {/* Floating Action Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={cn(
          "fixed bottom-6 right-6 w-14 h-14 rounded-2xl",
          "bg-[var(--color-primary)] hover:bg-[var(--color-primary-hover)]",
          "shadow-glow-lg hover:shadow-glow-xl",
          "flex items-center justify-center",
          "transition-all duration-200",
          "z-40"
        )}
      >
        <RocketLaunchIcon className="h-6 w-6 text-black" />
      </motion.button>

      {/* Quick Actions Panel */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => {
                setIsOpen(false)
                setExpandedAction(null)
              }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            />

            {/* Actions Grid */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: "spring", damping: 20 }}
              className="fixed bottom-24 right-6 z-50"
            >
              <div className="bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)] rounded-2xl shadow-glow-2xl p-4 w-96">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <SparklesIcon className="h-5 w-5 text-[var(--color-primary)]" />
                    <h3 className="text-sm font-medium">Quick Actions</h3>
                  </div>
                  <button
                    onClick={() => {
                      setIsOpen(false)
                      setExpandedAction(null)
                    }}
                    className="p-1 rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Search */}
                <input
                  type="text"
                  placeholder="Search actions..."
                  className="w-full px-3 py-2 mb-4 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                />

                {/* Actions Grid */}
                <div className="grid grid-cols-3 gap-3">
                  {enhancedActions.slice(0, 9).map((action) => (
                    <div key={action.id} className="relative">
                      <ExpandableToolButton
                        icon={action.icon}
                        label={action.label}
                        onClick={() => {
                          if (action.stats || action.quickInfo) {
                            setExpandedAction(expandedAction === action.id ? null : action.id)
                          } else {
                            action.onClick()
                            setIsOpen(false)
                          }
                        }}
                        variant="default"
                        size="lg"
                        badge={action.stats?.[0]?.value?.toString()}
                        expandedContent={
                          expandedAction === action.id && (
                            <div className="w-64">
                              {/* Description */}
                              <p className="text-xs text-zinc-400 mb-3">{action.description}</p>
                              
                              {/* Stats */}
                              {action.stats && action.stats.length > 0 && (
                                <div className="space-y-2 mb-3">
                                  {action.stats.map((stat, idx) => (
                                    <div key={idx} className="flex items-center justify-between">
                                      <span className="text-xs text-zinc-400">{stat.label}</span>
                                      <div className="flex items-center gap-1">
                                        <span className="text-sm font-medium">{stat.value}</span>
                                        {stat.trend && (
                                          <span className={cn(
                                            "text-xs",
                                            stat.trend === "up" && "text-green-400",
                                            stat.trend === "down" && "text-red-400"
                                          )}>
                                            {stat.trend === "up" ? "↑" : "↓"}
                                          </span>
                                        )}
                                        {stat.subtext && (
                                          <span className="text-[10px] text-zinc-500">{stat.subtext}</span>
                                        )}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                              
                              {/* Quick Info */}
                              {action.quickInfo}
                              
                              {/* Action Button */}
                              <button
                                onClick={() => {
                                  action.onClick()
                                  setIsOpen(false)
                                }}
                                className="w-full mt-3 px-3 py-2 bg-[var(--color-primary)]/20 hover:bg-[var(--color-primary)]/30 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2"
                              >
                                {action.label}
                                {action.shortcut && (
                                  <span className="text-xs text-zinc-400">{action.shortcut}</span>
                                )}
                              </button>
                            </div>
                          )
                        }
                        className="w-full"
                      />
                      
                      {/* Label */}
                      <p className="mt-2 text-xs text-center text-zinc-400 truncate">
                        {action.label}
                      </p>
                    </div>
                  ))}
                </div>

                {/* View All */}
                {enhancedActions.length > 9 && (
                  <button className="w-full mt-4 p-2 bg-white/5 hover:bg-white/10 rounded-lg text-sm font-medium transition-colors">
                    View All Actions ({enhancedActions.length})
                  </button>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

export { EnhancedQuickActions }