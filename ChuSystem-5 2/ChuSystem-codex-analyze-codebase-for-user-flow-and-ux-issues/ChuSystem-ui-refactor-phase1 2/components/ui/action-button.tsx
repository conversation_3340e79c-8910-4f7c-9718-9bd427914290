"use client"

import type React from "react"
import { forwardRef } from "react"
import { cn } from "@/lib/utils"
import { Button, type ButtonProps } from "../ui/button"

interface ActionButtonProps extends ButtonProps {
  icon?: React.ReactNode
  label: string
  description?: string
  onClick?: () => void
}

const ActionButton = forwardRef<HTMLButtonElement, ActionButtonProps>(
  ({ className, icon, label, description, variant = "secondary", size = "md", onClick, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        variant={variant}
        size={size}
        onClick={onClick}
        className={cn(
          "action-button", // Use global action-button style
          "w-full justify-start text-left", // Ensure full width and text alignment
          "py-3 px-4", // Adjust padding as needed
          className,
        )}
        {...props}
      >
        {icon && (
          <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-xl bg-[var(--bg-glass)] border border-[var(--border-subtle)]">
            {icon}
          </div>
        )}
        <div className="flex-1">
          <div className="font-medium text-white">{label}</div>
          {description && <div className="text-xs text-zinc-400">{description}</div>}
        </div>
      </Button>
    )
  },
)

ActionButton.displayName = "ActionButton"

// Export as default
export default ActionButton

// Also export as named export
export { ActionButton }
