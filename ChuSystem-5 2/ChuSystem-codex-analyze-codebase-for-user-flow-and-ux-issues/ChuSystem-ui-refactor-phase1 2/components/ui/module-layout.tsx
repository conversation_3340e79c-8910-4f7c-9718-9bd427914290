import type { ReactNode } from "react"
import Image from "next/image" // Import next/image
import SidebarModern from "../sidebar-modern"
import RightPanelModern from "../right-panel-modern"
import { cn } from "@/lib/utils"
import { MagnifyingGlassIcon, AdjustmentsHorizontalIcon, ShareIcon } from "@heroicons/react/24/outline"

interface ModuleLayoutProps {
  children: ReactNode
  title?: ReactNode
  subtitle?: ReactNode
  breadcrumbs?: ReactNode
  searchPlaceholder?: string
  showSearch?: boolean
  showRightPanel?: boolean
  actions?: ReactNode
  sidebarActions?: any[]
  topBarActions?: ReactNode
  className?: string
  bottomBarContent?: ReactNode
  headerMode?: "default" | "compact" | "search-focused" | "title-focused"
  showShareButton?: boolean
}

function ModuleLayout({
  children,
  title,
  subtitle,
  breadcrumbs,
  searchPlaceholder = "Search...",
  showSearch = true,
  showRightPanel = false,
  actions,
  sidebarActions,
  topBarActions,
  className,
  bottomBarContent,
  headerMode = "default",
  showShareButton = true,
}: ModuleLayoutProps) {
  return (
    <div className={cn("layout-modern flex", className)}>
      {/* Modern Sidebar */}
      <SidebarModern />

      {/* Main Content */}
      <main className="main-content-modern relative flex-1 ml-80 min-h-screen overflow-hidden"> {/* Fixed sidebar offset and overflow */}
        {/* Dynamic Top Bar */}
        <div className="sticky top-0 z-10 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)]">
          {/* Search Bar - Always visible when showSearch is true */}
          {showSearch && (
            <div className={cn(
              "border-b border-[var(--border-subtle)]/50 px-4 py-3 transition-all duration-200",
              headerMode === "search-focused" && "py-4 bg-[var(--bg-glass-hover)]"
            )}>
              <div className={cn(
                "search-modern transition-all duration-200",
                headerMode === "search-focused" ? "max-w-2xl" : "max-w-md"
              )}>
                <MagnifyingGlassIcon className="w-5 h-5 text-zinc-400" />
                <input
                  type="text"
                  placeholder={searchPlaceholder}
                  className="flex-1 bg-transparent placeholder:text-zinc-500 focus:outline-none"
                />
                <button className="icon-button rounded-xl">
                  <AdjustmentsHorizontalIcon className="w-4 h-4 text-zinc-400" />
                </button>
              </div>
            </div>
          )}
          
          {/* Header Content */}
          <div className={cn(
            "flex items-center justify-between p-4 transition-all duration-200",
            headerMode === "compact" && "py-2",
            headerMode === "search-focused" && showSearch && "pt-2",
            headerMode === "title-focused" && "py-6"
          )}>
            {/* Title Section */}
            <div className="flex-1">
              {/* Breadcrumbs */}
              {breadcrumbs && (
                <div className="flex items-center text-sm text-zinc-400 mb-1">
                  {breadcrumbs}
                </div>
              )}
              
              {/* Title */}
              {title && (
                <div className={cn(
                  "flex items-center gap-2 transition-all duration-200",
                  headerMode === "compact" ? "text-lg font-semibold" : "text-2xl font-bold",
                  headerMode === "search-focused" && "text-xl font-semibold",
                  headerMode === "title-focused" && "text-3xl font-bold"
                )}>
                  {title}
                </div>
              )}
              
              {/* Subtitle */}
              {subtitle && (
                <div className="text-sm text-zinc-400 mt-1">
                  {subtitle}
                </div>
              )}
            </div>

            {/* Actions Section */}
            <div className={cn(
              "flex items-center gap-3 ml-4 transition-all duration-200",
              headerMode === "compact" && "gap-2",
              headerMode === "title-focused" && "gap-4 ml-6"
            )}>
              {/* Custom Actions */}
              {topBarActions || actions}

              {/* Share Button */}
              {showShareButton && (
                <button className="action-button">
                  <ShareIcon className="w-5 h-5" />
                  <span>Share</span>
                </button>
              )}

              {/* User Profile */}
              <div className="flex items-center gap-3 pl-4 border-l border-[var(--border-subtle)]">
                <div className="text-right">
                  <p className="text-sm font-medium">Emily King</p>
                  <p className="text-xs text-zinc-400">Admin</p>
                </div>
                <Image
                  src="/avatar1.png"
                  alt="User"
                  width={40}
                  height={40}
                  className="rounded-full object-cover border border-[var(--border-subtle)]"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className={cn(
          "overflow-auto custom-scrollbar animate-fade-in",
          headerMode === "compact" ? "h-[calc(100vh-80px)]" : "h-[calc(100vh-120px)]",
          showSearch && "h-[calc(100vh-160px)]"
        )}>
          {children}
        </div>
        
        {/* Bottom Bar Content */}
        {bottomBarContent}
      </main>

      {/* Right Panel */}
      {showRightPanel && <RightPanelModern />}
    </div>
  )
}

// Export as named export
export { ModuleLayout }

// Also export as default
export default ModuleLayout
