"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check, Minus } from "lucide-react"
import { cn } from "@/lib/utils"

interface GlassCheckboxProps
  extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> {
  variant?: "default" | "glass" | "compact"
  size?: "sm" | "md" | "lg"
  label?: string
  description?: string
  indeterminate?: boolean
}

const GlassCheckbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  GlassCheckboxProps
>(({ 
  className, 
  variant = "glass",
  size = "md",
  label,
  description,
  indeterminate = false,
  ...props 
}, ref) => {
  const variants = {
    default: "bg-[var(--bg-card)] border border-[var(--border-subtle)]",
    glass: "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] shadow-glass",
    compact: "bg-[var(--bg-glass)] border border-[var(--border-subtle)]"
  }

  const sizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  }

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4", 
    lg: "h-5 w-5"
  }

  const checkbox = (
    <CheckboxPrimitive.Root
      ref={ref}
      className={cn(
        "peer shrink-0 rounded-lg transition-all duration-200",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--color-primary)]/20",
        "hover:bg-[var(--bg-glass-hover)] hover:border-[var(--border-light)]",
        "disabled:cursor-not-allowed disabled:opacity-50",
        "data-[state=checked]:bg-[rgba(var(--color-primary),0.2)] data-[state=checked]:border-[rgba(var(--color-primary),0.5)]",
        "data-[state=indeterminate]:bg-[rgba(var(--color-primary),0.2)] data-[state=indeterminate]:border-[rgba(var(--color-primary),0.5)]",
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        className={cn("flex items-center justify-center text-[rgba(var(--color-primary),1)]")}
      >
        {indeterminate ? (
          <Minus className={iconSizes[size]} />
        ) : (
          <Check className={iconSizes[size]} />
        )}
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  )

  if (label || description) {
    return (
      <div className="flex items-start space-x-3">
        {checkbox}
        <div className="grid gap-1.5 leading-none">
          {label && (
            <label
              htmlFor={props.id}
              className="text-sm font-medium text-white leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
            >
              {label}
            </label>
          )}
          {description && (
            <p className="text-xs text-zinc-400">
              {description}
            </p>
          )}
        </div>
      </div>
    )
  }

  return checkbox
})

GlassCheckbox.displayName = CheckboxPrimitive.Root.displayName

export { GlassCheckbox }
export default GlassCheckbox