"use client"

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommandCenter from "@/components/ui/floating-command-center"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface LayoutMode {
  id: string
  label: string
  icon: React.ComponentType<any>
  badge?: string | number
  pulse?: boolean
}

interface QuickStat {
  label: string
  value: string | number
  trend?: {
    value: number
    positive: boolean
  }
  icon?: React.ComponentType<any>
  color?: string
}

interface UnifiedLayoutProps {
  // Product info
  productName: string
  
  // Layout modes for this product
  layoutModes: LayoutMode[]
  currentMode: string
  onModeChange: (mode: string) => void
  
  // Top bar actions
  topBarActions?: React.ReactNode
  
  // Stats bar (optional)
  showStatsBar?: boolean
  quickStats?: QuickStat[]
  
  // Main content
  children: React.ReactNode
  
  // Floating panels
  floatingPanels?: React.ReactNode
  
  // Bottom dock (optional)
  showBottomDock?: boolean
  dockItems?: any[]
  onDockItemClick?: (item: any) => void
  
  // Live mode (optional)
  showLiveToggle?: boolean
  liveMode?: boolean
  onLiveModeToggle?: () => void
  
  // Right panel (optional)
  rightPanel?: React.ReactNode
  rightPanelOpen?: boolean
  
  // Custom class names
  className?: string
}

export default function UnifiedLayout({
  productName,
  layoutModes,
  currentMode,
  onModeChange,
  topBarActions,
  showStatsBar = true,
  quickStats = [],
  children,
  floatingPanels,
  showBottomDock = false,
  dockItems = [],
  onDockItemClick,
  showLiveToggle = false,
  liveMode = false,
  onLiveModeToggle,
  rightPanel,
  rightPanelOpen = false,
  className
}: UnifiedLayoutProps) {
  const [time, setTime] = useState(new Date())
  
  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className={cn("flex h-screen bg-black overflow-hidden", className)}>
      {/* Sidebar - Always present */}
      <SidebarRevolutionary />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Navigation Bar - Sticky */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] sticky top-0 z-30">
          <div className="h-full px-6 flex items-center justify-between">
            {/* Product Name & Layout Modes */}
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">{productName}</h1>
              
              {/* Layout Mode Switcher */}
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {layoutModes.map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => onModeChange(mode.id)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded transition-all relative",
                      currentMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <mode.icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{mode.label}</span>
                    {mode.badge && (
                      <Badge 
                        variant="secondary" 
                        size="sm"
                        className={mode.pulse ? "animate-pulse" : ""}
                      >
                        {mode.badge}
                      </Badge>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Top Bar Actions */}
            <div className="flex items-center gap-3">
              {/* Live Mode Toggle */}
              {showLiveToggle && (
                <button
                  onClick={onLiveModeToggle}
                  className={cn(
                    "px-3 py-1.5 rounded-lg flex items-center gap-2 transition-all",
                    liveMode 
                      ? "bg-green-500/20 text-green-400 border border-green-500/30" 
                      : "bg-white/5 hover:bg-white/10"
                  )}
                >
                  {liveMode && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
                  <span className="text-sm font-medium">{liveMode ? "Live" : "Live Off"}</span>
                </button>
              )}
              
              {/* Custom Actions */}
              {topBarActions}
              
              {/* Time Display */}
              <div className="text-sm font-mono text-zinc-400">
                {time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          </div>
        </div>

        {/* Stats Bar - Optional */}
        {showStatsBar && quickStats.length > 0 && (
          <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center">
            <div className={cn(
              "grid gap-6 w-full",
              `grid-cols-${Math.min(quickStats.length, 6)}`
            )}>
              {quickStats.map((stat, idx) => (
                <div key={idx} className="text-center">
                  <div className="flex items-center justify-center gap-2">
                    {stat.icon && <stat.icon className={cn("h-5 w-5", stat.color)} />}
                    <p className={cn("text-2xl font-bold", stat.color)}>
                      {stat.value}
                    </p>
                  </div>
                  <p className="text-xs text-zinc-400">{stat.label}</p>
                  {stat.trend && (
                    <div className={cn(
                      "text-xs mt-1",
                      stat.trend.positive ? "text-green-400" : "text-red-400"
                    )}>
                      {stat.trend.positive ? "+" : ""}{stat.trend.value}%
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Main Content Area with Optional Right Panel */}
        <div className="flex-1 flex relative overflow-hidden">
          {/* Primary Content */}
          <div className={cn(
            "flex-1 relative",
            rightPanelOpen && "mr-80"
          )}>
            {children}
          </div>

          {/* Right Panel - Optional */}
          <AnimatePresence>
            {rightPanelOpen && rightPanel && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 320, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                className="absolute right-0 top-0 bottom-0 bg-[var(--bg-glass)] backdrop-blur-xl border-l border-[var(--border-subtle)] overflow-hidden"
              >
                <div className="w-80 h-full">
                  {rightPanel}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Bottom Dock - Restored to original working design */}
        {showBottomDock && dockItems.length > 0 && (
          <div className="fixed bottom-6 left-1/2 -translate-x-1/2 z-50">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)] rounded-2xl p-2 shadow-2xl"
            >
              <div className="flex items-center gap-2">
                {dockItems.slice(0, 7).map((item, idx) => (
                  <motion.button
                    key={item.id || idx}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => onDockItemClick?.(item)}
                    className={cn(
                      "relative w-12 h-12 rounded-xl flex items-center justify-center",
                      "bg-white/5 hover:bg-white/10 transition-all",
                      "border border-white/5 hover:border-white/10"
                    )}
                  >
                    <item.icon className="h-5 w-5 text-white/80" />
                    {item.badge && (
                      <Badge
                        variant="secondary"
                        size="sm"
                        className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-[10px]"
                      >
                        {item.badge}
                      </Badge>
                    )}
                  </motion.button>
                ))}
                
                {dockItems.length > 7 && (
                  <div className="w-px h-8 bg-white/10 mx-1" />
                )}
                
                {dockItems.length > 7 && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-8 h-8 rounded-lg bg-white/5 hover:bg-white/10 flex items-center justify-center transition-all"
                  >
                    <span className="text-xs text-white/60">+{dockItems.length - 7}</span>
                  </motion.button>
                )}
              </div>
            </motion.div>
          </div>
        )}
      </div>

      {/* Floating Panels */}
      {floatingPanels}

      {/* Floating Command Center - Always present */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}