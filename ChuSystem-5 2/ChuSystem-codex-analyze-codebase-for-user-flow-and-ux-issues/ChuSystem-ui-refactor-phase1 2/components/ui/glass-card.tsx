"use client"

import React from "react"
import { cn } from "@/lib/utils"

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "elevated" | "subtle" | "glow"
  blur?: "sm" | "md" | "lg" | "xl"
  opacity?: "low" | "medium" | "high"
  border?: boolean
  shadow?: "none" | "sm" | "md" | "lg" | "glow"
  gradient?: boolean
  children: React.ReactNode
}

const GlassCard = React.forwardRef<HTMLDivElement, GlassCardProps>(
  ({ 
    className, 
    variant = "default",
    blur = "md",
    opacity = "medium",
    border = true,
    shadow = "md",
    gradient = false,
    children,
    ...props 
  }, ref) => {
    const baseStyles = "relative overflow-hidden transition-all duration-300"
    
    const variantStyles = {
      default: "bg-[var(--bg-glass)] backdrop-blur-md",
      elevated: "bg-[var(--bg-glass-elevated)] backdrop-blur-lg",
      subtle: "bg-[var(--bg-glass-subtle)] backdrop-blur-sm",
      glow: "bg-[var(--bg-glass)] backdrop-blur-md border-[var(--color-primary)]/20"
    }
    
    const blurStyles = {
      sm: "backdrop-blur-sm",
      md: "backdrop-blur-md", 
      lg: "backdrop-blur-lg",
      xl: "backdrop-blur-xl"
    }
    
    const shadowStyles = {
      none: "",
      sm: "shadow-sm",
      md: "shadow-lg shadow-black/10",
      lg: "shadow-xl shadow-black/20",
      glow: "shadow-2xl shadow-[var(--color-primary)]/10"
    }

    return (
      <div
        ref={ref}
        className={cn(
          baseStyles,
          variantStyles[variant],
          blurStyles[blur],
          shadowStyles[shadow],
          border && "border border-[var(--border-subtle)]",
          gradient && "bg-gradient-to-br from-white/5 to-white/2",
          variant === "glow" && "ring-1 ring-[var(--color-primary)]/10",
          "rounded-xl",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

GlassCard.displayName = "GlassCard"

export { GlassCard }
