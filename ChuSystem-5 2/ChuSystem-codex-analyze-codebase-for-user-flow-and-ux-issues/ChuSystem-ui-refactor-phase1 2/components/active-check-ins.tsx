import { ClockIcon, UserIcon, MapPinIcon, CheckIcon } from "@heroicons/react/24/outline"

export default function ActiveCheckIns() {
  const checkIns = [
    {
      id: 1,
      name: "<PERSON>",
      time: "9:15 AM",
      location: "Main Sanctuary",
      status: "Checked In",
    },
    {
      id: 2,
      name: "<PERSON>",
      time: "9:22 AM",
      location: "Children's Ministry",
      status: "Checked In",
    },
    {
      id: 3,
      name: "<PERSON>",
      time: "9:25 AM",
      location: "Youth Center",
      status: "Checked In",
    },
    {
      id: 4,
      name: "<PERSON>",
      time: "9:30 AM",
      location: "Main Sanctuary",
      status: "Checked In",
    },
    {
      id: 5,
      name: "<PERSON>",
      time: "9:32 AM",
      location: "Children's Ministry",
      status: "Checked In",
    },
  ]

  return (
    <div className="bg-zinc-900/90 rounded-xl border border-zinc-800/20 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-zinc-800/50 border-b border-zinc-700/30">
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Name</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Time</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Location</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400">Status</th>
            </tr>
          </thead>
          <tbody>
            {checkIns.map((checkIn) => (
              <tr key={checkIn.id} className="border-b border-zinc-800/30 hover:bg-zinc-800/20 transition-colors">
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <UserIcon className="h-4 w-4 mr-2 text-zinc-500" />
                    <span>{checkIn.name}</span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-2 text-zinc-500" />
                    <span>{checkIn.time}</span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <MapPinIcon className="h-4 w-4 mr-2 text-zinc-500" />
                    <span>{checkIn.location}</span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <CheckIcon className="h-4 w-4 mr-2 text-green-500" />
                    <span className="text-green-500">{checkIn.status}</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
