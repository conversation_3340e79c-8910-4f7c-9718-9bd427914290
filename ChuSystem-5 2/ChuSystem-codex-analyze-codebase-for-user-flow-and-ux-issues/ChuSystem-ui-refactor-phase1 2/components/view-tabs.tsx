"use client"

import { TabGroup } from "@/components/ui/tab-group"
import { FunnelIcon } from "@heroicons/react/16/solid"

export default function ViewTabs() {
  const viewTabs = [
    {
      name: "Blocks",
      content: null, // This will be rendered separately
    },
    {
      name: "Table",
      content: null,
    },
    {
      name: "Calendar",
      content: null,
    },
  ]

  return (
    <div className="flex items-center mb-6">
      <TabGroup
        tabs={viewTabs}
        className="w-auto"
        tabClassName="bg-zinc-900/30 border border-zinc-800/40 hover:bg-zinc-800/30"
      />

      <div className="ml-auto">
        <button className="bg-zinc-800/80 hover:bg-zinc-800 transition-all flex items-center gap-2 px-4 py-1.5 rounded-xl text-sm border border-zinc-700/20 shadow-sm hover:shadow-md">
          <FunnelIcon className="size-4 fill-white/60" />
          Filters
          <span className="badge-circle bg-zinc-700">2</span>
        </button>
      </div>
    </div>
  )
}
