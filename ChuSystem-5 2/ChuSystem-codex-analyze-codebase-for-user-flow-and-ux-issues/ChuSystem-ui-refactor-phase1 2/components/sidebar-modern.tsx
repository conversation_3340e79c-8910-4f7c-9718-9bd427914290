"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  HomeIcon,
  UsersIcon,
  CalendarIcon,
  FolderIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  SparklesIcon,
  Cog6ToothIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  MusicalNoteIcon,
  VideoCameraIcon,
  GlobeAltIcon,
  RocketLaunchIcon,
} from "@heroicons/react/24/outline"
import { cn } from "@/lib/utils"
import Image from "next/image"
import OrganizationSwitcher from "./ui/organization-switcher"
import EnhancedProductSwitcher from "./ui/enhanced-product-switcher"

const navigation = [
  { name: "Dashboard", href: "/", icon: HomeIcon },
  { name: "People", href: "/people", icon: UsersIcon },
  { name: "Services", href: "/services", icon: CalendarIcon },
  { name: "Events", href: "/events", icon: SparklesIcon },
  { name: "Groups", href: "/groups", icon: UserGroupIcon },
  { name: "Giving", href: "/giving", icon: CurrencyDollarIcon },
  { name: "Ministries", href: "/ministries", icon: FolderIcon },
  { name: "Publish", href: "/publish", icon: GlobeAltIcon, badge: "NEW" },
  { name: "Check-in", href: "/check-in", icon: UsersIcon },
  { name: "Settings", href: "/settings", icon: Cog6ToothIcon },
]

interface Contact {
  id: string
  name: string
  role: string
  avatar: string
  status: "active" | "inactive"
}

export default function SidebarModern() {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  const contacts: Contact[] = [
    { id: "1", name: "Gregory Clark", role: "Manager", avatar: "/avatar1.png", status: "active" },
    { id: "2", name: "George Allen", role: "Broker", avatar: "/avatar2.png", status: "active" },
    { id: "3", name: "Megan Brooks", role: "Analyst", avatar: "/avatar3.png", status: "active" },
    { id: "4", name: "Logan Smith", role: "Manager", avatar: "/avatar4.png", status: "active" },
  ]

  const filteredContacts = contacts.filter(
    (contact) =>
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.role.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <div
      className={cn(
        "flex flex-col h-screen bg-[var(--bg-sidebar)] backdrop-blur-2xl border-r border-[var(--border-subtle)] transition-all duration-300 fixed left-0 top-0 z-30",
        isCollapsed ? "w-20" : "w-80",
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-[var(--border-subtle)]">
        <div className="flex items-center justify-between">
          <div className={cn("flex items-center gap-3", isCollapsed && "justify-center")}>
            <Image src="/orbital-logo.png" alt="Orbital" width={32} height={32} className="rounded-lg" />
            {!isCollapsed && <span className="text-lg font-semibold">Orbital Church</span>}
          </div>
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1.5 rounded-xl hover:bg-white/10 transition-colors icon-button"
          >
            {isCollapsed ? <ChevronRightIcon className="h-5 w-5" /> : <ChevronLeftIcon className="h-5 w-5" />}
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-3 space-y-1" aria-label="Main navigation">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "sidebar-item-modern rounded-xl",
                isActive && "sidebar-item-modern-active",
                isCollapsed && "justify-center",
              )}
            >
              <item.icon className="h-5 w-5 flex-shrink-0" />
              {!isCollapsed && (
                <div className="flex items-center justify-between flex-1">
                  <span className="text-sm font-medium">{item.name}</span>
                  {item.badge && (
                    <span className="px-2 py-0.5 text-xs font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full">
                      {item.badge}
                    </span>
                  )}
                </div>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Contacts Section */}
      {!isCollapsed && (
        <div className="border-t border-[var(--border-subtle)] p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-zinc-400">All Contacts</h3>
            <span className="text-xs text-zinc-500">{contacts.length}</span>
          </div>

          {/* Search */}
          <div className="relative mb-3">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-zinc-500" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search contacts..."
              className="input-modern"
            />
          </div>

          {/* New Contact Button */}
          <button className="w-full flex items-center gap-2 px-3 py-2 mb-3 rounded-xl transition-colors action-button">
            <div className="w-8 h-8 rounded-full bg-white/5 flex items-center justify-center">
              <PlusIcon className="h-4 w-4" />
            </div>
            <span className="text-sm font-medium">New contact</span>
          </button>

          {/* Contact List */}
          <div className="space-y-1 max-h-64 overflow-y-auto custom-scrollbar">
            {filteredContacts.map((contact) => (
              <button
                key={contact.id}
                className="w-full flex items-center gap-3 px-3 py-2 rounded-xl text-left contact-item"
              >
                <Image
                  src={contact.avatar || "/placeholder.svg"}
                  alt={contact.name}
                  width={32} // Corresponds to w-8
                  height={32} // Corresponds to h-8
                  className="rounded-full object-cover" // Added object-cover
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{contact.name}</p>
                  <p className="text-xs text-zinc-500 truncate">{contact.role}</p>
                </div>
              </button>
            ))}
          </div>

          {/* Favorites */}
          <div className="mt-4 pt-4 border-t border-[var(--border-subtle)]">
            <button className="w-full flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-white/5 transition-colors">
              <div className="w-8 h-8 rounded-full bg-white/5 flex items-center justify-center">
                <span className="text-xs font-medium">12</span>
              </div>
              <span className="text-sm font-medium">Favorites contact</span>
            </button>
          </div>
        </div>
      )}

      {/* Organization Switcher */}
      <OrganizationSwitcher 
        isCollapsed={isCollapsed}
        onOrganizationChange={(org) => console.log("Switched to:", org.name)}
        onAddOrganization={() => console.log("Add new organization")}
      />
      
      {/* Enhanced Product Switcher - At bottom like workspace switcher */}
      <EnhancedProductSwitcher isCollapsed={isCollapsed} className="absolute bottom-0 left-0 right-0" />
    </div>
  )
}
