import { cn } from "@/lib/utils"
import Avatar from "./design-system/avatar"

interface TeamAvatarsProps {
  avatars: string[]
  size?: "xs" | "sm" | "md" | "lg"
  max?: number
  className?: string
  showCount?: boolean
}

export default function TeamAvatars({
  avatars,
  size = "md",
  max = 4,
  className = "",
  showCount = true,
}: TeamAvatarsProps) {
  const sizeStyles = {
    xs: "size-6",
    sm: "size-8",
    md: "size-10",
    lg: "size-12",
  }

  const offsetStyles = {
    xs: "-ml-1.5",
    sm: "-ml-2",
    md: "-ml-2.5",
    lg: "-ml-3",
  }

  const countStyles = {
    xs: "text-[10px] w-6 h-6",
    sm: "text-xs w-8 h-8",
    md: "text-sm w-10 h-10",
    lg: "text-base w-12 h-12",
  }

  const displayAvatars = avatars.slice(0, max)
  const remainingCount = avatars.length > max ? avatars.length - max : 0

  return (
    <div className={cn("flex", className)}>
      {displayAvatars.map((avatar, index) => (
        <div key={index} className={cn(index !== 0 && offsetStyles[size], "relative z-10")}>
          <Avatar src={avatar} alt={`Team Member ${index + 1}`} size={size} ring={true} />
        </div>
      ))}

      {remainingCount > 0 && showCount && (
        <div
          className={cn(
            "flex items-center justify-center bg-zinc-800 rounded-full text-zinc-300 font-medium",
            sizeStyles[size],
            offsetStyles[size],
            countStyles[size],
            "border-2 border-black",
          )}
        >
          +{remainingCount}
        </div>
      )}
    </div>
  )
}
