"use client"

import { useState, useCallback } from "react"
import { Brush, Box, Activity } from "lucide-react"
import { DndProvider } from "react-dnd"
import { HTML5Backend } from "react-dnd-html5-backend"
import MonthControls from "./month-controls"
import AddCard from "./add-card"
import DraggableTaskCard from "./draggable-task-card"
import DropZone from "./drop-zone"

// Define the task type
export interface Task {
  id: string
  title: string
  color: string
  icon?: any
  avatars: string[]
}

// Define the day type
export interface Day {
  id: string
  day: string
  date?: string
  dateHighlight?: string
  highlight?: string
  tasks: Task[]
}

export default function ProjectCalendar() {
  const [month, setMonth] = useState("October, 2022")

  // Initialize weekdays with tasks
  const [weekdays, setWeekdays] = useState<Day[]>([
    {
      id: "mon",
      day: "Mon",
      highlight: "red",
      tasks: [
        {
          id: "task-1",
          title: "New Brief",
          color: "bg-orange-400",
          icon: null,
          avatars: [],
        },
      ],
    },
    {
      id: "tue",
      day: "Tue",
      date: "19",
      dateHighlight: "blue",
      tasks: [
        {
          id: "task-2",
          title: "Meeting on 1:00 PM",
          color: "bg-green-500",
          icon: null,
          avatars: [],
        },
        {
          id: "task-3",
          title: "Vigo App",
          color: "bg-blue-500",
          icon: Activity,
          avatars: ["/avatar1.png", "/avatar2.png"],
        },
      ],
    },
    {
      id: "wed",
      day: "Wed",
      tasks: [],
    },
    {
      id: "thu",
      day: "Thu",
      tasks: [
        {
          id: "task-4",
          title: "3D NFT",
          color: "bg-zinc-800",
          icon: Box,
          avatars: [],
        },
        {
          id: "task-5",
          title: "Icon Set",
          color: "bg-blue-500",
          icon: Brush,
          avatars: ["/avatar1.png", "/avatar3.png"],
        },
        {
          id: "task-6",
          title: "Vigo App",
          color: "bg-zinc-800",
          icon: Activity,
          avatars: ["/avatar1.png", "/avatar2.png"],
        },
      ],
    },
    {
      id: "fri",
      day: "Fri",
      highlight: "red",
      tasks: [
        {
          id: "task-7",
          title: "New Brief",
          color: "bg-orange-400",
          icon: null,
          avatars: [],
        },
        {
          id: "task-8",
          title: "Astin Landing",
          color: "bg-zinc-800",
          icon: Activity,
          avatars: ["/avatar1.png", "/avatar3.png"],
        },
      ],
    },
    {
      id: "sat",
      day: "Sat",
      date: "4",
      dateHighlight: "blue",
      tasks: [
        {
          id: "task-9",
          title: "Meeting on 3:15 PM",
          color: "bg-green-500",
          icon: null,
          avatars: [],
        },
      ],
    },
    {
      id: "sun",
      day: "Sun",
      tasks: [],
    },
  ])

  const handlePrevMonth = () => {
    setMonth("September, 2022")
  }

  const handleNextMonth = () => {
    setMonth("November, 2022")
  }

  // Handle moving a task from one day to another
  const moveTask = useCallback((taskId: string, fromDayId: string, toDayId: string) => {
    setWeekdays((prevWeekdays) => {
      // Create a new array to avoid mutating state
      const newWeekdays = [...prevWeekdays]

      // Find the source and destination days
      const fromDayIndex = newWeekdays.findIndex((day) => day.id === fromDayId)
      const toDayIndex = newWeekdays.findIndex((day) => day.id === toDayId)

      if (fromDayIndex === -1 || toDayIndex === -1) return prevWeekdays

      // Find the task in the source day
      const taskIndex = newWeekdays[fromDayIndex].tasks.findIndex((task) => task.id === taskId)
      if (taskIndex === -1) return prevWeekdays

      // Remove the task from the source day
      const [task] = newWeekdays[fromDayIndex].tasks.splice(taskIndex, 1)

      // Add the task to the destination day
      newWeekdays[toDayIndex].tasks.push(task)

      return newWeekdays
    })
  }, [])

  return (
    <DndProvider backend={HTML5Backend}>
      <div>
        <MonthControls month={month} onPrevious={handlePrevMonth} onNext={handleNextMonth} />

        <div className="grid grid-cols-7 gap-4">
          {weekdays.map((day) => (
            <div key={day.id} className="flex flex-col space-y-4">
              {/* Day Header */}
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium">{day.day}</span>

                {day.date && (
                  <span
                    className={`w-5 h-5 rounded-full flex items-center justify-center text-xs ${
                      day.dateHighlight === "blue" ? "bg-blue-500 shadow-glow-blue" : ""
                    }`}
                  >
                    {day.date}
                  </span>
                )}

                {day.highlight === "red" && (
                  <span className="w-2 h-2 rounded-full bg-red-500 ml-auto shadow-glow-red"></span>
                )}
              </div>

              {/* Task Cards */}
              <DropZone dayId={day.id} onDrop={(taskId, fromDayId) => moveTask(taskId, fromDayId, day.id)}>
                <div className="space-y-4 min-h-[200px]">
                  {day.tasks.map((task) => (
                    <DraggableTaskCard
                      key={task.id}
                      id={task.id}
                      dayId={day.id}
                      title={task.title}
                      color={task.color}
                      icon={task.icon}
                      avatars={task.avatars}
                    />
                  ))}
                </div>
              </DropZone>

              {/* Add New Task Button */}
              <AddCard />
            </div>
          ))}
        </div>
      </div>
    </DndProvider>
  )
}
