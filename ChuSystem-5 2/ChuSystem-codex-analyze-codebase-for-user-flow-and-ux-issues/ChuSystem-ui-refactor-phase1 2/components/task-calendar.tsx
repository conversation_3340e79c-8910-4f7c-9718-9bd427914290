"use client"

import { useState } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight, Plus, Activity, Brush, CuboidIcon as Cube, Paintbrush } from "lucide-react"

export default function TaskCalendar() {
  const [currentMonth, setCurrentMonth] = useState("October, 2022")

  const days = [
    { day: "Mon", date: "", tasks: [{ title: "New Brief", color: "bg-orange-400", time: "" }] },
    { day: "Tue", date: "19", tasks: [{ title: "Meeting on 1:00 PM", color: "bg-green-500", time: "" }] },
    { day: "Wed", date: "", tasks: [] },
    {
      day: "Thu",
      date: "",
      tasks: [
        { title: "3D NFT", color: "bg-zinc-700", icon: Cube },
        {
          title: "Icon Set",
          color: "bg-blue-500",
          icon: Brush,
          users: ["/professional-headshot.png", "/professional-woman-headshot.png"],
        },
      ],
    },
    { day: "Fri", date: "", tasks: [{ title: "New Brief", color: "bg-orange-400", time: "" }] },
    { day: "Sat", date: "4", tasks: [{ title: "Meeting on 3:15 PM", color: "bg-green-500", time: "" }] },
    { day: "Sun", date: "", tasks: [] },
  ]

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <button className="p-2 rounded-full hover:bg-zinc-800">
          <ChevronLeft className="h-5 w-5" />
        </button>

        <div className="flex items-center gap-2">
          <h3 className="text-xl font-semibold">{currentMonth}</h3>
          <button className="p-1 bg-zinc-800 rounded-md">
            <Paintbrush className="h-4 w-4" />
          </button>
          <span className="text-xs text-gray-400">Edited 29m ago</span>
        </div>

        <button className="p-2 rounded-full hover:bg-zinc-800">
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>

      <div className="grid grid-cols-7 gap-4">
        {days.map((day, index) => (
          <div key={index} className="relative">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">{day.day}</span>
                {day.date && (
                  <span
                    className={`w-5 h-5 rounded-full flex items-center justify-center text-xs ${index === 1 || index === 5 ? "bg-blue-500" : ""}`}
                  >
                    {day.date}
                  </span>
                )}
                {index === 0 || index === 4 ? <span className="w-2 h-2 rounded-full bg-red-500"></span> : null}
              </div>
            </div>

            <div className="space-y-4">
              {day.tasks.map((task, taskIndex) => (
                <div
                  key={taskIndex}
                  className={`${task.color} rounded-lg p-3 h-24 flex flex-col ${taskIndex > 0 ? "mt-4" : ""}`}
                >
                  {task.icon ? <task.icon className="h-5 w-5 mb-2" /> : null}
                  <h4 className="text-sm font-medium">{task.title}</h4>
                  {task.users && (
                    <div className="flex -space-x-2 mt-auto">
                      {task.users.map((user, userIndex) => (
                        <Image
                          key={userIndex}
                          src={user || "/placeholder.svg"}
                          alt="User"
                          width={28}
                          height={28}
                          className="rounded-full border-2 border-blue-500"
                        />
                      ))}
                    </div>
                  )}
                </div>
              ))}

              {/* Empty cell with add button */}
              {day.tasks.length === 0 && (
                <div className="border border-dashed border-zinc-700 rounded-lg flex items-center justify-center h-24">
                  <button className="text-blue-400 flex items-center justify-center w-full h-full">
                    <span className="flex flex-col items-center">
                      <Plus className="h-5 w-5" />
                      <span className="text-xs mt-1">Add</span>
                    </span>
                  </button>
                </div>
              )}

              {/* Special case for day 3 (Wednesday) which has Vigo App */}
              {index === 2 && (
                <div className="border border-dashed border-zinc-700 rounded-lg flex items-center justify-center h-24 mt-4">
                  <button className="text-blue-400 flex items-center justify-center w-full h-full">
                    <span className="flex flex-col items-center">
                      <Plus className="h-5 w-5" />
                      <span className="text-xs mt-1">Add</span>
                    </span>
                  </button>
                </div>
              )}

              {/* Special case for Vigo App on Tuesday */}
              {index === 1 && (
                <div className="bg-blue-500 rounded-lg p-3 h-24 mt-4">
                  <Activity className="h-5 w-5 mb-2" />
                  <h4 className="text-sm font-medium">Vigo App</h4>
                  <div className="flex -space-x-2 mt-auto">
                    <Image
                      src="/professional-headshot.png"
                      alt="User"
                      width={28}
                      height={28}
                      className="rounded-full border-2 border-blue-500"
                    />
                    <Image
                      src="/professional-woman-headshot.png"
                      alt="User"
                      width={28}
                      height={28}
                      className="rounded-full border-2 border-blue-500"
                    />
                  </div>
                </div>
              )}

              {/* Special cases for Thursday's Vigo App */}
              {index === 3 && (
                <div className="bg-zinc-800 rounded-lg p-3 h-24 mt-4">
                  <Activity className="h-5 w-5 mb-2" />
                  <h4 className="text-sm font-medium">Vigo App</h4>
                  <div className="flex -space-x-2 mt-auto">
                    <Image
                      src="/professional-headshot.png"
                      alt="User"
                      width={28}
                      height={28}
                      className="rounded-full border-2 border-zinc-800"
                    />
                    <Image
                      src="/professional-woman-headshot.png"
                      alt="User"
                      width={28}
                      height={28}
                      className="rounded-full border-2 border-zinc-800"
                    />
                  </div>
                </div>
              )}

              {/* Astin Landing for Friday */}
              {index === 4 && (
                <div className="bg-zinc-800 rounded-lg p-3 h-24 mt-4">
                  <Activity className="h-5 w-5 mb-2" />
                  <h4 className="text-sm font-medium">Astin Landing</h4>
                  <div className="flex -space-x-2 mt-auto">
                    <Image
                      src="/professional-headshot.png"
                      alt="User"
                      width={28}
                      height={28}
                      className="rounded-full border-2 border-zinc-800"
                    />
                    <Image
                      src="/professional-woman-headshot.png"
                      alt="User"
                      width={28}
                      height={28}
                      className="rounded-full border-2 border-zinc-800"
                    />
                  </div>
                </div>
              )}

              {/* Add buttons for empty slots */}
              {(index === 5 || index === 6) && (
                <div className="border border-dashed border-zinc-700 rounded-lg flex items-center justify-center h-24 mt-4">
                  <button className="text-blue-400 flex items-center justify-center w-full h-full">
                    <span className="flex flex-col items-center">
                      <Plus className="h-5 w-5" />
                      <span className="text-xs mt-1">Add</span>
                    </span>
                  </button>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
