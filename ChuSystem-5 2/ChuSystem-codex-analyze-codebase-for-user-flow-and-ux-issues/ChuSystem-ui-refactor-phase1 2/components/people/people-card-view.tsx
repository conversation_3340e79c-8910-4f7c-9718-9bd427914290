"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { 
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  UserGroupIcon,
  HeartIcon,
  TagIcon,
  EllipsisVerticalIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon
} from "@heroicons/react/24/outline"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Image from "next/image"

interface Person {
  id: string
  name: string
  email: string
  phone: string
  avatar?: string
  status: "active" | "inactive" | "visitor" | "new"
  joinDate: string
  lastSeen: string
  groups: string[]
  tags: string[]
  address?: {
    street: string
    city: string
    state: string
    zip: string
  }
  engagement: {
    attendance: number // percentage
    giving: "active" | "inactive" | "occasional"
    volunteer: boolean
  }
  familyMembers?: string[]
}

interface PeopleCardViewProps {
  people: Person[]
  onPersonClick?: (person: Person) => void
  onSelectionChange?: (selectedIds: Set<string>) => void
  selectedIds?: Set<string>
  viewMode?: "compact" | "detailed"
}

export default function PeopleCardView({
  people,
  onPersonClick,
  onSelectionChange,
  selectedIds = new Set(),
  viewMode = "detailed"
}: PeopleCardViewProps) {
  const [hoveredId, setHoveredId] = useState<string | null>(null)

  const handleSelection = (personId: string, event: React.MouseEvent) => {
    if (event.metaKey || event.ctrlKey) {
      const newSelection = new Set(selectedIds)
      if (newSelection.has(personId)) {
        newSelection.delete(personId)
      } else {
        newSelection.add(personId)
      }
      onSelectionChange?.(newSelection)
    }
  }

  const getStatusIcon = (status: Person["status"]) => {
    switch (status) {
      case "active":
        return <CheckCircleIcon className="h-4 w-4 text-green-400" />
      case "inactive":
        return <ExclamationCircleIcon className="h-4 w-4 text-amber-400" />
      case "visitor":
        return <ClockIcon className="h-4 w-4 text-blue-400" />
      case "new":
        return <HeartIcon className="h-4 w-4 text-purple-400" />
    }
  }

  const getEngagementColor = (attendance: number) => {
    if (attendance >= 80) return "text-green-400"
    if (attendance >= 60) return "text-blue-400"
    if (attendance >= 40) return "text-amber-400"
    return "text-red-400"
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-6">
      {people.map((person, index) => (
        <motion.div
          key={person.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.05 }}
          onMouseEnter={() => setHoveredId(person.id)}
          onMouseLeave={() => setHoveredId(null)}
          onClick={(e) => handleSelection(person.id, e)}
          className={cn(
            "relative group cursor-pointer",
            selectedIds.has(person.id) && "ring-2 ring-[var(--color-primary)] rounded-xl"
          )}
        >
          <div className={cn(
            "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)]",
            "rounded-xl overflow-hidden transition-all duration-300",
            hoveredId === person.id && "transform scale-105 shadow-glow-lg"
          )}>
            {/* Header Section */}
            <div className="relative h-24 bg-gradient-to-br from-[var(--color-primary)]/20 to-[var(--color-primary)]/5">
              {/* Selection Checkbox */}
              <div className="absolute top-2 left-2">
                <input
                  type="checkbox"
                  checked={selectedIds.has(person.id)}
                  onChange={() => {}}
                  className="w-4 h-4 rounded"
                />
              </div>

              {/* Status Badge */}
              <div className="absolute top-2 right-2">
                {getStatusIcon(person.status)}
              </div>

              {/* Avatar */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                <div className="relative">
                  <Image
                    src={person.avatar || "/placeholder-user.jpg"}
                    alt={person.name}
                    width={64}
                    height={64}
                    className="rounded-full border-4 border-[var(--bg-glass)] object-cover"
                  />
                  {person.engagement.volunteer && (
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                      <HeartIcon className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="pt-10 px-4 pb-4">
              {/* Name and Basic Info */}
              <div className="text-center mb-3">
                <h3 className="font-medium text-lg">{person.name}</h3>
                <p className="text-xs text-zinc-400">Member since {person.joinDate}</p>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-2 mb-3">
                <div className="text-center p-2 bg-white/5 rounded-lg">
                  <p className={cn("text-lg font-bold", getEngagementColor(person.engagement.attendance))}>
                    {person.engagement.attendance}%
                  </p>
                  <p className="text-[10px] text-zinc-400">Attendance</p>
                </div>
                <div className="text-center p-2 bg-white/5 rounded-lg">
                  <p className="text-lg font-bold">
                    {person.groups.length}
                  </p>
                  <p className="text-[10px] text-zinc-400">Groups</p>
                </div>
                <div className="text-center p-2 bg-white/5 rounded-lg">
                  <p className={cn(
                    "text-lg font-bold",
                    person.engagement.giving === "active" ? "text-green-400" : "text-zinc-400"
                  )}>
                    {person.engagement.giving === "active" ? "✓" : "—"}
                  </p>
                  <p className="text-[10px] text-zinc-400">Giving</p>
                </div>
              </div>

              {/* Contact Info */}
              {viewMode === "detailed" && (
                <div className="space-y-2 mb-3">
                  <button className="w-full flex items-center gap-2 p-2 hover:bg-white/5 rounded-lg transition-colors text-left">
                    <EnvelopeIcon className="h-4 w-4 text-zinc-400" />
                    <span className="text-xs truncate">{person.email}</span>
                  </button>
                  <button className="w-full flex items-center gap-2 p-2 hover:bg-white/5 rounded-lg transition-colors text-left">
                    <PhoneIcon className="h-4 w-4 text-zinc-400" />
                    <span className="text-xs">{person.phone}</span>
                  </button>
                  {person.address && (
                    <div className="flex items-center gap-2 p-2 text-left">
                      <MapPinIcon className="h-4 w-4 text-zinc-400" />
                      <span className="text-xs text-zinc-400">
                        {person.address.city}, {person.address.state}
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Tags */}
              <div className="flex flex-wrap gap-1 mb-3">
                {person.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="secondary" size="sm">
                    {tag}
                  </Badge>
                ))}
                {person.tags.length > 3 && (
                  <Badge variant="secondary" size="sm">
                    +{person.tags.length - 3}
                  </Badge>
                )}
              </div>

              {/* Groups */}
              <div className="flex items-center gap-1 text-xs text-zinc-400">
                <UserGroupIcon className="h-3 w-3" />
                <span>{person.groups.join(", ")}</span>
              </div>

              {/* Family Members */}
              {person.familyMembers && person.familyMembers.length > 0 && (
                <div className="mt-3 pt-3 border-t border-[var(--border-subtle)]">
                  <p className="text-xs text-zinc-400 mb-1">Family</p>
                  <div className="flex -space-x-2">
                    {person.familyMembers.slice(0, 4).map((member, idx) => (
                      <div
                        key={idx}
                        className="w-6 h-6 rounded-full bg-white/10 border border-[var(--bg-glass)] flex items-center justify-center text-[10px]"
                      >
                        {member[0]}
                      </div>
                    ))}
                    {person.familyMembers.length > 4 && (
                      <div className="w-6 h-6 rounded-full bg-white/10 border border-[var(--bg-glass)] flex items-center justify-center text-[10px]">
                        +{person.familyMembers.length - 4}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Hover Actions */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: hoveredId === person.id ? 1 : 0, y: hoveredId === person.id ? 0 : 10 }}
                className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent"
              >
                <div className="flex gap-2">
                  <Button
                    variant="glass"
                    size="sm"
                    className="flex-1"
                    onClick={(e) => {
                      e.stopPropagation()
                      onPersonClick?.(person)
                    }}
                  >
                    View
                  </Button>
                  <Button variant="glass" size="sm">
                    <EnvelopeIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="glass" size="sm">
                    <PhoneIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="glass" size="sm">
                    <EllipsisVerticalIcon className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export { PeopleCardView }