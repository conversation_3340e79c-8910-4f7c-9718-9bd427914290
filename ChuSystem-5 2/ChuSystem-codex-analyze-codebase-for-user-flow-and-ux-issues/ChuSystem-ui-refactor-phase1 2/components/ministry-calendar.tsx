"use client"

import { useState, useCallback } from "react"
import { Music, Heart, BookOpen, Users } from "lucide-react"
import { DndProvider } from "react-dnd"
import { HTML5Backend } from "react-dnd-html5-backend"
import MonthControls from "./month-controls"
import AddCard from "./add-card"
import DraggableEventCard from "./draggable-event-card"
import DropZone from "./drop-zone"

// Define the event type
export interface Event {
  id: string
  title: string
  color: string
  icon?: any
  avatars: string[]
  time?: string
}

// Define the day type
export interface Day {
  id: string
  day: string
  date?: string
  dateHighlight?: string
  highlight?: string
  events: Event[]
}

export default function MinistryCalendar() {
  const [month, setMonth] = useState("June, 2025")

  // Initialize weekdays with events
  const [weekdays, setWeekdays] = useState<Day[]>([
    {
      id: "mon",
      day: "Mon",
      highlight: "red",
      events: [
        {
          id: "event-1",
          title: "Staff Meeting",
          color: "bg-orange-400",
          icon: null,
          avatars: [],
          time: "9:00 AM",
        },
      ],
    },
    {
      id: "tue",
      day: "Tue",
      date: "11",
      dateHighlight: "blue",
      events: [
        {
          id: "event-2",
          title: "Prayer Breakfast",
          color: "bg-green-500",
          icon: null,
          avatars: [],
          time: "7:30 AM",
        },
        {
          id: "event-3",
          title: "Worship Practice",
          color: "bg-blue-500",
          icon: Music,
          avatars: ["/avatar1.png", "/avatar2.png"],
          time: "6:30 PM",
        },
      ],
    },
    {
      id: "wed",
      day: "Wed",
      events: [
        {
          id: "event-4",
          title: "Bible Study",
          color: "bg-purple-500",
          icon: BookOpen,
          avatars: ["/avatar3.png"],
          time: "7:00 PM",
        },
      ],
    },
    {
      id: "thu",
      day: "Thu",
      events: [
        {
          id: "event-5",
          title: "Youth Group",
          color: "bg-zinc-800",
          icon: Users,
          avatars: [],
          time: "6:00 PM",
        },
        {
          id: "event-6",
          title: "Choir Practice",
          color: "bg-blue-500",
          icon: Music,
          avatars: ["/avatar1.png", "/avatar3.png"],
          time: "7:00 PM",
        },
      ],
    },
    {
      id: "fri",
      day: "Fri",
      highlight: "red",
      events: [
        {
          id: "event-7",
          title: "Outreach Planning",
          color: "bg-orange-400",
          icon: null,
          avatars: [],
          time: "5:30 PM",
        },
        {
          id: "event-8",
          title: "Community Dinner",
          color: "bg-zinc-800",
          icon: Heart,
          avatars: ["/avatar1.png", "/avatar3.png"],
          time: "6:30 PM",
        },
      ],
    },
    {
      id: "sat",
      day: "Sat",
      date: "15",
      dateHighlight: "blue",
      events: [
        {
          id: "event-9",
          title: "Men's Breakfast",
          color: "bg-green-500",
          icon: null,
          avatars: [],
          time: "8:00 AM",
        },
      ],
    },
    {
      id: "sun",
      day: "Sun",
      date: "16",
      dateHighlight: "blue",
      events: [
        {
          id: "event-10",
          title: "Sunday Service",
          color: "bg-blue-500",
          icon: null,
          avatars: [],
          time: "10:00 AM",
        },
        {
          id: "event-11",
          title: "Youth Service",
          color: "bg-purple-500",
          icon: Users,
          avatars: ["/avatar2.png", "/avatar4.png"],
          time: "6:00 PM",
        },
      ],
    },
  ])

  const handlePrevMonth = () => {
    setMonth("May, 2025")
  }

  const handleNextMonth = () => {
    setMonth("July, 2025")
  }

  // Handle moving an event from one day to another
  const moveEvent = useCallback((eventId: string, fromDayId: string, toDayId: string) => {
    setWeekdays((prevWeekdays) => {
      // Create a new array to avoid mutating state
      const newWeekdays = [...prevWeekdays]

      // Find the source and destination days
      const fromDayIndex = newWeekdays.findIndex((day) => day.id === fromDayId)
      const toDayIndex = newWeekdays.findIndex((day) => day.id === toDayId)

      if (fromDayIndex === -1 || toDayIndex === -1) return prevWeekdays

      // Find the event in the source day
      const eventIndex = newWeekdays[fromDayIndex].events.findIndex((event) => event.id === eventId)
      if (eventIndex === -1) return prevWeekdays

      // Remove the event from the source day
      const [event] = newWeekdays[fromDayIndex].events.splice(eventIndex, 1)

      // Add the event to the destination day
      newWeekdays[toDayIndex].events.push(event)

      return newWeekdays
    })
  }, [])

  return (
    <DndProvider backend={HTML5Backend}>
      <div>
        <MonthControls month={month} onPrevious={handlePrevMonth} onNext={handleNextMonth} />

        <div className="grid grid-cols-7 gap-4">
          {weekdays.map((day) => (
            <div key={day.id} className="flex flex-col space-y-4">
              {/* Day Header */}
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium">{day.day}</span>

                {day.date && (
                  <span
                    className={`w-5 h-5 rounded-full flex items-center justify-center text-xs ${
                      day.dateHighlight === "blue" ? "bg-blue-500 shadow-glow-blue" : ""
                    }`}
                  >
                    {day.date}
                  </span>
                )}

                {day.highlight === "red" && (
                  <span className="w-2 h-2 rounded-full bg-red-500 ml-auto shadow-glow-red"></span>
                )}
              </div>

              {/* Event Cards */}
              <DropZone dayId={day.id} onDrop={(eventId, fromDayId) => moveEvent(eventId, fromDayId, day.id)}>
                <div className="space-y-4 min-h-[200px]">
                  {day.events.map((event) => (
                    <DraggableEventCard
                      key={event.id}
                      id={event.id}
                      dayId={day.id}
                      title={event.title}
                      color={event.color}
                      icon={event.icon}
                      avatars={event.avatars}
                      time={event.time}
                    />
                  ))}
                </div>
              </DropZone>

              {/* Add New Event Button */}
              <AddCard text="Add Event" />
            </div>
          ))}
        </div>
      </div>
    </DndProvider>
  )
}
