"use client"

import type React from "react"

import { useDrop } from "react-dnd"
import { cn } from "@/lib/utils"

interface DropZoneProps {
  dayId: string
  onDrop: (eventId: string, fromDayId: string) => void
  children: React.ReactNode
  className?: string
}

export default function DropZone({ dayId, onDrop, children, className }: DropZoneProps) {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: "EVENT",
    drop: (item: { id: string; dayId: string }) => {
      if (item.dayId !== dayId) {
        onDrop(item.id, item.dayId)
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }))

  return (
    <div
      ref={drop}
      className={cn(
        "transition-colors rounded-xl",
        isOver && "bg-zinc-800/50 outline-dashed outline-2 outline-blue-500/50",
        className,
      )}
    >
      {children}
    </div>
  )
}
