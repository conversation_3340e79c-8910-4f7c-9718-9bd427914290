"use client"

import { useState } from "react"
import { SparklesIcon, XMarkIcon } from "@heroicons/react/16/solid"
import { But<PERSON> } from "./ui/button"
import Card from "./design-system/card"

export default function ChurchAssistant() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="px-4 py-3 border-b border-zinc-800/80">
      <Button
        variant="glass"
        size="md"
        className="w-full flex items-center justify-between bg-gradient-to-r from-blue-900/20 to-purple-900/20 border border-blue-500/20"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center">
          <SparklesIcon className="h-5 w-5 text-blue-400 mr-2" />
          <span>Church Assistant</span>
        </div>
        <div className="bg-blue-500/30 text-xs px-2 py-0.5 rounded-full">AI</div>
      </Button>

      {isOpen && (
        <Card
          variant="glass"
          className="mt-3 p-4 border border-blue-500/20 bg-gradient-to-r from-blue-900/10 to-purple-900/10"
        >
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center">
              <SparklesIcon className="h-5 w-5 text-blue-400 mr-2" />
              <h3 className="font-medium">Church Assistant</h3>
            </div>
            <Button variant="ghost" size="icon-sm" onClick={() => setIsOpen(false)} className="h-6 w-6">
              <XMarkIcon className="h-4 w-4" />
            </Button>
          </div>

          <p className="text-sm text-zinc-300 mb-3">
            How can I help you today? I can assist with service planning, team coordination, or answer questions about
            your church.
          </p>

          <div className="flex flex-col gap-2">
            <Button variant="secondary" size="sm" className="justify-start">
              <span>Help me plan Sunday's service</span>
            </Button>
            <Button variant="secondary" size="sm" className="justify-start">
              <span>Schedule a team meeting</span>
            </Button>
            <Button variant="secondary" size="sm" className="justify-start">
              <span>Generate sermon ideas</span>
            </Button>
          </div>
        </Card>
      )}
    </div>
  )
}
