"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
  Clock,
  Music,
  Mic,
  Video,
  MessageSquare,
  Users,
  BookOpen,
  DollarSign,
  ArrowRight,
  Plus,
  Trash2,
  GripVertical,
  ChevronDown,
  ChevronRight,
  AlertCircle,
  CheckCircle,
  Lock,
  Eye,
  Edit,
  MoreHorizontal,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import TeamAvatars from "@/components/team-avatars"
import GlassTooltip from "@/components/ui/glass-tooltip"

// Define service element types
type ElementType =
  | "worship"
  | "sermon"
  | "announcement"
  | "prayer"
  | "scripture"
  | "video"
  | "offering"
  | "transition"
  | "welcome"
  | "benediction"
  | "communion"
  | "baptism"
  | "testimony"
  | "drama"
  | "special"

interface ServiceElement {
  id: string
  type: ElementType
  title: string
  duration: string // in minutes:seconds format
  durationSeconds: number // duration in seconds for calculations
  startTime?: string // calculated start time
  endTime?: string // calculated end time
  assignedTo: string[] // array of avatar URLs
  notes?: string
  details?: any
  status?: "pending" | "ready" | "in-progress" | "completed" | "skipped"
  isLocked?: boolean
  isGroup?: boolean
  children?: ServiceElement[]
  parentId?: string
  cues?: {
    lighting?: string[]
    sound?: string[]
    video?: string[]
    stage?: string[]
  }
}

interface ServiceTimelineBuilderProps {
  initialElements?: ServiceElement[]
  onChange?: (elements: ServiceElement[]) => void
  isLiveMode?: boolean
  viewMode?: "all" | "worship" | "tech" | "speaking"
  onElementClick?: (element: ServiceElement) => void
  serviceStartTime?: string // e.g. "10:00 AM"
}

export default function ServiceTimelineBuilder({
  initialElements = [],
  onChange,
  isLiveMode = false,
  viewMode = "all",
  onElementClick,
  serviceStartTime = "10:00 AM",
}: ServiceTimelineBuilderProps) {
  const [elements, setElements] = useState<ServiceElement[]>(initialElements)
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({})
  const [activeElement, setActiveElement] = useState<string | null>(null)
  const [draggedElement, setDraggedElement] = useState<string | null>(null)
  const [showAddMenu, setShowAddMenu] = useState<boolean>(false)
  const [addMenuPosition, setAddMenuPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 })
  const timelineRef = useRef<HTMLDivElement>(null)
  const addMenuRef = useRef<HTMLDivElement>(null)

  // Calculate total duration and update start/end times
  useEffect(() => {
    let totalSeconds = 0
    const startTimeDate = parseTimeString(serviceStartTime)

    const updatedElements = elements.map((element) => {
      // Skip children as they'll be handled within their parent group
      if (element.parentId) return element

      const startSeconds = totalSeconds
      const elementDuration = element.durationSeconds || convertDurationToSeconds(element.duration)

      // Calculate start time
      const elementStartTime = new Date(startTimeDate)
      elementStartTime.setSeconds(elementStartTime.getSeconds() + startSeconds)
      const formattedStartTime = formatTimeString(elementStartTime)

      // Calculate end time
      const elementEndTime = new Date(elementStartTime)
      elementEndTime.setSeconds(elementEndTime.getSeconds() + elementDuration)
      const formattedEndTime = formatTimeString(elementEndTime)

      // Update children if this is a group
      let updatedChildren = element.children
      if (element.isGroup && element.children) {
        let childTotalSeconds = 0
        updatedChildren = element.children.map((child) => {
          const childStartSeconds = childTotalSeconds
          const childDuration = child.durationSeconds || convertDurationToSeconds(child.duration)

          // Calculate child start time relative to parent
          const childStartTime = new Date(elementStartTime)
          childStartTime.setSeconds(childStartTime.getSeconds() + childStartSeconds)
          const formattedChildStartTime = formatTimeString(childStartTime)

          // Calculate child end time
          const childEndTime = new Date(childStartTime)
          childEndTime.setSeconds(childEndTime.getSeconds() + childDuration)
          const formattedChildEndTime = formatTimeString(childEndTime)

          childTotalSeconds += childDuration

          return {
            ...child,
            startTime: formattedChildStartTime,
            endTime: formattedChildEndTime,
          }
        })
      }

      // Add this element's duration to the total
      totalSeconds += elementDuration

      return {
        ...element,
        startTime: formattedStartTime,
        endTime: formattedEndTime,
        children: updatedChildren,
      }
    })

    setElements(updatedElements)

    // Notify parent component of changes
    if (onChange) {
      onChange(updatedElements)
    }
  }, [elements.length, serviceStartTime])

  // Helper function to parse time string (e.g. "10:00 AM") to Date object
  const parseTimeString = (timeString: string): Date => {
    const date = new Date()
    const [time, period] = timeString.split(" ")
    let [hours, minutes] = time.split(":").map(Number)

    if (period === "PM" && hours < 12) {
      hours += 12
    } else if (period === "AM" && hours === 12) {
      hours = 0
    }

    date.setHours(hours, minutes, 0, 0)
    return date
  }

  // Helper function to format Date as time string (e.g. "10:00 AM")
  const formatTimeString = (date: Date): string => {
    let hours = date.getHours()
    const minutes = date.getMinutes().toString().padStart(2, "0")
    const period = hours >= 12 ? "PM" : "AM"

    if (hours > 12) {
      hours -= 12
    } else if (hours === 0) {
      hours = 12
    }

    return `${hours}:${minutes} ${period}`
  }

  // Helper function to convert duration string (e.g. "5:30") to seconds
  const convertDurationToSeconds = (duration: string): number => {
    const [minutes, seconds = "0"] = duration.split(":").map(Number)
    return minutes * 60 + seconds
  }

  // Helper function to convert seconds to duration string
  const convertSecondsToDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  // Get icon for element type
  const getElementIcon = (type: ElementType) => {
    switch (type) {
      case "worship":
        return <Music className="h-5 w-5 text-blue-400" />
      case "sermon":
        return <Mic className="h-5 w-5 text-green-400" />
      case "announcement":
        return <MessageSquare className="h-5 w-5 text-amber-400" />
      case "prayer":
        return <Users className="h-5 w-5 text-purple-400" />
      case "scripture":
        return <BookOpen className="h-5 w-5 text-cyan-400" />
      case "video":
        return <Video className="h-5 w-5 text-red-400" />
      case "offering":
        return <DollarSign className="h-5 w-5 text-emerald-400" />
      case "transition":
        return <ArrowRight className="h-5 w-5 text-zinc-400" />
      case "welcome":
        return <Users className="h-5 w-5 text-pink-400" />
      case "benediction":
        return <Users className="h-5 w-5 text-indigo-400" />
      case "communion":
        return <Users className="h-5 w-5 text-amber-400" />
      case "baptism":
        return <Users className="h-5 w-5 text-blue-400" />
      case "testimony":
        return <Mic className="h-5 w-5 text-violet-400" />
      case "drama":
        return <Users className="h-5 w-5 text-orange-400" />
      case "special":
        return <Music className="h-5 w-5 text-teal-400" />
      default:
        return <Clock className="h-5 w-5 text-zinc-400" />
    }
  }

  // Get status indicator for element
  const getStatusIndicator = (status?: ServiceElement["status"]) => {
    switch (status) {
      case "ready":
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case "in-progress":
        return (
          <div className="relative">
            <div className="absolute inset-0 bg-blue-400 rounded-full animate-ping opacity-50"></div>
            <div className="relative h-3 w-3 bg-blue-400 rounded-full"></div>
          </div>
        )
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-400 fill-green-400" />
      case "skipped":
        return <AlertCircle className="h-4 w-4 text-amber-400" />
      default:
        return null
    }
  }

  // Toggle group expansion
  const toggleGroup = (id: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  // Handle element click
  const handleElementClick = (element: ServiceElement) => {
    if (onElementClick) {
      onElementClick(element)
    }
    setActiveElement(element.id)
  }

  // Handle add element button click
  const handleAddElementClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const rect = timelineRef.current?.getBoundingClientRect()
    if (rect) {
      setAddMenuPosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      })
    }

    setShowAddMenu(true)
  }

  // Handle click outside add menu
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (addMenuRef.current && !addMenuRef.current.contains(e.target as Node)) {
        setShowAddMenu(false)
      }
    }

    if (showAddMenu) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [showAddMenu])

  // Add new element
  const addElement = (type: ElementType) => {
    const newElement: ServiceElement = {
      id: `element-${Date.now()}`,
      type,
      title: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      duration: "5:00",
      durationSeconds: 300,
      assignedTo: [],
    }

    setElements((prev) => [...prev, newElement])
    setShowAddMenu(false)
  }

  // Filter elements based on view mode
  const filteredElements = elements.filter((element) => {
    if (viewMode === "all") return true
    if (viewMode === "worship" && (element.type === "worship" || element.type === "music")) return true
    if (viewMode === "tech" && (element.type === "video" || element.type === "transition")) return true
    if (
      viewMode === "speaking" &&
      (element.type === "sermon" || element.type === "announcement" || element.type === "prayer")
    )
      return true
    return false
  })

  return (
    <div className="relative" ref={timelineRef}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 bg-zinc-800/30 rounded-xl text-sm mb-3">
        <div className="w-8"></div>
        <div className="flex-1 font-medium">Element</div>
        <div className="w-24 text-center">Start</div>
        <div className="w-24 text-center">Duration</div>
        <div className="w-32 text-center">Assigned To</div>
        <div className="w-24 text-center">Actions</div>
      </div>

      {/* Timeline */}
      <div className="space-y-2">
        {filteredElements.map((element) => (
          <div key={element.id}>
            {/* Main element */}
            <div
              className={`
                flex items-center p-3 rounded-xl transition-colors
                ${element.isLocked ? "bg-zinc-800/30 cursor-not-allowed" : "bg-zinc-800/50 hover:bg-zinc-800/70 cursor-pointer"}
                ${activeElement === element.id ? "ring-2 ring-blue-500/30" : ""}
                ${element.status === "in-progress" ? "bg-blue-900/10 border border-blue-500/20" : ""}
                ${element.status === "completed" ? "bg-green-900/10 border border-green-500/20" : ""}
              `}
              onClick={() => handleElementClick(element)}
            >
              {/* Drag handle or expand/collapse for groups */}
              <div className="w-8 flex justify-center">
                {element.isGroup ? (
                  <button
                    className="h-6 w-6 rounded-full hover:bg-zinc-700/50 flex items-center justify-center"
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleGroup(element.id)
                    }}
                  >
                    {expandedGroups[element.id] ? (
                      <ChevronDown className="h-4 w-4 text-zinc-400" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-zinc-400" />
                    )}
                  </button>
                ) : (
                  <div className="cursor-move">
                    <GripVertical className="h-5 w-5 text-zinc-500" />
                  </div>
                )}
              </div>

              {/* Element info */}
              <div className="flex-1 flex items-center">
                <div className="h-8 w-8 rounded-lg flex items-center justify-center mr-3">
                  {getElementIcon(element.type)}
                </div>
                <div className="min-w-0">
                  <div className="font-medium flex items-center">
                    {element.title}
                    {element.isLocked && <Lock className="h-3 w-3 text-zinc-500 ml-2" />}
                    {getStatusIndicator(element.status) && (
                      <span className="ml-2">{getStatusIndicator(element.status)}</span>
                    )}
                  </div>
                  {element.notes && <div className="text-xs text-zinc-400 mt-0.5 line-clamp-1">{element.notes}</div>}
                </div>
              </div>

              {/* Start time */}
              <div className="w-24 text-center">
                <span className="text-sm text-zinc-300">{element.startTime}</span>
              </div>

              {/* Duration */}
              <div className="w-24 text-center">
                <span className="px-2 py-1 bg-zinc-700/50 rounded-md text-sm">{element.duration}</span>
              </div>

              {/* Assigned team */}
              <div className="w-32 flex justify-center">
                {element.assignedTo.length > 0 ? (
                  <TeamAvatars avatars={element.assignedTo} size="sm" />
                ) : (
                  <span className="text-xs text-zinc-500">Unassigned</span>
                )}
              </div>

              {/* Actions */}
              <div className="w-24 flex justify-center space-x-1">
                {isLiveMode ? (
                  <>
                    {element.status !== "completed" && element.status !== "skipped" && (
                      <GlassTooltip content="Mark Complete" position="top">
                        <Button variant="ghost" size="icon" className="h-7 w-7 rounded-lg">
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                      </GlassTooltip>
                    )}
                    <GlassTooltip content="View Details" position="top">
                      <Button variant="ghost" size="icon" className="h-7 w-7 rounded-lg">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </GlassTooltip>
                  </>
                ) : (
                  <>
                    <GlassTooltip content="Edit" position="top">
                      <Button variant="ghost" size="icon" className="h-7 w-7 rounded-lg">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </GlassTooltip>
                    <GlassTooltip content="More Options" position="top">
                      <Button variant="ghost" size="icon" className="h-7 w-7 rounded-lg">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </GlassTooltip>
                  </>
                )}
              </div>
            </div>

            {/* Children elements (if group is expanded) */}
            {element.isGroup && expandedGroups[element.id] && element.children && (
              <div className="ml-8 pl-4 border-l border-zinc-700/30 mt-1 space-y-2">
                {element.children.map((child) => (
                  <div
                    key={child.id}
                    className={`
                      flex items-center p-2 rounded-xl transition-colors
                      ${child.isLocked ? "bg-zinc-800/30 cursor-not-allowed" : "bg-zinc-800/40 hover:bg-zinc-800/60 cursor-pointer"}
                      ${activeElement === child.id ? "ring-2 ring-blue-500/30" : ""}
                    `}
                    onClick={() => handleElementClick(child)}
                  >
                    <div className="w-8 flex justify-center">
                      <div className="cursor-move">
                        <GripVertical className="h-4 w-4 text-zinc-500" />
                      </div>
                    </div>
                    <div className="flex-1 flex items-center">
                      <div className="h-7 w-7 rounded-lg flex items-center justify-center mr-2">
                        {getElementIcon(child.type)}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{child.title}</div>
                      </div>
                    </div>
                    <div className="w-24 text-center">
                      <span className="text-xs text-zinc-300">{child.startTime}</span>
                    </div>
                    <div className="w-24 text-center">
                      <span className="px-2 py-0.5 bg-zinc-700/50 rounded-md text-xs">{child.duration}</span>
                    </div>
                    <div className="w-32 flex justify-center">
                      {child.assignedTo.length > 0 ? (
                        <TeamAvatars avatars={child.assignedTo} size="xs" />
                      ) : (
                        <span className="text-xs text-zinc-500">Unassigned</span>
                      )}
                    </div>
                    <div className="w-24 flex justify-center space-x-1">
                      <Button variant="ghost" size="icon" className="h-6 w-6 rounded-lg">
                        <Edit className="h-3.5 w-3.5" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6 rounded-lg text-red-400">
                        <Trash2 className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}

        {/* Add element button */}
        <div className="flex justify-center p-3">
          <Button variant="outline" size="sm" className="rounded-xl" onClick={handleAddElementClick}>
            <Plus className="h-4 w-4 mr-1.5" />
            Add Element
          </Button>
        </div>
      </div>

      {/* Add element menu */}
      <AnimatePresence>
        {showAddMenu && (
          <motion.div
            ref={addMenuRef}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.15 }}
            style={{ top: addMenuPosition.y, left: addMenuPosition.x }}
            className="absolute z-50 bg-zinc-900/95 backdrop-blur-lg border border-zinc-700/30 rounded-xl shadow-xl p-2 w-56"
          >
            <div className="text-sm font-medium px-2 py-1.5 text-zinc-400">Add Element</div>
            <div className="h-px bg-zinc-800 my-1"></div>
            <div className="max-h-64 overflow-y-auto">
              <button
                className="flex items-center w-full px-2 py-1.5 hover:bg-zinc-800/50 rounded-lg text-left"
                onClick={() => addElement("worship")}
              >
                <Music className="h-4 w-4 text-blue-400 mr-2" />
                <span>Worship</span>
              </button>
              <button
                className="flex items-center w-full px-2 py-1.5 hover:bg-zinc-800/50 rounded-lg text-left"
                onClick={() => addElement("sermon")}
              >
                <Mic className="h-4 w-4 text-green-400 mr-2" />
                <span>Sermon</span>
              </button>
              <button
                className="flex items-center w-full px-2 py-1.5 hover:bg-zinc-800/50 rounded-lg text-left"
                onClick={() => addElement("announcement")}
              >
                <MessageSquare className="h-4 w-4 text-amber-400 mr-2" />
                <span>Announcement</span>
              </button>
              <button
                className="flex items-center w-full px-2 py-1.5 hover:bg-zinc-800/50 rounded-lg text-left"
                onClick={() => addElement("prayer")}
              >
                <Users className="h-4 w-4 text-purple-400 mr-2" />
                <span>Prayer</span>
              </button>
              <button
                className="flex items-center w-full px-2 py-1.5 hover:bg-zinc-800/50 rounded-lg text-left"
                onClick={() => addElement("scripture")}
              >
                <BookOpen className="h-4 w-4 text-cyan-400 mr-2" />
                <span>Scripture Reading</span>
              </button>
              <button
                className="flex items-center w-full px-2 py-1.5 hover:bg-zinc-800/50 rounded-lg text-left"
                onClick={() => addElement("video")}
              >
                <Video className="h-4 w-4 text-red-400 mr-2" />
                <span>Video</span>
              </button>
              <button
                className="flex items-center w-full px-2 py-1.5 hover:bg-zinc-800/50 rounded-lg text-left"
                onClick={() => addElement("offering")}
              >
                <DollarSign className="h-4 w-4 text-emerald-400 mr-2" />
                <span>Offering</span>
              </button>
              <button
                className="flex items-center w-full px-2 py-1.5 hover:bg-zinc-800/50 rounded-lg text-left"
                onClick={() => addElement("transition")}
              >
                <ArrowRight className="h-4 w-4 text-zinc-400 mr-2" />
                <span>Transition</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
