"use client"

import { useState } from "react"
import {
  Calendar,
  Clock,
  MapPin,
  Monitor,
  Mic,
  Music,
  Video,
  Settings,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import DataCard from "@/components/ui/data-card"

interface Resource {
  id: string
  name: string
  type: "room" | "equipment" | "instrument" | "media"
  status: "available" | "booked" | "maintenance" | "conflict"
  details?: string
}

interface ResourceManagerProps {
  serviceId: string
}

export default function ResourceManager({ serviceId }: ResourceManagerProps) {
  // This would normally be fetched from an API
  const [resources, setResources] = useState<Resource[]>([
    {
      id: "r1",
      name: "Main Sanctuary",
      type: "room",
      status: "booked",
      details: "Capacity: 500",
    },
    {
      id: "r2",
      name: "Sound System",
      type: "equipment",
      status: "booked",
      details: "Digital mixer, speakers, subwoofers",
    },
    {
      id: "r3",
      name: "Wireless Microphones",
      type: "equipment",
      status: "booked",
      details: "4 handheld, 2 lapel",
    },
    {
      id: "r4",
      name: "Grand Piano",
      type: "instrument",
      status: "booked",
    },
    {
      id: "r5",
      name: "Acoustic Guitar",
      type: "instrument",
      status: "booked",
    },
    {
      id: "r6",
      name: "Projector & Screens",
      type: "equipment",
      status: "booked",
    },
    {
      id: "r7",
      name: "Lighting System",
      type: "equipment",
      status: "booked",
    },
    {
      id: "r8",
      name: "Green Room",
      type: "room",
      status: "available",
    },
    {
      id: "r9",
      name: "Video Camera",
      type: "equipment",
      status: "conflict",
      details: "Also booked for Youth Event",
    },
  ])

  const [selectedType, setSelectedType] = useState<string>("all")

  const filteredResources =
    selectedType === "all" ? resources : resources.filter((resource) => resource.type === selectedType)

  const getResourceIcon = (type: string) => {
    switch (type) {
      case "room":
        return <MapPin className="h-5 w-5 text-blue-400" />
      case "equipment":
        return <Settings className="h-5 w-5 text-amber-400" />
      case "instrument":
        return <Music className="h-5 w-5 text-purple-400" />
      case "media":
        return <Video className="h-5 w-5 text-red-400" />
      default:
        return <div className="h-5 w-5" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "available":
        return <span className="px-2 py-0.5 bg-green-500/20 text-green-400 rounded-full text-xs">Available</span>
      case "booked":
        return <span className="px-2 py-0.5 bg-blue-500/20 text-blue-400 rounded-full text-xs">Booked</span>
      case "maintenance":
        return <span className="px-2 py-0.5 bg-amber-500/20 text-amber-400 rounded-full text-xs">Maintenance</span>
      case "conflict":
        return <span className="px-2 py-0.5 bg-red-500/20 text-red-400 rounded-full text-xs">Conflict</span>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <DataCard title="Resource Summary" tooltip="Overview of resource bookings">
          <div className="space-y-3">
            <div className="flex items-center justify-between p-2 border-b border-white/5">
              <span className="text-sm">Total Resources</span>
              <span className="font-medium">{resources.length}</span>
            </div>
            <div className="flex items-center justify-between p-2 border-b border-white/5">
              <span className="text-sm">Booked</span>
              <span className="font-medium">{resources.filter((r) => r.status === "booked").length}</span>
            </div>
            <div className="flex items-center justify-between p-2 border-b border-white/5">
              <span className="text-sm">Available</span>
              <span className="font-medium">{resources.filter((r) => r.status === "available").length}</span>
            </div>
            <div className="flex items-center justify-between p-2">
              <span className="text-sm">Conflicts</span>
              <span className="font-medium">{resources.filter((r) => r.status === "conflict").length}</span>
            </div>

            {resources.filter((r) => r.status === "conflict").length > 0 && (
              <div className="mt-3 p-2 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center">
                <AlertCircle className="h-4 w-4 text-red-400 mr-2" />
                <span className="text-xs text-red-400">Resource conflicts need to be resolved.</span>
              </div>
            )}
          </div>
        </DataCard>

        <DataCard title="Service Details" tooltip="Event information">
          <div className="space-y-3">
            <div className="flex items-center p-2 border-b border-white/5">
              <Calendar className="h-4 w-4 text-zinc-400 mr-3" />
              <span className="text-sm">Sunday, May 18th, 2025</span>
            </div>
            <div className="flex items-center p-2 border-b border-white/5">
              <Clock className="h-4 w-4 text-zinc-400 mr-3" />
              <span className="text-sm">9:00 AM - 10:30 AM</span>
            </div>
            <div className="flex items-center p-2">
              <MapPin className="h-4 w-4 text-zinc-400 mr-3" />
              <span className="text-sm">Main Sanctuary</span>
            </div>
          </div>
        </DataCard>

        <DataCard title="Quick Actions" tooltip="Common resource management tasks">
          <div className="space-y-2">
            <Button variant="outline" size="sm" className="w-full justify-start rounded-lg">
              <Plus className="h-4 w-4 mr-2" />
              Add Resource
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start rounded-lg">
              <Monitor className="h-4 w-4 mr-2" />
              Check Availability
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start rounded-lg">
              <Mic className="h-4 w-4 mr-2" />
              Request Equipment
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start rounded-lg">
              <AlertCircle className="h-4 w-4 mr-2" />
              Report Issue
            </Button>
          </div>
        </DataCard>
      </div>

      <DataCard
        title="Resource Bookings"
        tooltip="Manage resources for this service"
        actions={
          <div className="flex items-center gap-2">
            <select
              className="bg-zinc-800/50 border border-zinc-700/30 rounded-lg p-1.5 text-xs"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
            >
              <option value="all">All Resources</option>
              <option value="room">Rooms</option>
              <option value="equipment">Equipment</option>
              <option value="instrument">Instruments</option>
              <option value="media">Media</option>
            </select>
            <Button variant="outline" size="sm" className="rounded-lg text-xs">
              <Plus className="h-3.5 w-3.5 mr-1.5" />
              Add Resource
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-zinc-800/30 rounded-xl text-sm">
            <div className="w-12"></div>
            <div className="flex-1 font-medium">Resource</div>
            <div className="w-32 text-center">Type</div>
            <div className="w-24 text-center">Status</div>
            <div className="w-32 text-center">Actions</div>
          </div>

          {filteredResources.length === 0 ? (
            <div className="p-8 text-center text-zinc-500">No resources found</div>
          ) : (
            filteredResources.map((resource) => (
              <div
                key={resource.id}
                className="flex items-center p-3 bg-zinc-800/50 hover:bg-zinc-800/70 rounded-xl transition-colors"
              >
                <div className="w-12 flex justify-center">{getResourceIcon(resource.type)}</div>
                <div className="flex-1">
                  <div className="font-medium">{resource.name}</div>
                  {resource.details && <div className="text-xs text-zinc-400">{resource.details}</div>}
                </div>
                <div className="w-32 text-center">
                  <span className="text-sm capitalize">{resource.type}</span>
                </div>
                <div className="w-24 text-center">{getStatusBadge(resource.status)}</div>
                <div className="w-32 flex justify-center space-x-1">
                  <Button variant="ghost" size="sm" className="h-7 w-7 rounded-lg p-0">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-7 w-7 rounded-lg p-0">
                    <Calendar className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-7 w-7 rounded-lg p-0 text-red-400">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </DataCard>
    </div>
  )
}
