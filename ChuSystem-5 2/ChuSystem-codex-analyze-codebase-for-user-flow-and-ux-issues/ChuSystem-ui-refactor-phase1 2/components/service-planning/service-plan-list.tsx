"use client"

import { useState } from "react"
import { Calendar, Clock, Users, Edit, Eye, Copy, MoreHorizontal, CheckCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import TeamAvatars from "@/components/team-avatars"

interface ServicePlan {
  id: string
  title: string
  date: string
  time: string
  type: string
  status: "Draft" | "Ready" | "In Progress" | "Completed" | "Needs Review"
  teamMembers: string[]
  lastEdited: string
}

export default function ServicePlanList() {
  const [servicePlans] = useState<ServicePlan[]>([
    {
      id: "SP-001",
      title: "Sunday Morning Service",
      date: "June 16, 2025",
      time: "10:00 AM",
      type: "Sunday Worship",
      status: "Ready",
      teamMembers: ["/avatar1.png", "/avatar2.png", "/avatar3.png"],
      lastEdited: "Today at 9:45 AM",
    },
    {
      id: "SP-002",
      title: "Sunday Evening Service",
      date: "June 16, 2025",
      time: "6:00 PM",
      type: "Sunday Worship",
      status: "In Progress",
      teamMembers: ["/avatar2.png", "/avatar4.png"],
      lastEdited: "Yesterday at 3:20 PM",
    },
    {
      id: "SP-003",
      title: "Midweek Prayer Service",
      date: "June 19, 2025",
      time: "7:00 PM",
      type: "Prayer Meeting",
      status: "Draft",
      teamMembers: ["/avatar1.png"],
      lastEdited: "June 10, 2025",
    },
    {
      id: "SP-004",
      title: "Youth Worship Night",
      date: "June 21, 2025",
      time: "7:30 PM",
      type: "Youth Service",
      status: "Needs Review",
      teamMembers: ["/avatar3.png", "/avatar4.png"],
      lastEdited: "June 9, 2025",
    },
    {
      id: "SP-005",
      title: "Sunday Morning Service",
      date: "June 23, 2025",
      time: "10:00 AM",
      type: "Sunday Worship",
      status: "Draft",
      teamMembers: ["/avatar1.png", "/avatar2.png"],
      lastEdited: "June 8, 2025",
    },
  ])

  const getStatusBadge = (status: ServicePlan["status"]) => {
    switch (status) {
      case "Draft":
        return <span className="px-2 py-0.5 bg-zinc-500/20 text-zinc-400 rounded-full text-xs">Draft</span>
      case "Ready":
        return <span className="px-2 py-0.5 bg-green-500/20 text-green-400 rounded-full text-xs">Ready</span>
      case "In Progress":
        return <span className="px-2 py-0.5 bg-blue-500/20 text-blue-400 rounded-full text-xs">In Progress</span>
      case "Completed":
        return <span className="px-2 py-0.5 bg-purple-500/20 text-purple-400 rounded-full text-xs">Completed</span>
      case "Needs Review":
        return <span className="px-2 py-0.5 bg-amber-500/20 text-amber-400 rounded-full text-xs">Needs Review</span>
      default:
        return null
    }
  }

  return (
    <div className="space-y-4">
      {servicePlans.map((plan) => (
        <div
          key={plan.id}
          className="bg-zinc-900/90 rounded-xl border border-zinc-800/20 p-4 hover:border-zinc-700/30 transition-all"
        >
          <div className="flex justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h3 className="text-lg font-medium">{plan.title}</h3>
                {getStatusBadge(plan.status)}
                <span className="px-2 py-0.5 bg-zinc-800/80 rounded-full text-xs">{plan.type}</span>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="flex items-center text-sm text-zinc-300">
                  <Calendar className="h-4 w-4 mr-2 text-zinc-400" />
                  <span>{plan.date}</span>
                </div>
                <div className="flex items-center text-sm text-zinc-300">
                  <Clock className="h-4 w-4 mr-2 text-zinc-400" />
                  <span>{plan.time}</span>
                </div>
                <div className="flex items-center text-sm text-zinc-300">
                  <Users className="h-4 w-4 mr-2 text-zinc-400" />
                  <span>Team: {plan.teamMembers.length}</span>
                </div>
              </div>
            </div>

            <div className="ml-6 flex flex-col items-end justify-between">
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
                <MoreHorizontal className="h-5 w-5" />
              </Button>

              <div className="flex flex-col items-end gap-2">
                <div className="text-xs text-zinc-400">Last edited: {plan.lastEdited}</div>
                <TeamAvatars avatars={plan.teamMembers} size="sm" />
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-zinc-800/30">
            <Button variant="outline" size="sm" className="rounded-xl">
              <Copy className="h-4 w-4 mr-1.5" />
              Duplicate
            </Button>
            <Button variant="outline" size="sm" className="rounded-xl">
              <Eye className="h-4 w-4 mr-1.5" />
              Preview
            </Button>
            <Button variant="primary" size="sm" className="rounded-xl">
              <Edit className="h-4 w-4 mr-1.5" />
              Edit
            </Button>
          </div>

          {plan.status === "Needs Review" && (
            <div className="mt-3 p-2 bg-amber-500/10 border border-amber-500/20 rounded-lg flex items-center">
              <AlertCircle className="h-4 w-4 text-amber-400 mr-2" />
              <span className="text-xs text-amber-400">This service plan needs review before it's ready.</span>
            </div>
          )}

          {plan.status === "Ready" && (
            <div className="mt-3 p-2 bg-green-500/10 border border-green-500/20 rounded-lg flex items-center">
              <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
              <span className="text-xs text-green-400">This service plan is ready for Sunday.</span>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}
