import type React from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AvatarRoot, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Search, Plus, Check, AlertCircle } from "lucide-react"

interface RoleSelectorProps {
  elementType: string
  currentAssignee: string
}

// Mock team members data
const teamMembers = [
  {
    id: 1,
    name: "<PERSON>",
    avatar: "/avatar1.png",
    initials: "<PERSON><PERSON>",
    role: "Worship Leader",
    skills: ["Vocals", "Guitar", "Piano"],
    availability: "available",
  },
  {
    id: 2,
    name: "<PERSON>",
    avatar: "/avatar2.png",
    initials: "<PERSON><PERSON>",
    role: "Pastor",
    skills: ["Preaching", "Teaching"],
    availability: "available",
  },
  {
    id: 3,
    name: "<PERSON>",
    avatar: "/avatar3.png",
    initials: "<PERSON><PERSON>",
    role: "Tech Director",
    skills: ["Sound", "Lighting", "ProPresenter"],
    availability: "available",
  },
  {
    id: 4,
    name: "<PERSON>",
    avatar: "/avatar4.png",
    initials: "SW",
    role: "Elder",
    skills: ["Scripture Reading", "Prayer"],
    availability: "unavailable",
  },
  {
    id: 5,
    name: "David Kim",
    avatar: "/professional-man-headshot.png",
    initials: "DK",
    role: "Musician",
    skills: ["Drums", "Bass"],
    availability: "available",
  },
  {
    id: 6,
    name: "Rachel Lee",
    avatar: "/professional-woman-headshot.png",
    initials: "RL",
    role: "Vocalist",
    skills: ["Vocals", "Harmony"],
    availability: "conflict",
  },
]

// Helper function to get suggested roles based on element type
const getSuggestedRoles = (elementType: string) => {
  switch (elementType) {
    case "welcome":
      return ["Pastor", "Elder", "Host"]
    case "worship":
      return ["Worship Leader", "Musician", "Vocalist"]
    case "scripture":
      return ["Elder", "Pastor", "Volunteer"]
    case "sermon":
      return ["Pastor", "Guest Speaker"]
    case "prayer":
      return ["Pastor", "Elder", "Prayer Team"]
    case "video":
      return ["Tech Director", "Media Team"]
    case "presentation":
      return ["Tech Director", "Media Team"]
    default:
      return ["Pastor", "Worship Leader", "Elder", "Volunteer"]
  }
}

export const RoleSelector: React.FC<RoleSelectorProps> = ({ elementType, currentAssignee }) => {
  const suggestedRoles = getSuggestedRoles(elementType)

  // Filter team members based on suggested roles
  const suggestedMembers = teamMembers.filter((member) => suggestedRoles.includes(member.role))

  // Other team members
  const otherMembers = teamMembers.filter((member) => !suggestedRoles.includes(member.role))

  const getAvailabilityIndicator = (availability: string) => {
    switch (availability) {
      case "available":
        return <span className="h-2.5 w-2.5 rounded-full bg-green-500"></span>
      case "unavailable":
        return <span className="h-2.5 w-2.5 rounded-full bg-red-500"></span>
      case "conflict":
        return <span className="h-2.5 w-2.5 rounded-full bg-amber-500"></span>
      default:
        return <span className="h-2.5 w-2.5 rounded-full bg-gray-500"></span>
    }
  }

  return (
    <div className="space-y-4">
      <div className="relative">
        <Input placeholder="Search team members..." className="pl-10 bg-black/60 border-white/10" />
        <Search className="absolute left-3 top-2.5 h-5 w-5 text-white/50" />
      </div>

      <div>
        <Label className="text-sm text-white/70 mb-2 block">Suggested for this role</Label>
        <div className="space-y-2">
          {suggestedMembers.map((member) => (
            <Card
              key={member.id}
              className={`p-3 flex items-center justify-between cursor-pointer transition-all
                ${
                  currentAssignee.includes(member.name)
                    ? "bg-purple-950/30 border-purple-500/50"
                    : "bg-black/60 border-white/10 hover:border-white/30"
                }`}
            >
              <div className="flex items-center">
                <div className="relative">
                  <AvatarRoot className="h-8 w-8 border border-white/10">
                    <AvatarRootImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                    <AvatarRootFallback>{member.initials}</AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-0.5 -right-0.5 flex items-center justify-center h-3.5 w-3.5 rounded-full bg-black border border-white/10">
                    {getAvailabilityIndicator(member.availability)}
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{member.name}</p>
                  <p className="text-xs text-white/60">{member.role}</p>
                </div>
              </div>

              <div className="flex items-center">
                {member.availability === "conflict" && (
                  <div className="mr-2 px-1.5 py-0.5 text-xs rounded bg-amber-500/20 text-amber-300 border border-amber-500/30 flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Conflict
                  </div>
                )}
                {currentAssignee.includes(member.name) ? (
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <Check className="h-4 w-4 text-purple-400" />
                  </Button>
                ) : (
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <Plus className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </Card>
          ))}
        </div>
      </div>

      <div>
        <Label className="text-sm text-white/70 mb-2 block">Other team members</Label>
        <div className="space-y-2 max-h-[200px] overflow-y-auto scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
          {otherMembers.map((member) => (
            <Card
              key={member.id}
              className={`p-3 flex items-center justify-between cursor-pointer transition-all
                ${
                  currentAssignee.includes(member.name)
                    ? "bg-purple-950/30 border-purple-500/50"
                    : "bg-black/60 border-white/10 hover:border-white/30"
                }`}
            >
              <div className="flex items-center">
                <div className="relative">
                  <AvatarRoot className="h-8 w-8 border border-white/10">
                    <AvatarRootImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                    <AvatarRootFallback>{member.initials}</AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-0.5 -right-0.5 flex items-center justify-center h-3.5 w-3.5 rounded-full bg-black border border-white/10">
                    {getAvailabilityIndicator(member.availability)}
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{member.name}</p>
                  <p className="text-xs text-white/60">{member.role}</p>
                </div>
              </div>

              <div className="flex items-center">
                {member.availability === "unavailable" && (
                  <div className="mr-2 px-1.5 py-0.5 text-xs rounded bg-red-500/20 text-red-300 border border-red-500/30 flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Unavailable
                  </div>
                )}
                {currentAssignee.includes(member.name) ? (
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <Check className="h-4 w-4 text-purple-400" />
                  </Button>
                ) : (
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <Plus className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </Card>
          ))}
        </div>
      </div>

      <Button
        variant="outline"
        className="w-full border-dashed border-white/20 bg-white/5 hover:bg-white/10 hover:border-white/30"
      >
        <Plus className="h-4 w-4 mr-2" />
        Create New Team
      </Button>
    </div>
  )
}
