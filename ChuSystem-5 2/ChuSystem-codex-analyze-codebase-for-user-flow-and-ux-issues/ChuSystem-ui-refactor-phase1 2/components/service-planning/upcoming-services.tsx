import { Calendar, Clock, Users, Music } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function UpcomingServices() {
  const services = [
    {
      id: "1",
      title: "Sunday Morning Service",
      date: "June 16, 2025",
      time: "10:00 AM",
      location: "Main Sanctuary",
      songs: 5,
      team: 8,
    },
    {
      id: "2",
      title: "Sunday Evening Service",
      date: "June 16, 2025",
      time: "6:00 PM",
      location: "Main Sanctuary",
      songs: 4,
      team: 6,
    },
    {
      id: "3",
      title: "Midweek Prayer Service",
      date: "June 19, 2025",
      time: "7:00 PM",
      location: "Fellowship Hall",
      songs: 3,
      team: 4,
    },
  ]

  return (
    <div className="space-y-3">
      {services.map((service) => (
        <div
          key={service.id}
          className="p-3 bg-zinc-800/50 rounded-xl hover:bg-zinc-800/80 transition-colors cursor-pointer"
        >
          <h4 className="font-medium mb-2">{service.title}</h4>
          <div className="space-y-1.5">
            <div className="flex items-center text-xs text-zinc-400">
              <Calendar className="h-3.5 w-3.5 mr-2" />
              <span>{service.date}</span>
            </div>
            <div className="flex items-center text-xs text-zinc-400">
              <Clock className="h-3.5 w-3.5 mr-2" />
              <span>{service.time}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center text-xs text-zinc-400">
                <Music className="h-3.5 w-3.5 mr-2" />
                <span>{service.songs} songs</span>
              </div>
              <div className="flex items-center text-xs text-zinc-400">
                <Users className="h-3.5 w-3.5 mr-2" />
                <span>{service.team} team members</span>
              </div>
            </div>
          </div>
        </div>
      ))}

      <div className="flex justify-center pt-2">
        <Button variant="outline" size="sm" className="text-xs rounded-xl w-full">
          View All Services
        </Button>
      </div>
    </div>
  )
}
