"use client"

import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Clock, Users, Music, FileText, MessageSquare, Settings, X, Plus, Trash2 } from "lucide-react"
import { RoleSelector } from "@/components/service-planning/role-selector"

interface Song {
  title: string
  key: string
  arrangement: string
}

interface ServiceElement {
  id: string
  type: string
  title: string
  duration: number
  assignedTo: string
  notes: string
  status: string
  songs?: Song[]
  content?: string
}

interface ServiceElementEditorProps {
  elementId: string
  element: ServiceElement
  onClose: () => void
}

export const ServiceElementEditor: React.FC<ServiceElementEditorProps> = ({ elementId, element, onClose }) => {
  return (
    <Card className="bg-black/40 backdrop-blur-sm border border-white/10 shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg">Edit Element</CardTitle>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="element-title">Title</Label>
          <Input id="element-title" defaultValue={element.title} className="mt-1 bg-black/60 border-white/10" />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="element-type">Type</Label>
            <Select defaultValue={element.type}>
              <SelectTrigger className="mt-1 bg-black/60 border-white/10">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent className="bg-black/90 border-white/10">
                <SelectItem value="welcome">Welcome</SelectItem>
                <SelectItem value="worship">Worship</SelectItem>
                <SelectItem value="scripture">Scripture</SelectItem>
                <SelectItem value="sermon">Sermon</SelectItem>
                <SelectItem value="prayer">Prayer</SelectItem>
                <SelectItem value="video">Video</SelectItem>
                <SelectItem value="presentation">Presentation</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="element-duration">Duration (minutes)</Label>
            <div className="relative mt-1">
              <Input
                id="element-duration"
                type="number"
                defaultValue={element.duration}
                className="pl-10 bg-black/60 border-white/10"
              />
              <Clock className="absolute left-3 top-2.5 h-5 w-5 text-white/50" />
            </div>
          </div>
        </div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="bg-black/60 border border-white/10">
            <TabsTrigger value="details" className="data-[state=active]:bg-white/10">
              <FileText className="h-4 w-4 mr-2" />
              Details
            </TabsTrigger>
            <TabsTrigger value="people" className="data-[state=active]:bg-white/10">
              <Users className="h-4 w-4 mr-2" />
              People
            </TabsTrigger>
            {element.type === "worship" && (
              <TabsTrigger value="songs" className="data-[state=active]:bg-white/10">
                <Music className="h-4 w-4 mr-2" />
                Songs
              </TabsTrigger>
            )}
            <TabsTrigger value="notes" className="data-[state=active]:bg-white/10">
              <MessageSquare className="h-4 w-4 mr-2" />
              Notes
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-4 space-y-4">
            <div>
              <Label htmlFor="element-status">Status</Label>
              <Select defaultValue={element.status}>
                <SelectTrigger className="mt-1 bg-black/60 border-white/10">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent className="bg-black/90 border-white/10">
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="needs-attention">Needs Attention</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {element.type === "scripture" && (
              <div>
                <Label htmlFor="scripture-reference">Scripture Reference</Label>
                <Input
                  id="scripture-reference"
                  defaultValue={element.content}
                  className="mt-1 bg-black/60 border-white/10"
                />
              </div>
            )}

            {element.type === "sermon" && (
              <div>
                <Label htmlFor="sermon-title">Sermon Title</Label>
                <Input
                  id="sermon-title"
                  defaultValue={element.title.replace('Sermon: "', "").replace('"', "")}
                  className="mt-1 bg-black/60 border-white/10"
                />
              </div>
            )}

            {(element.type === "video" || element.type === "presentation") && (
              <div>
                <Label htmlFor="media-source">Media Source</Label>
                <div className="mt-1 p-4 border border-dashed border-white/20 rounded-md bg-black/60 flex flex-col items-center justify-center">
                  <Button variant="outline" className="border-white/10 mb-2">
                    <Plus className="h-4 w-4 mr-2" />
                    Select Media
                  </Button>
                  <p className="text-xs text-white/50">Supported formats: MP4, MOV, PPT, PDF</p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="people" className="mt-4">
            <RoleSelector elementType={element.type} currentAssignee={element.assignedTo} />
          </TabsContent>

          <TabsContent value="songs" className="mt-4">
            {element.songs &&
              element.songs.map((song, idx) => (
                <div key={idx} className="mb-4 p-3 border border-white/10 rounded-md bg-black/60">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">{song.title}</h4>
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-white/70 hover:text-white/100">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor={`song-key-${idx}`} className="text-xs">
                        Key
                      </Label>
                      <Select defaultValue={song.key}>
                        <SelectTrigger className="mt-1 bg-black/80 border-white/10 h-8 text-sm">
                          <SelectValue placeholder="Select key" />
                        </SelectTrigger>
                        <SelectContent className="bg-black/90 border-white/10">
                          <SelectItem value="A">A</SelectItem>
                          <SelectItem value="Bb">Bb</SelectItem>
                          <SelectItem value="B">B</SelectItem>
                          <SelectItem value="C">C</SelectItem>
                          <SelectItem value="D">D</SelectItem>
                          <SelectItem value="E">E</SelectItem>
                          <SelectItem value="F">F</SelectItem>
                          <SelectItem value="G">G</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor={`song-arrangement-${idx}`} className="text-xs">
                        Arrangement
                      </Label>
                      <Select defaultValue={song.arrangement}>
                        <SelectTrigger className="mt-1 bg-black/80 border-white/10 h-8 text-sm">
                          <SelectValue placeholder="Select arrangement" />
                        </SelectTrigger>
                        <SelectContent className="bg-black/90 border-white/10">
                          <SelectItem value="Standard">Standard</SelectItem>
                          <SelectItem value="Contemporary">Contemporary</SelectItem>
                          <SelectItem value="Acoustic">Acoustic</SelectItem>
                          <SelectItem value="Contemplative">Contemplative</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              ))}

            <Button variant="outline" className="w-full border-white/10 bg-black/40">
              <Plus className="h-4 w-4 mr-2" />
              Add Song
            </Button>
          </TabsContent>

          <TabsContent value="notes" className="mt-4 space-y-4">
            <div>
              <Label htmlFor="general-notes">General Notes</Label>
              <Textarea
                id="general-notes"
                defaultValue={element.notes}
                className="mt-1 bg-black/60 border-white/10 min-h-[100px]"
              />
            </div>

            <div>
              <Label htmlFor="tech-notes">Tech Notes</Label>
              <Textarea
                id="tech-notes"
                placeholder="Notes for the tech team..."
                className="mt-1 bg-black/60 border-white/10 min-h-[80px]"
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" className="border-white/10">
          <Trash2 className="h-4 w-4 mr-2" />
          Delete
        </Button>
        <div className="flex space-x-2">
          <Button variant="ghost" onClick={onClose}>
            Cancel
          </Button>
          <Button className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700">
            <Settings className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
