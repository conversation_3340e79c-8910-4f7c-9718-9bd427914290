"use client"

import { useState } from "react"
import { Users, MessageSquare, Clock, Calendar, Bell, Plus, Send } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import DataCard from "@/components/ui/data-card"

interface CollaborationPanelProps {
  serviceId: string
}

interface ActivityItem {
  id: string
  user: {
    name: string
    avatar: string
  }
  action: string
  timestamp: string
}

interface ChatMessage {
  id: string
  user: {
    name: string
    avatar: string
  }
  message: string
  timestamp: string
}

export default function CollaborationPanel({ serviceId }: CollaborationPanelProps) {
  // This would normally be fetched from an API
  const [activity, setActivity] = useState<ActivityItem[]>([
    {
      id: "a1",
      user: { name: "<PERSON>", avatar: "/avatar2.png" },
      action: "added 'How Great Is Our God' to the opening worship",
      timestamp: "10 minutes ago",
    },
    {
      id: "a2",
      user: { name: "<PERSON>", avatar: "/avatar1.png" },
      action: "updated the sermon notes",
      timestamp: "25 minutes ago",
    },
    {
      id: "a3",
      user: { name: "<PERSON>", avatar: "/avatar3.png" },
      action: "confirmed availability for this service",
      timestamp: "1 hour ago",
    },
    {
      id: "a4",
      user: { name: "<PERSON> <PERSON>", avatar: "/avatar4.png" },
      action: "added scripture reading: Romans 8:28-39",
      timestamp: "2 hours ago",
    },
    {
      id: "a5",
      user: { name: "<PERSON> <PERSON>", avatar: "/avatar1.png" },
      action: "created this service plan",
      timestamp: "Yesterday",
    },
  ])

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "m1",
      user: { name: "Sarah Johnson", avatar: "/avatar2.png" },
      message: "I've updated the worship set with the new songs we discussed.",
      timestamp: "45 minutes ago",
    },
    {
      id: "m2",
      user: { name: "John Smith", avatar: "/avatar1.png" },
      message: "Looks great! Can we make sure to include time for testimonies after the sermon?",
      timestamp: "30 minutes ago",
    },
    {
      id: "m3",
      user: { name: "Sarah Johnson", avatar: "/avatar2.png" },
      message: "Sure, I'll add that in. How much time should we allocate?",
      timestamp: "25 minutes ago",
    },
    {
      id: "m4",
      user: { name: "John Smith", avatar: "/avatar1.png" },
      message: "Let's plan for 5-7 minutes. We have two people sharing.",
      timestamp: "20 minutes ago",
    },
  ])

  const [newMessage, setNewMessage] = useState("")
  const [activeUsers, setActiveUsers] = useState([
    { name: "Sarah Johnson", avatar: "/avatar2.png", status: "active" },
    { name: "John Smith", avatar: "/avatar1.png", status: "active" },
    { name: "Michael Williams", avatar: "/avatar3.png", status: "idle" },
  ])

  const [activeTab, setActiveTab] = useState<"activity" | "chat">("activity")

  const sendMessage = () => {
    if (!newMessage.trim()) return

    const message: ChatMessage = {
      id: `m${messages.length + 1}`,
      user: { name: "You", avatar: "/avatar1.png" },
      message: newMessage,
      timestamp: "Just now",
    }

    setMessages([...messages, message])
    setNewMessage("")
  }

  return (
    <div className="space-y-6">
      <DataCard title="Active Collaborators" tooltip="People currently working on this service plan">
        <div className="space-y-2">
          {activeUsers.map((user, index) => (
            <div key={index} className="flex items-center p-2 bg-zinc-800/30 rounded-lg">
              <div className="relative">
                <img src={user.avatar || "/placeholder.svg"} alt={user.name} className="w-8 h-8 rounded-full mr-3" />
                <div
                  className={`absolute bottom-0 right-2 w-2.5 h-2.5 rounded-full ${
                    user.status === "active" ? "bg-green-500" : "bg-amber-500"
                  }`}
                ></div>
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium">{user.name}</div>
                <div className="text-xs text-zinc-400 capitalize">{user.status}</div>
              </div>
            </div>
          ))}
          <Button variant="outline" size="sm" className="w-full rounded-lg mt-2">
            <Plus className="h-4 w-4 mr-1.5" />
            Invite Collaborator
          </Button>
        </div>
      </DataCard>

      <div>
        <div className="flex border-b border-zinc-800/40 mb-4">
          <Button
            variant="ghost"
            className={`px-4 py-2 ${activeTab === "activity" ? "border-b-2 border-blue-500 text-white" : "text-zinc-400"}`}
            onClick={() => setActiveTab("activity")}
          >
            <Clock className="h-4 w-4 mr-1.5" />
            Activity
          </Button>
          <Button
            variant="ghost"
            className={`px-4 py-2 ${activeTab === "chat" ? "border-b-2 border-blue-500 text-white" : "text-zinc-400"}`}
            onClick={() => setActiveTab("chat")}
          >
            <MessageSquare className="h-4 w-4 mr-1.5" />
            Chat
          </Button>
        </div>

        {activeTab === "activity" && (
          <DataCard title="Recent Activity" tooltip="Latest changes to this service plan">
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {activity.map((item) => (
                <div key={item.id} className="flex p-2 border-b border-zinc-800/30 last:border-0">
                  <img
                    src={item.user.avatar || "/placeholder.svg"}
                    alt={item.user.name}
                    className="w-8 h-8 rounded-full mr-3"
                  />
                  <div>
                    <div className="text-sm">
                      <span className="font-medium">{item.user.name}</span>{" "}
                      <span className="text-zinc-400">{item.action}</span>
                    </div>
                    <div className="text-xs text-zinc-500 mt-0.5">{item.timestamp}</div>
                  </div>
                </div>
              ))}
            </div>
          </DataCard>
        )}

        {activeTab === "chat" && (
          <DataCard title="Team Chat" tooltip="Discuss this service plan with your team">
            <div className="flex flex-col h-96">
              <div className="flex-1 overflow-y-auto space-y-3 mb-3">
                {messages.map((message) => (
                  <div key={message.id} className="flex p-2">
                    <img
                      src={message.user.avatar || "/placeholder.svg"}
                      alt={message.user.name}
                      className="w-8 h-8 rounded-full mr-3"
                    />
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium text-sm">{message.user.name}</span>
                        <span className="text-xs text-zinc-500 ml-2">{message.timestamp}</span>
                      </div>
                      <div className="text-sm mt-1">{message.message}</div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex items-center mt-auto">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 bg-zinc-800/50 border border-zinc-700/30 rounded-l-lg p-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                  onKeyDown={(e) => e.key === "Enter" && sendMessage()}
                />
                <Button variant="primary" size="sm" className="rounded-l-none rounded-r-lg" onClick={sendMessage}>
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </DataCard>
        )}
      </div>

      <DataCard title="Upcoming Deadlines" tooltip="Important dates for this service">
        <div className="space-y-3">
          <div className="flex items-center justify-between p-2 bg-zinc-800/30 rounded-lg">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 text-blue-400 mr-2" />
              <span className="text-sm">Final Plan Due</span>
            </div>
            <span className="text-sm">May 16, 2025</span>
          </div>
          <div className="flex items-center justify-between p-2 bg-zinc-800/30 rounded-lg">
            <div className="flex items-center">
              <Users className="h-4 w-4 text-green-400 mr-2" />
              <span className="text-sm">Team Confirmation</span>
            </div>
            <span className="text-sm">May 15, 2025</span>
          </div>
          <div className="flex items-center justify-between p-2 bg-zinc-800/30 rounded-lg">
            <div className="flex items-center">
              <Bell className="h-4 w-4 text-amber-400 mr-2" />
              <span className="text-sm">Rehearsal</span>
            </div>
            <span className="text-sm">May 17, 2025</span>
          </div>
        </div>
      </DataCard>
    </div>
  )
}
