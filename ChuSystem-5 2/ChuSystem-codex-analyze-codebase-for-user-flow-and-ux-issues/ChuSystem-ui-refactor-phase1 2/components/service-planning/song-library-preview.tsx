import { Music, Clock } from "lucide-react"

export default function SongLibraryPreview() {
  const songs = [
    {
      id: "1",
      title: "Amazing Grace (My Chains Are Gone)",
      artist: "<PERSON>",
      key: "G",
      tempo: "72 BPM",
      lastUsed: "2 weeks ago",
    },
    {
      id: "2",
      title: "How Great Is Our God",
      artist: "<PERSON>",
      key: "C",
      tempo: "78 BPM",
      lastUsed: "1 month ago",
    },
    {
      id: "3",
      title: "10,000 Reasons (Bless the Lord)",
      artist: "<PERSON>",
      key: "D",
      tempo: "73 BPM",
      lastUsed: "3 weeks ago",
    },
    {
      id: "4",
      title: "What A Beautiful Name",
      artist: "Hillsong Worship",
      key: "D",
      tempo: "68 BPM",
      lastUsed: "Last Sunday",
    },
  ]

  return (
    <div className="space-y-3">
      {songs.map((song) => (
        <div
          key={song.id}
          className="p-3 bg-zinc-800/50 rounded-xl hover:bg-zinc-800/80 transition-colors cursor-pointer flex items-center"
        >
          <div className="h-10 w-10 rounded-lg bg-violet-500/20 flex items-center justify-center mr-3">
            <Music className="h-5 w-5 text-violet-400" />
          </div>
          <div className="flex-1">
            <h4 className="font-medium text-sm">{song.title}</h4>
            <div className="text-xs text-zinc-400">{song.artist}</div>
          </div>
          <div className="flex flex-col items-end">
            <div className="flex items-center text-xs text-zinc-300 mb-1">
              <span className="px-1.5 py-0.5 bg-zinc-700/50 rounded-md mr-2">{song.key}</span>
              <Clock className="h-3 w-3 mr-1" />
              <span>{song.tempo}</span>
            </div>
            <div className="text-xs text-zinc-500">Last used: {song.lastUsed}</div>
          </div>
        </div>
      ))}
    </div>
  )
}
