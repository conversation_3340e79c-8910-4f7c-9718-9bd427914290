"use client"

import { useState } from "react"
import {
  Music,
  Mic,
  MessageSquare,
  Book,
  Video,
  DollarSign,
  Clock,
  Plus,
  GripVertical,
  Edit,
  Trash2,
  ChevronDown,
  ChevronUp,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import DataCard from "@/components/ui/data-card"

interface ServiceElement {
  id: string
  type: "worship" | "sermon" | "announcement" | "scripture" | "video" | "offering" | "prayer" | "transition"
  title: string
  duration: string
  assignedTo: string[]
  notes?: string
  details?: any
}

interface ServiceBuilderProps {
  serviceId: string
}

export default function ServiceBuilder({ serviceId }: ServiceBuilderProps) {
  // This would normally be fetched from an API
  const [elements, setElements] = useState<ServiceElement[]>([
    {
      id: "e1",
      type: "worship",
      title: "Opening Worship",
      duration: "15:00",
      assignedTo: ["/avatar2.png", "/avatar3.png"],
      details: {
        songs: [
          { title: "How Great Is Our God", key: "C", duration: "4:30" },
          { title: "10,000 Reasons", key: "D", duration: "5:15" },
          { title: "What A Beautiful Name", key: "D", duration: "5:15" },
        ],
      },
    },
    {
      id: "e2",
      type: "announcement",
      title: "Welcome & Announcements",
      duration: "5:00",
      assignedTo: ["/avatar1.png"],
      notes: "Highlight upcoming youth event and mission trip registration deadline",
    },
    {
      id: "e3",
      type: "worship",
      title: "Worship Set",
      duration: "12:00",
      assignedTo: ["/avatar2.png", "/avatar3.png"],
      details: {
        songs: [
          { title: "Great Are You Lord", key: "G", duration: "4:00" },
          { title: "Cornerstone", key: "Bb", duration: "4:00" },
          { title: "Build My Life", key: "D", duration: "4:00" },
        ],
      },
    },
    {
      id: "e4",
      type: "scripture",
      title: "Scripture Reading",
      duration: "3:00",
      assignedTo: ["/avatar4.png"],
      details: {
        passage: "Romans 8:28-39",
      },
    },
    {
      id: "e5",
      type: "sermon",
      title: "Sermon: 'Finding Peace in Chaos'",
      duration: "30:00",
      assignedTo: ["/avatar1.png"],
      notes: "Part 3 of the 'Peace That Passes Understanding' series",
    },
    {
      id: "e6",
      type: "worship",
      title: "Response Song",
      duration: "5:00",
      assignedTo: ["/avatar2.png"],
      details: {
        songs: [{ title: "It Is Well", key: "D", duration: "5:00" }],
      },
    },
    {
      id: "e7",
      type: "prayer",
      title: "Closing Prayer",
      duration: "2:00",
      assignedTo: ["/avatar1.png"],
    },
  ])

  const [expandedElement, setExpandedElement] = useState<string | null>(null)

  const getElementIcon = (type: ServiceElement["type"]) => {
    switch (type) {
      case "worship":
        return <Music className="h-5 w-5 text-blue-400" />
      case "sermon":
        return <Mic className="h-5 w-5 text-green-400" />
      case "announcement":
        return <MessageSquare className="h-5 w-5 text-amber-400" />
      case "prayer":
        return <Users className="h-5 w-5 text-purple-400" />
      case "scripture":
        return <Book className="h-5 w-5 text-cyan-400" />
      case "video":
        return <Video className="h-5 w-5 text-red-400" />
      case "offering":
        return <DollarSign className="h-5 w-5 text-emerald-400" />
      case "transition":
        return <ArrowRight className="h-5 w-5 text-zinc-400" />
      default:
        return <div className="h-5 w-5" />
    }
  }

  const toggleExpand = (id: string) => {
    if (expandedElement === id) {
      setExpandedElement(null)
    } else {
      setExpandedElement(id)
    }
  }

  // Calculate total duration
  const totalDuration = elements.reduce((total, element) => {
    const [minutes, seconds] = element.duration.split(":").map(Number)
    return total + minutes * 60 + seconds
  }, 0)

  const formatTotalDuration = () => {
    const hours = Math.floor(totalDuration / 3600)
    const minutes = Math.floor((totalDuration % 3600) / 60)
    const seconds = totalDuration % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m ${seconds > 0 ? seconds + "s" : ""}`
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center text-sm">
            <Clock className="h-4 w-4 mr-1.5 text-zinc-400" />
            <span className="text-zinc-300">Total Duration: </span>
            <span className="ml-1 font-medium">{formatTotalDuration()}</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="rounded-lg">
            <Plus className="h-4 w-4 mr-1.5" />
            Add Element
          </Button>
        </div>
      </div>

      <DataCard title="Service Flow" tooltip="Drag and drop to reorder elements">
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-zinc-800/30 rounded-xl text-sm">
            <div className="w-8"></div>
            <div className="flex-1 font-medium">Element</div>
            <div className="w-24 text-center">Duration</div>
            <div className="w-32 text-center">Assigned To</div>
            <div className="w-24 text-center">Actions</div>
          </div>

          {elements.map((element, index) => (
            <div key={element.id} className="space-y-2">
              <div className="flex items-center p-3 bg-zinc-800/50 hover:bg-zinc-800/70 rounded-xl transition-colors">
                <div className="w-8 flex justify-center cursor-move">
                  <GripVertical className="h-5 w-5 text-zinc-500" />
                </div>
                <div className="flex-1 flex items-center">
                  <div className="h-8 w-8 rounded-lg flex items-center justify-center mr-3">
                    {getElementIcon(element.type)}
                  </div>
                  <div>
                    <div className="font-medium">{element.title}</div>
                    {element.notes && <div className="text-xs text-zinc-400 mt-0.5 line-clamp-1">{element.notes}</div>}
                  </div>
                </div>
                <div className="w-24 text-center">
                  <span className="px-2 py-1 bg-zinc-700/50 rounded-md text-sm">{element.duration}</span>
                </div>
                <div className="w-32 flex justify-center">
                  <div className="flex -space-x-2">
                    {element.assignedTo.map((avatar, i) => (
                      <img
                        key={i}
                        src={avatar || "/placeholder.svg"}
                        alt="Team member"
                        className="w-6 h-6 rounded-full border border-black"
                      />
                    ))}
                  </div>
                </div>
                <div className="w-24 flex justify-center space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-lg"
                    onClick={() => toggleExpand(element.id)}
                  >
                    {expandedElement === element.id ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                  <Button variant="ghost" size="icon" className="h-7 w-7 rounded-lg">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-7 w-7 rounded-lg text-red-400">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {expandedElement === element.id && (
                <div className="ml-8 p-4 bg-zinc-800/30 rounded-xl">
                  {element.type === "worship" && element.details?.songs && (
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium">Songs</h4>
                      <div className="space-y-2">
                        {element.details.songs.map((song: any, i: number) => (
                          <div key={i} className="flex items-center justify-between p-2 bg-zinc-800/50 rounded-lg">
                            <div>
                              <div className="font-medium text-sm">{song.title}</div>
                              <div className="text-xs text-zinc-400">Key: {song.key}</div>
                            </div>
                            <div className="text-xs text-zinc-400">{song.duration}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {element.type === "scripture" && element.details?.passage && (
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium">Scripture Passage</h4>
                      <div className="p-3 bg-zinc-800/50 rounded-lg">
                        <div className="text-sm">{element.details.passage}</div>
                      </div>
                    </div>
                  )}

                  {element.notes && (
                    <div className="space-y-3 mt-3">
                      <h4 className="text-sm font-medium">Notes</h4>
                      <div className="p-3 bg-zinc-800/50 rounded-lg">
                        <div className="text-sm">{element.notes}</div>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end mt-4">
                    <Button variant="outline" size="sm" className="rounded-lg">
                      <Edit className="h-4 w-4 mr-1.5" />
                      Edit Details
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ))}

          <div className="flex justify-center p-3">
            <Button variant="outline" size="sm" className="rounded-xl">
              <Plus className="h-4 w-4 mr-1.5" />
              Add Element
            </Button>
          </div>
        </div>
      </DataCard>
    </div>
  )
}

function Users(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  )
}

function ArrowRight(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M5 12h14" />
      <path d="m12 5 7 7-7 7" />
    </svg>
  )
}
