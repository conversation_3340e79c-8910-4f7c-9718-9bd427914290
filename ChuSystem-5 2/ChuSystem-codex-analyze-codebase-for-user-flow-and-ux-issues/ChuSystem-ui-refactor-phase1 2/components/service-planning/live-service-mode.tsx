"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>, Users } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { AvatarRoot, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { CheckCircle, AlertCircle, Send, Presentation, Settings } from "lucide-react"

interface LiveServiceModeProps {
  serviceId: string
}

// Mock service elements data
const serviceElements = [
  {
    id: "element-1",
    type: "welcome",
    title: "Welcome & Announcements",
    duration: 5,
    assignedTo: "Pastor Emily",
    status: "completed",
    actualDuration: 4,
  },
  {
    id: "element-2",
    type: "worship",
    title: "Opening Worship Set",
    duration: 15,
    assignedTo: "<PERSON> (Worship Leader)",
    status: "live",
    progress: 60,
    elapsedTime: 9,
  },
  {
    id: "element-3",
    type: "scripture",
    title: "Scripture Reading",
    duration: 3,
    assignedTo: "Sarah (Reader)",
    status: "upcoming",
  },
  {
    id: "element-4",
    type: "sermon",
    title: "Sermon",
    duration: 30,
    assignedTo: "Pastor Emily",
    status: "upcoming",
  },
  {
    id: "element-5",
    type: "worship",
    title: "Closing Worship Set",
    duration: 10,
    assignedTo: "John (Worship Leader)",
    status: "upcoming",
  },
  {
    id: "element-6",
    type: "closing",
    title: "Closing Prayer",
    duration: 3,
    assignedTo: "Pastor Emily",
    status: "upcoming",
  },
]

// Sample service data
// const sampleServiceElements = [
//   { id: "welcome", type: "segment", title: "Welcome & Announcements", duration: 5, status: "completed", actualDuration: 6, person: "Pastor Emily" },
//   { id: "worship1", type: "worship", title: "Worship Set 1", duration: 15, status: "active", elapsedTime: 8, person: "John Davis" },
//   { id: "scripture", type: "content", title: "Scripture Reading", duration: 3, status: "upcoming", person: "Sarah Wilson" },
//   { id: "sermon", type: "content", title: "Sermon", duration: 30, status: "upcoming", person: "Pastor Emily" },
//   { id: "worship2", type: "worship", title: "Worship Set 2", duration: 10, status: "upcoming", person: "John Davis" },
//   { id: "closing", type: "segment", title: "Closing Prayer", duration: 3, status: "upcoming", person: "Pastor Emily" },
// ];

export function LiveServiceMode() {
  // const [serviceElements, setServiceElements] = useState(sampleServiceElements);
  const [elapsedTime, setElapsedTime] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [messages, setMessages] = useState([
    { id: 1, sender: "Mike (Tech)", avatar: "/avatar1.png", text: "Pastor mic is hot", time: "10:02 AM" },
    {
      id: 2,
      sender: "John (Worship)",
      avatar: "/professional-man-headshot.png",
      text: "Ready for next song",
      time: "10:05 AM",
    },
  ])
  const [newMessage, setNewMessage] = useState("")
  const [showSidebar, setShowSidebar] = useState(true)

  // Find the active element
  const activeElementIndex = serviceElements.findIndex((el) => el.status === "live")
  const activeElement = serviceElements[activeElementIndex]
  const nextElement = serviceElements[activeElementIndex + 1]

  // Calculate total service time
  const totalPlannedDuration = serviceElements.reduce((total, el) => total + el.duration, 0)
  const completedDuration = serviceElements
    .filter((el) => el.status === "completed")
    .reduce((total, el) => total + (el.actualDuration || el.duration), 0)

  // Calculate progress percentage
  const progressPercentage = Math.min(
    ((completedDuration + (activeElement ? elapsedTime : 0)) / totalPlannedDuration) * 100,
    100,
  )

  // Timer effect for the active element
  useEffect(() => {
    if (!activeElement || isPaused) return

    const timer = setInterval(() => {
      setElapsedTime((prev) => {
        // If we've reached the planned duration, we don't stop automatically
        // This allows for tracking overtime
        return prev + 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [activeElement, isPaused])

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  // Handle completing the current element
  const completeCurrentElement = () => {
    if (!activeElement) return

    // setServiceElements(elements =>
    //   elements.map((el, idx) => {
    //     if (idx === activeElementIndex) {
    //       return { ...el, status: "completed", actualDuration: elapsedTime };
    //     } else if (idx === activeElementIndex + 1) {
    //       return { ...el, status: "active" };
    //     } else {
    //       return el;
    //     }
    //   })
    // );

    setElapsedTime(0)
  }

  // Handle skipping the current element
  const skipCurrentElement = () => {
    if (!activeElement) return

    // setServiceElements(elements =>
    //   elements.map((el, idx) => {
    //     if (idx === activeElementIndex) {
    //       return { ...el, status: "skipped" };
    //     } else if (idx === activeElementIndex + 1) {
    //       return { ...el, status: "active" };
    //     } else {
    //       return el;
    //     }
    //   })
    // );

    setElapsedTime(0)
  }

  // Send a new message
  const sendMessage = () => {
    if (!newMessage.trim()) return

    const message = {
      id: Date.now(),
      sender: "You",
      avatar: "/professional-headshot.png",
      text: newMessage,
      time: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    }

    setMessages([...messages, message])
    setNewMessage("")
  }

  return (
    <div className="h-full bg-muted">
      <Tabs defaultValue="service" className="h-full">
        <div className="border-b">
          <div className="mx-auto flex w-full max-w-5xl items-center justify-between py-4">
            <div className="space-y-1">
              <h1 className="scroll-m-20 text-2xl font-semibold tracking-tight">Sunday Service - July 21, 2024</h1>
              <p className="text-sm text-muted-foreground">Live Service Mode</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center font-medium">
                <Clock className="mr-2 h-4 w-4" />
                1:02:48 / 1:30:00
              </div>
              <Button variant="outline" size="sm">
                <Pause className="mr-2 h-4 w-4" />
                Pause
              </Button>
              <TabsList>
                <TabsTrigger value="service">Service</TabsTrigger>
                <TabsTrigger value="settings">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </TabsTrigger>
              </TabsList>
            </div>
          </div>
        </div>
        <div className="mx-auto flex w-full max-w-5xl h-full">
          <TabsContent value="service" className="h-full">
            <div className="grid h-full grid-cols-4 gap-4 py-4">
              <div className="col-span-3 flex flex-col space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Now Playing</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-4">
                      <Presentation className="h-12 w-12" />
                      <div className="space-y-1">
                        <p className="text-lg font-semibold">{activeElement?.title}</p>
                        <p className="text-sm text-muted-foreground">Assigned to: {activeElement?.assignedTo}</p>
                        <Progress value={activeElement?.progress} className="h-2" />
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>
                            {activeElement?.elapsedTime} / {activeElement?.duration}
                          </span>
                          <span>{activeElement?.progress}%</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="flex-1">
                  <CardHeader>
                    <CardTitle>Service Elements</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2 overflow-auto">
                    {serviceElements.map((element) => (
                      <div key={element.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {element.status === "completed" ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : element.status === "live" ? (
                            <AlertCircle className="h-4 w-4 text-blue-500 animate-pulse" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-gray-500" />
                          )}
                          <div>
                            <p className="text-sm font-medium">{element.title}</p>
                            <p className="text-xs text-muted-foreground">{element.assignedTo}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm">{element.duration} min</p>
                          {element.status === "completed" && (
                            <p className="text-xs text-muted-foreground">Actual: {element.actualDuration} min</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
              <div className="col-span-1 flex flex-col space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Team Communication</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      {messages.map((message) => (
                        <div key={message.id} className="flex items-start space-x-2">
                          <AvatarRoot className="h-8 w-8">
                            <AvatarRootImage src={message.avatar || "/placeholder.svg"} alt={message.sender} />
                            <AvatarRootFallback>{message.sender.substring(0, 2)}</AvatarFallback>
                          </Avatar>
                          <div className="space-y-1">
                            <p className="text-sm font-medium">{message.sender}</p>
                            <p className="text-sm text-muted-foreground">{message.text}</p>
                            <p className="text-xs text-muted-foreground">{message.time}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 flex items-center space-x-2">
                      <Input
                        type="text"
                        placeholder="Type your message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        className="flex-1"
                      />
                      <Button onClick={sendMessage} size="sm">
                        <Send className="mr-2 h-4 w-4" />
                        Send
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Attendees</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm">53 attendees</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="settings">
            <div className="py-4">Settings content</div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
}
