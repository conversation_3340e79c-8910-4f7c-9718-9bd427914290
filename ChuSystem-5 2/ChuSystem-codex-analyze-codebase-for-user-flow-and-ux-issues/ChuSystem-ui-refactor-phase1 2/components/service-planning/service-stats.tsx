import type React from "react"
import { ArrowUp, ArrowDown } from "lucide-react"

interface ServiceStatsProps {
  title: string
  count: number
  icon: React.ReactNode
  trend: "up" | "down" | "neutral"
  percent: string
  comparison: string
}

export default function ServiceStats({ title, count, icon, trend, percent, comparison }: ServiceStatsProps) {
  return (
    <div className="bg-zinc-900/90 rounded-xl border border-zinc-800/20 p-5 shadow-ambient">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm text-zinc-400">{title}</h3>
        <div className="bg-zinc-800/50 p-1.5 rounded-full">{icon}</div>
      </div>

      <div className="text-3xl font-bold mb-2">{count}</div>

      <div className="flex items-center">
        {trend === "up" && (
          <div className="flex items-center text-green-400 text-sm">
            <ArrowUp className="h-4 w-4 mr-1" />
            {percent}
          </div>
        )}
        {trend === "down" && (
          <div className="flex items-center text-red-400 text-sm">
            <ArrowDown className="h-4 w-4 mr-1" />
            {percent}
          </div>
        )}
        {trend === "neutral" && <div className="text-sm">{percent}</div>}
        <span className="text-xs text-zinc-500 ml-2">{comparison}</span>
      </div>
    </div>
  )
}
