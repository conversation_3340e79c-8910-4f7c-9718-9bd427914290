import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Plus } from "lucide-react"

interface TemplateProps {
  id: string
  title: string
  description: string
  elements: number
  duration: string
  isPopular?: boolean
}

const templates: TemplateProps[] = [
  {
    id: "sunday-contemporary",
    title: "Sunday Contemporary",
    description: "Standard contemporary service with worship set, sermon, and response time",
    elements: 12,
    duration: "75 min",
    isPopular: true,
  },
  {
    id: "traditional-liturgy",
    title: "Traditional Liturgy",
    description: "Traditional service with liturgical elements and hymns",
    elements: 15,
    duration: "60 min",
  },
  {
    id: "youth-night",
    title: "Youth Night",
    description: "Energetic service designed for youth with games and interactive elements",
    elements: 10,
    duration: "90 min",
  },
  {
    id: "midweek-prayer",
    title: "Midweek Prayer",
    description: "Focused prayer service with minimal music and extended prayer time",
    elements: 8,
    duration: "45 min",
  },
]

const TemplateCard = ({ template }: { template: TemplateP<PERSON> }) => {
  return (
    <Card
      className={`cursor-pointer transition-all duration-200 hover:border-purple-500/50 ${template.isPopular ? "border-purple-500/50 bg-gradient-to-b from-purple-950/20 to-transparent" : "bg-black/40 border-white/10"}`}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-base font-medium">{template.title}</CardTitle>
          {template.isPopular && (
            <span className="px-2 py-0.5 text-xs rounded-full bg-purple-500/20 text-purple-300 border border-purple-500/30">
              Popular
            </span>
          )}
        </div>
        <CardDescription className="text-xs text-white/60">{template.description}</CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="flex justify-between text-xs text-white/70">
          <span>{template.elements} elements</span>
          <span>{template.duration}</span>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="ghost" size="sm" className="w-full text-xs">
          <FileText className="h-3.5 w-3.5 mr-1.5" />
          Use Template
        </Button>
      </CardFooter>
    </Card>
  )
}

export const TemplateSelector = () => {
  return (
    <div className="space-y-4">
      <Card className="bg-black/40 backdrop-blur-sm border border-white/10 shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg">Service Templates</CardTitle>
          <CardDescription>Start with a template or create from scratch</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {templates.map((template) => (
            <TemplateCard key={template.id} template={template} />
          ))}

          <Card className="cursor-pointer transition-all duration-200 hover:border-white/30 border-dashed border-white/20 bg-white/5">
            <CardContent className="flex flex-col items-center justify-center py-6">
              <Plus className="h-8 w-8 text-white/40 mb-2" />
              <p className="text-sm text-white/60">Create Custom Template</p>
            </CardContent>
          </Card>
        </CardContent>
        <CardFooter>
          <Button variant="link" className="w-full text-purple-300">
            Browse All Templates
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
