"use client"

import { useState } from "react"
import { Search, Music, Filter, Clock, Calendar, Plus, Download, ExternalLink } from "lucide-react"
import { cn } from "@/lib/utils"

// Sample song data
const sampleSongs = [
  {
    id: "song1",
    title: "<PERSON> Grace (My Chains Are Gone)",
    artist: "<PERSON>",
    ccli: "4768151",
    themes: ["<PERSON>", "Redemption"],
    lastUsed: "2 weeks ago",
    key: "G",
    tempo: 72,
    arrangements: ["Contemporary", "Acoustic"],
    duration: "4:30",
  },
  {
    id: "song2",
    title: "How Great Is Our God",
    artist: "<PERSON>",
    ccli: "4348399",
    themes: ["Worship", "God's Attributes"],
    lastUsed: "1 month ago",
    key: "C",
    tempo: 78,
    arrangements: ["Full Band", "Piano Lead"],
    duration: "4:45",
  },
  {
    id: "song3",
    title: "10,000 Reasons (Bless The Lord)",
    artist: "<PERSON>",
    ccli: "6016351",
    themes: ["Worship", "Gratitude"],
    lastUsed: "3 weeks ago",
    key: "D",
    tempo: 73,
    arrangements: ["Full Band", "Acoustic"],
    duration: "5:42",
  },
  {
    id: "song4",
    title: "What A Beautiful Name",
    artist: "Hillsong Worship",
    ccli: "7068424",
    themes: ["Jesus", "Worship"],
    lastUsed: "Last week",
    key: "D",
    tempo: 68,
    arrangements: ["Full Band", "Piano Lead"],
    duration: "5:21",
  },
  {
    id: "song5",
    title: "Great Are You Lord",
    artist: "All Sons & Daughters",
    ccli: "6460220",
    themes: ["Worship", "God's Attributes"],
    lastUsed: "2 months ago",
    key: "A",
    tempo: 75,
    arrangements: ["Full Band", "Acoustic"],
    duration: "5:08",
  },
  {
    id: "song6",
    title: "Cornerstone",
    artist: "Hillsong Worship",
    ccli: "6158927",
    themes: ["Faith", "Foundation"],
    lastUsed: "3 months ago",
    key: "E",
    tempo: 70,
    arrangements: ["Full Band", "Acoustic"],
    duration: "4:15",
  },
]

export function SongLibrary() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSong, setSelectedSong] = useState(null)
  const [filterOpen, setFilterOpen] = useState(false)
  const [activeFilters, setActiveFilters] = useState({
    key: [],
    themes: [],
    arrangements: [],
  })

  // Filter songs based on search term
  const filteredSongs = sampleSongs.filter(
    (song) =>
      song.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      song.artist.toLowerCase().includes(searchTerm.toLowerCase()) ||
      song.themes.some((theme) => theme.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  return (
    <div className="flex flex-col h-full bg-black text-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-800">
        <h1 className="text-xl font-semibold mb-4">Song Library</h1>

        <div className="flex space-x-3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search songs, artists, themes..."
              className="w-full bg-gray-800 rounded-md pl-10 pr-4 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <button
            className={cn(
              "flex items-center px-3 py-2 rounded-md text-sm transition-colors",
              filterOpen ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-800 hover:bg-gray-700",
            )}
            onClick={() => setFilterOpen(!filterOpen)}
          >
            <Filter className="h-4 w-4 mr-1.5" />
            Filters
            {Object.values(activeFilters).some((filters) => filters.length > 0) && (
              <span className="ml-1.5 px-1.5 py-0.5 bg-blue-500 rounded-full text-xs">
                {Object.values(activeFilters).reduce((count, filters) => count + filters.length, 0)}
              </span>
            )}
          </button>

          <button className="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm transition-colors">
            <Plus className="h-4 w-4 mr-1.5" />
            Add Song
          </button>
        </div>

        {/* Filters Panel */}
        {filterOpen && (
          <div className="mt-3 p-3 bg-gray-800 rounded-md">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Key</h3>
                <div className="grid grid-cols-4 gap-2">
                  {["A", "B", "C", "D", "E", "F", "G"].map((key) => (
                    <button
                      key={key}
                      className={cn(
                        "px-2 py-1 text-xs rounded-md",
                        activeFilters.key.includes(key)
                          ? "bg-blue-600 hover:bg-blue-700"
                          : "bg-gray-700 hover:bg-gray-600",
                      )}
                      onClick={() => {
                        setActiveFilters((prev) => ({
                          ...prev,
                          key: prev.key.includes(key) ? prev.key.filter((k) => k !== key) : [...prev.key, key],
                        }))
                      }}
                    >
                      {key}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Themes</h3>
                <div className="flex flex-wrap gap-2">
                  {["Worship", "Grace", "Faith", "Jesus", "Redemption"].map((theme) => (
                    <button
                      key={theme}
                      className={cn(
                        "px-2 py-1 text-xs rounded-md",
                        activeFilters.themes.includes(theme)
                          ? "bg-blue-600 hover:bg-blue-700"
                          : "bg-gray-700 hover:bg-gray-600",
                      )}
                      onClick={() => {
                        setActiveFilters((prev) => ({
                          ...prev,
                          themes: prev.themes.includes(theme)
                            ? prev.themes.filter((t) => t !== theme)
                            : [...prev.themes, theme],
                        }))
                      }}
                    >
                      {theme}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Arrangements</h3>
                <div className="flex flex-wrap gap-2">
                  {["Full Band", "Acoustic", "Piano Lead"].map((arr) => (
                    <button
                      key={arr}
                      className={cn(
                        "px-2 py-1 text-xs rounded-md",
                        activeFilters.arrangements.includes(arr)
                          ? "bg-blue-600 hover:bg-blue-700"
                          : "bg-gray-700 hover:bg-gray-600",
                      )}
                      onClick={() => {
                        setActiveFilters((prev) => ({
                          ...prev,
                          arrangements: prev.arrangements.includes(arr)
                            ? prev.arrangements.filter((a) => a !== arr)
                            : [...prev.arrangements, arr],
                        }))
                      }}
                    >
                      {arr}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-between mt-3 pt-3 border-t border-gray-700">
              <button
                className="text-xs text-gray-400 hover:text-white"
                onClick={() => setActiveFilters({ key: [], themes: [], arrangements: [] })}
              >
                Clear all filters
              </button>
              <button className="text-xs text-blue-400 hover:text-blue-300" onClick={() => setFilterOpen(false)}>
                Apply filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Song List and Details */}
      <div className="flex flex-1 min-h-0">
        {/* Song List */}
        <div className="w-1/2 border-r border-gray-800 overflow-auto">
          <div className="sticky top-0 bg-gray-900 p-3 border-b border-gray-800 text-xs text-gray-400 grid grid-cols-12 gap-2">
            <div className="col-span-5">Song Title</div>
            <div className="col-span-2">Key</div>
            <div className="col-span-3">Themes</div>
            <div className="col-span-2">Last Used</div>
          </div>

          <div>
            {filteredSongs.map((song) => (
              <button
                key={song.id}
                className={cn(
                  "w-full p-3 text-left border-b border-gray-800 hover:bg-gray-900 transition-colors grid grid-cols-12 gap-2",
                  selectedSong?.id === song.id ? "bg-gray-900" : "",
                )}
                onClick={() => setSelectedSong(song)}
              >
                <div className="col-span-5">
                  <p className="font-medium text-sm">{song.title}</p>
                  <p className="text-xs text-gray-400">{song.artist}</p>
                </div>
                <div className="col-span-2 flex items-center">
                  <span className="px-2 py-0.5 bg-gray-800 rounded text-xs">{song.key}</span>
                </div>
                <div className="col-span-3 flex items-center">
                  <p className="text-xs text-gray-400 truncate">{song.themes.join(", ")}</p>
                </div>
                <div className="col-span-2 flex items-center text-xs text-gray-400">
                  <Clock className="h-3 w-3 mr-1" />
                  {song.lastUsed}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Song Details */}
        <div className="w-1/2 overflow-auto">
          {selectedSong ? (
            <div className="p-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-xl font-semibold">{selectedSong.title}</h2>
                  <p className="text-gray-400">{selectedSong.artist}</p>
                </div>
                <div className="flex space-x-2">
                  <button className="flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 rounded-md text-xs transition-colors">
                    <Plus className="h-3.5 w-3.5 mr-1.5" />
                    Add to Service
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="p-3 bg-gray-800 rounded-md">
                  <h3 className="text-xs font-medium text-gray-400 mb-1">Key</h3>
                  <p className="text-2xl font-semibold">{selectedSong.key}</p>
                </div>
                <div className="p-3 bg-gray-800 rounded-md">
                  <h3 className="text-xs font-medium text-gray-400 mb-1">Tempo</h3>
                  <p className="text-2xl font-semibold">
                    {selectedSong.tempo} <span className="text-sm">BPM</span>
                  </p>
                </div>
                <div className="p-3 bg-gray-800 rounded-md">
                  <h3 className="text-xs font-medium text-gray-400 mb-1">Duration</h3>
                  <p className="text-2xl font-semibold">{selectedSong.duration}</p>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Themes</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedSong.themes.map((theme) => (
                    <span key={theme} className="px-2 py-1 bg-gray-800 rounded-md text-xs">
                      {theme}
                    </span>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Arrangements</h3>
                <div className="space-y-2">
                  {selectedSong.arrangements.map((arrangement) => (
                    <div key={arrangement} className="p-3 bg-gray-800 rounded-md">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">{arrangement}</h4>
                        <div className="flex space-x-2">
                          <button className="p-1.5 bg-gray-700 hover:bg-gray-600 rounded-md">
                            <Download className="h-4 w-4" />
                          </button>
                          <button className="p-1.5 bg-gray-700 hover:bg-gray-600 rounded-md">
                            <ExternalLink className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      <div className="mt-2 grid grid-cols-3 gap-2">
                        <button className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs">Chord Chart</button>
                        <button className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs">Lead Sheet</button>
                        <button className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs">Lyrics</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Usage History</h3>
                <div className="space-y-2">
                  <div className="flex items-center p-2 bg-gray-800 rounded-md">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    <div>
                      <p className="text-sm">Sunday Worship - April 28th, 2025</p>
                      <p className="text-xs text-gray-400">2 weeks ago</p>
                    </div>
                  </div>
                  <div className="flex items-center p-2 bg-gray-800 rounded-md">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    <div>
                      <p className="text-sm">Sunday Worship - March 17th, 2025</p>
                      <p className="text-xs text-gray-400">2 months ago</p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">CCLI Information</h3>
                <div className="p-3 bg-gray-800 rounded-md">
                  <p className="text-sm">CCLI Song # {selectedSong.ccli}</p>
                  <p className="text-xs text-gray-400 mt-1">
                    © {new Date().getFullYear() - Math.floor(Math.random() * 10)} {selectedSong.artist} (Admin. by
                    Capitol CMG Publishing)
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
              <Music className="h-12 w-12 mb-4 opacity-50" />
              <p className="text-lg">Select a song to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
