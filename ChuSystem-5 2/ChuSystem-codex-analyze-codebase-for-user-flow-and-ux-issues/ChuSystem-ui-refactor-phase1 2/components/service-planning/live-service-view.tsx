"use client"

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { 
  PlayIcon,
  PauseIcon,
  ForwardIcon,
  BackwardIcon,
  ClockIcon,
  UsersIcon,
  VideoCameraIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
  ComputerDesktopIcon,
  ArrowsPointingOutIcon,
  BellIcon,
  ChatBubbleLeftRightIcon,
  SignalIcon,
  ExclamationTriangleIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { GlassCard } from "@/components/ui/glass-card"
import { Badge } from "@/components/ui/badge"

interface ServiceElement {
  id: string
  type: "song" | "sermon" | "prayer" | "announcement" | "video" | "scripture"
  title: string
  duration: number // in seconds
  leader?: string
  notes?: string
  status: "upcoming" | "active" | "completed"
}

interface LiveServiceViewProps {
  servicePlan: {
    id: string
    title: string
    date: string
    elements: ServiceElement[]
  }
  onClose?: () => void
}

export default function LiveServiceView({ servicePlan, onClose }: LiveServiceViewProps) {
  const [currentElementIndex, setCurrentElementIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [totalElapsed, setTotalElapsed] = useState(0)
  const [showConfidence, setShowConfidence] = useState(true)
  const [activeOutputs, setActiveOutputs] = useState({
    main: true,
    confidence: true,
    stream: true,
    recording: false
  })

  const currentElement = servicePlan.elements[currentElementIndex]

  // Timer logic
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isPlaying) {
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 1)
        setTotalElapsed(prev => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isPlaying])

  // Auto-advance to next element
  useEffect(() => {
    if (currentElement && elapsedTime >= currentElement.duration) {
      handleNext()
    }
  }, [elapsedTime, currentElement])

  const handleNext = () => {
    if (currentElementIndex < servicePlan.elements.length - 1) {
      setCurrentElementIndex(prev => prev + 1)
      setElapsedTime(0)
    } else {
      setIsPlaying(false)
    }
  }

  const handlePrevious = () => {
    if (currentElementIndex > 0) {
      setCurrentElementIndex(prev => prev - 1)
      setElapsedTime(0)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getElementIcon = (type: ServiceElement["type"]) => {
    switch (type) {
      case "song": return <MicrophoneIcon className="h-4 w-4" />
      case "sermon": return <BookOpenIcon className="h-4 w-4" />
      case "prayer": return <HandRaisedIcon className="h-4 w-4" />
      case "announcement": return <MegaphoneIcon className="h-4 w-4" />
      case "video": return <VideoCameraIcon className="h-4 w-4" />
      case "scripture": return <BookOpenIcon className="h-4 w-4" />
    }
  }

  const getProgress = () => {
    if (!currentElement) return 0
    return (elapsedTime / currentElement.duration) * 100
  }

  const getTotalServiceDuration = () => {
    return servicePlan.elements.reduce((acc, el) => acc + el.duration, 0)
  }

  const getOverallProgress = () => {
    const completedDuration = servicePlan.elements
      .slice(0, currentElementIndex)
      .reduce((acc, el) => acc + el.duration, 0)
    const currentProgress = elapsedTime
    return ((completedDuration + currentProgress) / getTotalServiceDuration()) * 100
  }

  return (
    <div className="fixed inset-0 z-50 bg-black flex flex-col">
      {/* Header */}
      <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-bold">{servicePlan.title}</h2>
            <Badge variant="secondary">{servicePlan.date}</Badge>
            <div className="flex items-center gap-2">
              <SignalIcon className="h-4 w-4 text-green-400" />
              <span className="text-sm text-green-400">LIVE</span>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Output Status */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setActiveOutputs(prev => ({ ...prev, main: !prev.main }))}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  activeOutputs.main ? "bg-green-500/20 text-green-400" : "bg-white/10"
                )}
              >
                <ComputerDesktopIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setActiveOutputs(prev => ({ ...prev, confidence: !prev.confidence }))}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  activeOutputs.confidence ? "bg-green-500/20 text-green-400" : "bg-white/10"
                )}
              >
                <UsersIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setActiveOutputs(prev => ({ ...prev, stream: !prev.stream }))}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  activeOutputs.stream ? "bg-green-500/20 text-green-400" : "bg-white/10"
                )}
              >
                <VideoCameraIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setActiveOutputs(prev => ({ ...prev, recording: !prev.recording }))}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  activeOutputs.recording ? "bg-red-500/20 text-red-400" : "bg-white/10"
                )}
              >
                <div className="w-3 h-3 rounded-full bg-current" />
              </button>
            </div>

            <button
              onClick={() => setShowConfidence(!showConfidence)}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <UsersIcon className="h-5 w-5" />
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <ArrowsPointingOutIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Service Timeline */}
        <div className="w-80 bg-[var(--bg-glass)] backdrop-blur-xl border-r border-[var(--border-subtle)] p-4 overflow-y-auto">
          <div className="mb-4">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-zinc-400">Service Progress</span>
              <span>{formatTime(totalElapsed)} / {formatTime(getTotalServiceDuration())}</span>
            </div>
            <div className="h-2 bg-white/10 rounded-full overflow-hidden">
              <div 
                className="h-full bg-[var(--color-primary)] transition-all duration-1000"
                style={{ width: `${getOverallProgress()}%` }}
              />
            </div>
          </div>

          <div className="space-y-2">
            {servicePlan.elements.map((element, idx) => {
              const isActive = idx === currentElementIndex
              const isCompleted = idx < currentElementIndex
              
              return (
                <button
                  key={element.id}
                  onClick={() => {
                    setCurrentElementIndex(idx)
                    setElapsedTime(0)
                  }}
                  className={cn(
                    "w-full p-3 rounded-lg text-left transition-all",
                    isActive 
                      ? "bg-[var(--color-primary)]/20 border border-[var(--color-primary)]/30 scale-105" 
                      : isCompleted
                      ? "bg-white/5 opacity-50"
                      : "bg-white/5 hover:bg-white/10"
                  )}
                >
                  <div className="flex items-start gap-3">
                    <div className={cn(
                      "w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0",
                      isActive ? "bg-[var(--color-primary)]/20" : "bg-white/10"
                    )}>
                      {getElementIcon(element.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{element.title}</p>
                      <div className="flex items-center gap-2 text-xs text-zinc-400">
                        <span>{formatTime(element.duration)}</span>
                        {element.leader && (
                          <>
                            <span>•</span>
                            <span>{element.leader}</span>
                          </>
                        )}
                      </div>
                      {isActive && (
                        <div className="mt-2 h-1 bg-white/10 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-[var(--color-primary)] transition-all duration-1000"
                            style={{ width: `${getProgress()}%` }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Main Display */}
        <div className="flex-1 flex flex-col bg-black">
          {/* Current Element Display */}
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="text-center max-w-4xl">
              <motion.div
                key={currentElement?.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="mb-6">
                  {getElementIcon(currentElement?.type)}
                </div>
                <h1 className="text-6xl font-bold mb-4">{currentElement?.title}</h1>
                {currentElement?.leader && (
                  <p className="text-2xl text-zinc-400 mb-8">{currentElement.leader}</p>
                )}
                {currentElement?.notes && (
                  <div className="p-6 bg-white/5 rounded-xl max-w-2xl mx-auto">
                    <p className="text-lg">{currentElement.notes}</p>
                  </div>
                )}
              </motion.div>
            </div>
          </div>

          {/* Control Bar */}
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-t border-[var(--border-subtle)] p-6">
            <div className="flex items-center justify-between">
              {/* Timer */}
              <div className="flex items-center gap-4">
                <ClockIcon className="h-5 w-5 text-zinc-400" />
                <div>
                  <p className="text-2xl font-mono font-bold">
                    {formatTime(elapsedTime)} / {formatTime(currentElement?.duration || 0)}
                  </p>
                  <p className="text-xs text-zinc-400">Current / Total</p>
                </div>
              </div>

              {/* Playback Controls */}
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={handlePrevious}
                  disabled={currentElementIndex === 0}
                >
                  <BackwardIcon className="h-6 w-6" />
                </Button>
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="w-16 h-16 rounded-full"
                >
                  {isPlaying ? (
                    <PauseIcon className="h-8 w-8" />
                  ) : (
                    <PlayIcon className="h-8 w-8" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={handleNext}
                  disabled={currentElementIndex === servicePlan.elements.length - 1}
                >
                  <ForwardIcon className="h-6 w-6" />
                </Button>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center gap-2">
                <Button variant="glass" size="sm" leftIcon={<BellIcon className="h-4 w-4" />}>
                  Alert Team
                </Button>
                <Button variant="glass" size="sm" leftIcon={<ChatBubbleLeftRightIcon className="h-4 w-4" />}>
                  Messages
                </Button>
                <Button variant="glass" size="sm" leftIcon={<ExclamationTriangleIcon className="h-4 w-4" />}>
                  Emergency
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Confidence Monitor */}
        {showConfidence && (
          <div className="w-80 bg-[var(--bg-glass)] backdrop-blur-xl border-l border-[var(--border-subtle)] p-4">
            <h3 className="text-sm font-medium mb-4">Confidence Monitor</h3>
            
            {/* Next Up */}
            <div className="mb-6">
              <p className="text-xs text-zinc-400 mb-2">Next Up</p>
              {servicePlan.elements[currentElementIndex + 1] && (
                <div className="p-3 bg-white/5 rounded-lg">
                  <p className="text-sm font-medium">
                    {servicePlan.elements[currentElementIndex + 1].title}
                  </p>
                  <p className="text-xs text-zinc-400 mt-1">
                    {servicePlan.elements[currentElementIndex + 1].leader}
                  </p>
                </div>
              )}
            </div>

            {/* Team Status */}
            <div className="mb-6">
              <p className="text-xs text-zinc-400 mb-2">Team Status</p>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-white/5 rounded">
                  <span className="text-sm">Sound</span>
                  <Badge variant="secondary" size="sm">Ready</Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-white/5 rounded">
                  <span className="text-sm">Lights</span>
                  <Badge variant="secondary" size="sm">Ready</Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-white/5 rounded">
                  <span className="text-sm">Video</span>
                  <Badge variant="secondary" size="sm">Standby</Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-white/5 rounded">
                  <span className="text-sm">Stream</span>
                  <Badge variant="secondary" size="sm" className="bg-green-500/20 text-green-400">Live</Badge>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div>
              <p className="text-xs text-zinc-400 mb-2">Team Messages</p>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                <div className="p-2 bg-white/5 rounded text-xs">
                  <p className="font-medium">Sound Team</p>
                  <p className="text-zinc-400">Mic 3 battery low</p>
                </div>
                <div className="p-2 bg-white/5 rounded text-xs">
                  <p className="font-medium">Video Team</p>
                  <p className="text-zinc-400">Camera 2 ready</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Add missing imports
import { BookOpenIcon, HandRaisedIcon, MegaphoneIcon } from "@heroicons/react/24/outline"

export { LiveServiceView }