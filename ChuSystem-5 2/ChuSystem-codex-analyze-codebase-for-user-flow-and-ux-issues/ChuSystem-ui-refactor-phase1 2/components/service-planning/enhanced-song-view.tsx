"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { 
  MusicalNoteIcon,
  PlayIcon,
  PauseIcon,
  KeyIcon,
  ClockIcon,
  HeartIcon,
  ShareIcon,
  PrinterIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ViewColumnsIcon,
  AdjustmentsHorizontalIcon,
  SpeakerWaveIcon,
  VideoCameraIcon,
  DocumentTextIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { GlassCard } from "@/components/ui/glass-card"
import { Badge } from "@/components/ui/badge"

interface EnhancedSongViewProps {
  song: {
    id: string
    title: string
    artist: string
    originalKey: string
    tempo: number
    duration: string
    ccliNumber?: string
    lyrics: string
    chords?: Record<string, string[]>
    arrangement?: string[]
    tags?: string[]
  }
  onClose?: () => void
}

export default function EnhancedSongView({ song, onClose }: EnhancedSongViewProps) {
  const [currentKey, setCurrentKey] = useState(song.originalKey)
  const [fontSize, setFontSize] = useState(16)
  const [scrollSpeed, setScrollSpeed] = useState(30)
  const [isAutoScrolling, setIsAutoScrolling] = useState(false)
  const [viewMode, setViewMode] = useState<"lyrics" | "chords" | "leadsheet">("chords")
  const [columnView, setColumnView] = useState<"single" | "double">("single")
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showChords, setShowChords] = useState(true)
  const [highlightedSection, setHighlightedSection] = useState<string | null>(null)

  const keys = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
  
  const transposeKey = (originalKey: string, steps: number) => {
    const keyIndex = keys.indexOf(originalKey)
    const newIndex = (keyIndex + steps + keys.length) % keys.length
    return keys[newIndex]
  }

  const getTransposeSteps = () => {
    const originalIndex = keys.indexOf(song.originalKey)
    const currentIndex = keys.indexOf(currentKey)
    return (currentIndex - originalIndex + 12) % 12
  }

  const sections = [
    { id: "intro", label: "Intro", time: "0:00" },
    { id: "verse1", label: "Verse 1", time: "0:15" },
    { id: "chorus", label: "Chorus", time: "0:45" },
    { id: "verse2", label: "Verse 2", time: "1:15" },
    { id: "chorus2", label: "Chorus", time: "1:45" },
    { id: "bridge", label: "Bridge", time: "2:15" },
    { id: "chorus3", label: "Chorus", time: "2:45" },
    { id: "outro", label: "Outro", time: "3:15" }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={cn(
        "fixed inset-0 z-50 bg-black/95 backdrop-blur-2xl overflow-hidden",
        isFullscreen && "p-0"
      )}
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <ArrowsPointingInIcon className="h-5 w-5" />
              </button>
              <div>
                <h2 className="text-xl font-bold">{song.title}</h2>
                <p className="text-sm text-zinc-400">{song.artist} • {song.originalKey} • {song.tempo} BPM</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="glass" size="sm" leftIcon={<HeartIcon className="h-4 w-4" />}>
                Favorite
              </Button>
              <Button variant="glass" size="sm" leftIcon={<ShareIcon className="h-4 w-4" />}>
                Share
              </Button>
              <Button variant="glass" size="sm" leftIcon={<PrinterIcon className="h-4 w-4" />}>
                Print
              </Button>
              <Button variant="glass" size="sm" leftIcon={<VideoCameraIcon className="h-4 w-4" />}>
                Present
              </Button>
              <button
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <ArrowsPointingOutIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Control Bar */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] p-3">
          <div className="flex items-center justify-between">
            {/* View Mode */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-zinc-400">View:</span>
              <div className="flex bg-white/10 rounded-lg p-1">
                {[
                  { id: "lyrics", label: "Lyrics" },
                  { id: "chords", label: "Chords" },
                  { id: "leadsheet", label: "Lead Sheet" }
                ].map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => setViewMode(mode.id as any)}
                    className={cn(
                      "px-3 py-1 rounded text-sm transition-colors",
                      viewMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    {mode.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Key Transpose */}
            <div className="flex items-center gap-2">
              <KeyIcon className="h-4 w-4 text-zinc-400" />
              <span className="text-sm text-zinc-400">Key:</span>
              <button
                onClick={() => {
                  const currentIndex = keys.indexOf(currentKey)
                  setCurrentKey(keys[(currentIndex - 1 + keys.length) % keys.length])
                }}
                className="p-1 hover:bg-white/10 rounded"
              >
                <ChevronDownIcon className="h-4 w-4" />
              </button>
              <div className="px-3 py-1 bg-white/10 rounded-lg font-medium min-w-[3rem] text-center">
                {currentKey}
              </div>
              <button
                onClick={() => {
                  const currentIndex = keys.indexOf(currentKey)
                  setCurrentKey(keys[(currentIndex + 1) % keys.length])
                }}
                className="p-1 hover:bg-white/10 rounded"
              >
                <ChevronUpIcon className="h-4 w-4" />
              </button>
              {currentKey !== song.originalKey && (
                <span className="text-xs text-zinc-400">
                  ({getTransposeSteps() > 0 ? '+' : ''}{getTransposeSteps()})
                </span>
              )}
            </div>

            {/* Font Size */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-zinc-400">Size:</span>
              <button
                onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                className="p-1 hover:bg-white/10 rounded text-sm"
              >
                A-
              </button>
              <span className="px-2 text-sm w-12 text-center">{fontSize}</span>
              <button
                onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                className="p-1 hover:bg-white/10 rounded text-sm"
              >
                A+
              </button>
            </div>

            {/* Column View */}
            <button
              onClick={() => setColumnView(columnView === "single" ? "double" : "single")}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <ViewColumnsIcon className="h-5 w-5" />
            </button>

            {/* Auto Scroll */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setIsAutoScrolling(!isAutoScrolling)}
                className={cn(
                  "px-3 py-1 rounded-lg text-sm transition-colors flex items-center gap-2",
                  isAutoScrolling ? "bg-[var(--color-primary)]/20" : "bg-white/10 hover:bg-white/20"
                )}
              >
                {isAutoScrolling ? <PauseIcon className="h-3 w-3" /> : <PlayIcon className="h-3 w-3" />}
                Auto Scroll
              </button>
              {isAutoScrolling && (
                <input
                  type="range"
                  min="10"
                  max="100"
                  value={scrollSpeed}
                  onChange={(e) => setScrollSpeed(Number(e.target.value))}
                  className="w-20"
                />
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Section Navigator */}
          <div className="w-48 bg-[var(--bg-glass)] backdrop-blur-xl border-r border-[var(--border-subtle)] p-4 overflow-y-auto">
            <h3 className="text-sm font-medium mb-3">Sections</h3>
            <div className="space-y-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setHighlightedSection(section.id)}
                  className={cn(
                    "w-full text-left px-3 py-2 rounded-lg text-sm transition-colors",
                    highlightedSection === section.id
                      ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]"
                      : "hover:bg-white/10"
                  )}
                >
                  <div className="flex items-center justify-between">
                    <span>{section.label}</span>
                    <span className="text-xs text-zinc-400">{section.time}</span>
                  </div>
                </button>
              ))}
            </div>

            {/* Song Info */}
            <div className="mt-6 pt-6 border-t border-[var(--border-subtle)]">
              <h3 className="text-sm font-medium mb-3">Song Info</h3>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-zinc-400">CCLI#</span>
                  <span>{song.ccliNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-400">Duration</span>
                  <span>{song.duration}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-400">Original Key</span>
                  <span>{song.originalKey}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-400">Tempo</span>
                  <span>{song.tempo} BPM</span>
                </div>
              </div>
            </div>

            {/* Tags */}
            {song.tags && (
              <div className="mt-6 pt-6 border-t border-[var(--border-subtle)]">
                <h3 className="text-sm font-medium mb-3">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {song.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" size="sm">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Lyrics/Chords Display */}
          <div className="flex-1 overflow-y-auto custom-scrollbar p-8 bg-black/50">
            <div 
              className={cn(
                "max-w-4xl mx-auto",
                columnView === "double" && "columns-2 gap-8"
              )}
              style={{ fontSize: `${fontSize}px`, lineHeight: 1.8 }}
            >
              {/* Sample content with chords */}
              <div className="space-y-6">
                <div>
                  <p className="font-bold text-zinc-400 mb-2">Verse 1:</p>
                  <div className="space-y-2">
                    {showChords && viewMode === "chords" && (
                      <div className="flex gap-4 font-mono text-[var(--color-primary)] text-sm">
                        <span>{currentKey}</span>
                        <span>{transposeKey(currentKey, 5)}</span>
                        <span>{transposeKey(currentKey, -5)}</span>
                        <span>{currentKey}</span>
                      </div>
                    )}
                    <p>Amazing grace, how sweet the sound</p>
                    
                    {showChords && viewMode === "chords" && (
                      <div className="flex gap-4 font-mono text-[var(--color-primary)] text-sm">
                        <span>{currentKey}</span>
                        <span>{transposeKey(currentKey, 5)}</span>
                      </div>
                    )}
                    <p>That saved a wretch like me</p>
                    
                    {showChords && viewMode === "chords" && (
                      <div className="flex gap-4 font-mono text-[var(--color-primary)] text-sm">
                        <span>{currentKey}</span>
                        <span>{transposeKey(currentKey, 5)}</span>
                        <span>{transposeKey(currentKey, -5)}</span>
                        <span>{currentKey}</span>
                      </div>
                    )}
                    <p>I once was lost, but now am found</p>
                    
                    {showChords && viewMode === "chords" && (
                      <div className="flex gap-4 font-mono text-[var(--color-primary)] text-sm">
                        <span>{transposeKey(currentKey, 5)}</span>
                        <span>{currentKey}</span>
                      </div>
                    )}
                    <p>Was blind but now I see</p>
                  </div>
                </div>

                <div>
                  <p className="font-bold text-zinc-400 mb-2">Chorus:</p>
                  <div className="space-y-2">
                    {showChords && viewMode === "chords" && (
                      <div className="flex gap-4 font-mono text-[var(--color-primary)] text-sm">
                        <span>{currentKey}</span>
                        <span>{transposeKey(currentKey, 3)}</span>
                      </div>
                    )}
                    <p>My chains are gone, I've been set free</p>
                    
                    {showChords && viewMode === "chords" && (
                      <div className="flex gap-4 font-mono text-[var(--color-primary)] text-sm">
                        <span>{transposeKey(currentKey, 5)}</span>
                        <span>{currentKey}</span>
                      </div>
                    )}
                    <p>My God, my Savior has ransomed me</p>
                    
                    {showChords && viewMode === "chords" && (
                      <div className="flex gap-4 font-mono text-[var(--color-primary)] text-sm">
                        <span>{transposeKey(currentKey, 3)}</span>
                        <span>{transposeKey(currentKey, -2)}</span>
                      </div>
                    )}
                    <p>And like a flood, His mercy reigns</p>
                    
                    {showChords && viewMode === "chords" && (
                      <div className="flex gap-4 font-mono text-[var(--color-primary)] text-sm">
                        <span>{transposeKey(currentKey, -5)}</span>
                        <span>{transposeKey(currentKey, 5)}</span>
                        <span>{currentKey}</span>
                      </div>
                    )}
                    <p>Unending love, amazing grace</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Arrangement */}
          <div className="w-64 bg-[var(--bg-glass)] backdrop-blur-xl border-l border-[var(--border-subtle)] p-4 overflow-y-auto">
            <h3 className="text-sm font-medium mb-3">Arrangement</h3>
            <div className="space-y-2">
              {song.arrangement?.map((section, idx) => (
                <div
                  key={idx}
                  className="p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                >
                  <p className="text-sm font-medium">{section}</p>
                  <p className="text-xs text-zinc-400">Click to jump</p>
                </div>
              ))}
            </div>

            {/* Chord Progression */}
            <div className="mt-6 pt-6 border-t border-[var(--border-subtle)]">
              <h3 className="text-sm font-medium mb-3">Chord Progression</h3>
              <div className="space-y-2">
                <div className="p-2 bg-white/5 rounded">
                  <p className="text-xs text-zinc-400">Verse</p>
                  <p className="text-sm font-mono">I - V - vi - IV</p>
                </div>
                <div className="p-2 bg-white/5 rounded">
                  <p className="text-xs text-zinc-400">Chorus</p>
                  <p className="text-sm font-mono">I - IV - vi - V</p>
                </div>
                <div className="p-2 bg-white/5 rounded">
                  <p className="text-xs text-zinc-400">Bridge</p>
                  <p className="text-sm font-mono">IV - I - V - vi</p>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mt-6 pt-6 border-t border-[var(--border-subtle)]">
              <h3 className="text-sm font-medium mb-3">Quick Actions</h3>
              <div className="space-y-2">
                <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<DocumentTextIcon className="h-4 w-4" />}>
                  Export Chord Chart
                </Button>
                <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<MusicalNoteIcon className="h-4 w-4" />}>
                  Create Set List
                </Button>
                <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<SpeakerWaveIcon className="h-4 w-4" />}>
                  Play Track
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export { EnhancedSongView }