"use client"

import { useState } from "react"
import { Users, Plus, Check, X, AlertCircle, Mail, MessageSquare, Phone } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import DataCard from "@/components/ui/data-card"

interface TeamMember {
  id: string
  name: string
  role: string
  avatar: string
  status: "confirmed" | "pending" | "unavailable"
  skills: string[]
}

interface TeamViewProps {
  serviceId: string
}

export default function TeamView({ serviceId }: TeamViewProps) {
  // This would normally be fetched from an API
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([
    {
      id: "1",
      name: "<PERSON>",
      role: "Worship Leader",
      avatar: "/avatar2.png",
      status: "confirmed",
      skills: ["Vocals", "Piano", "Worship Leading"],
    },
    {
      id: "2",
      name: "<PERSON>",
      role: "Acoustic Guitar",
      avatar: "/avatar3.png",
      status: "confirmed",
      skills: ["Guitar", "Vocals", "Arrangement"],
    },
    {
      id: "3",
      name: "<PERSON>",
      role: "<PERSON>",
      avatar: "/avatar4.png",
      status: "pending",
      skills: ["Piano", "Keyboard", "Vocals"],
    },
    {
      id: "4",
      name: "<PERSON>",
      role: "Sound Engineer",
      avatar: "/avatar1.png",
      status: "confirmed",
      skills: ["Sound Mixing", "ProPresenter", "Audio Engineering"],
    },
    {
      id: "5",
      name: "David Wilson",
      role: "ProPresenter Operator",
      avatar: "/diverse-group.png",
      status: "unavailable",
      skills: ["ProPresenter", "Media", "Slides"],
    },
  ])

  const [teamGroups, setTeamGroups] = useState([
    { id: "worship", name: "Worship Team", count: 3 },
    { id: "tech", name: "Tech Team", count: 2 },
    { id: "hosting", name: "Hosting Team", count: 1 },
  ])

  const [selectedGroup, setSelectedGroup] = useState("all")

  const filteredMembers =
    selectedGroup === "all"
      ? teamMembers
      : teamMembers.filter((member) => {
          if (selectedGroup === "worship") return ["Worship Leader", "Acoustic Guitar", "Keys"].includes(member.role)
          if (selectedGroup === "tech") return ["Sound Engineer", "ProPresenter Operator"].includes(member.role)
          if (selectedGroup === "hosting") return ["Host", "Greeter"].includes(member.role)
          return true
        })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return <span className="px-2 py-0.5 bg-green-500/20 text-green-400 rounded-full text-xs">Confirmed</span>
      case "pending":
        return <span className="px-2 py-0.5 bg-amber-500/20 text-amber-400 rounded-full text-xs">Pending</span>
      case "unavailable":
        return <span className="px-2 py-0.5 bg-red-500/20 text-red-400 rounded-full text-xs">Unavailable</span>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <DataCard title="Team Overview" tooltip="Summary of team assignments">
          <div className="space-y-3">
            <div className="flex items-center justify-between p-2 border-b border-white/5">
              <span className="text-sm">Total Team Members</span>
              <span className="font-medium">{teamMembers.length}</span>
            </div>
            <div className="flex items-center justify-between p-2 border-b border-white/5">
              <span className="text-sm">Confirmed</span>
              <span className="font-medium">{teamMembers.filter((m) => m.status === "confirmed").length}</span>
            </div>
            <div className="flex items-center justify-between p-2 border-b border-white/5">
              <span className="text-sm">Pending</span>
              <span className="font-medium">{teamMembers.filter((m) => m.status === "pending").length}</span>
            </div>
            <div className="flex items-center justify-between p-2">
              <span className="text-sm">Unavailable</span>
              <span className="font-medium">{teamMembers.filter((m) => m.status === "unavailable").length}</span>
            </div>

            {teamMembers.filter((m) => m.status === "unavailable").length > 0 && (
              <div className="mt-3 p-2 bg-amber-500/10 border border-amber-500/20 rounded-lg flex items-center">
                <AlertCircle className="h-4 w-4 text-amber-400 mr-2" />
                <span className="text-xs text-amber-400">Some positions need to be filled.</span>
              </div>
            )}
          </div>
        </DataCard>

        <DataCard title="Team Notifications" tooltip="Send messages to team members">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="flex-1 rounded-lg">
                <Mail className="h-4 w-4 mr-2" />
                Email
              </Button>
              <Button variant="outline" size="sm" className="flex-1 rounded-lg">
                <MessageSquare className="h-4 w-4 mr-2" />
                Message
              </Button>
              <Button variant="outline" size="sm" className="flex-1 rounded-lg">
                <Phone className="h-4 w-4 mr-2" />
                Call
              </Button>
            </div>
            <textarea
              placeholder="Type a message to send to the team..."
              className="w-full h-24 bg-zinc-800/50 border border-zinc-700/30 rounded-lg p-3 text-sm resize-none"
            ></textarea>
            <Button variant="primary" size="sm" className="w-full rounded-lg">
              Send to All
            </Button>
          </div>
        </DataCard>

        <DataCard title="Quick Actions" tooltip="Common team management tasks">
          <div className="space-y-2">
            <Button variant="outline" size="sm" className="w-full justify-start rounded-lg">
              <Plus className="h-4 w-4 mr-2" />
              Add Team Member
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start rounded-lg">
              <Users className="h-4 w-4 mr-2" />
              Create New Team
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start rounded-lg">
              <Check className="h-4 w-4 mr-2" />
              Send Reminders
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start rounded-lg">
              <X className="h-4 w-4 mr-2" />
              Find Replacements
            </Button>
          </div>
        </DataCard>
      </div>

      <DataCard
        title="Team Assignments"
        tooltip="Manage team roles and assignments"
        actions={
          <div className="flex items-center gap-2">
            <select
              className="bg-zinc-800/50 border border-zinc-700/30 rounded-lg p-1.5 text-xs"
              value={selectedGroup}
              onChange={(e) => setSelectedGroup(e.target.value)}
            >
              <option value="all">All Teams</option>
              {teamGroups.map((group) => (
                <option key={group.id} value={group.id}>
                  {group.name}
                </option>
              ))}
            </select>
            <Button variant="outline" size="sm" className="rounded-lg text-xs">
              <Plus className="h-3.5 w-3.5 mr-1.5" />
              Add Member
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-zinc-800/30 rounded-xl text-sm">
            <div className="w-12"></div>
            <div className="flex-1 font-medium">Name</div>
            <div className="w-32 text-center">Role</div>
            <div className="w-24 text-center">Status</div>
            <div className="w-32 text-center">Actions</div>
          </div>

          {filteredMembers.length === 0 ? (
            <div className="p-8 text-center text-zinc-500">No team members found</div>
          ) : (
            filteredMembers.map((member) => (
              <div
                key={member.id}
                className="flex items-center p-3 bg-zinc-800/50 hover:bg-zinc-800/70 rounded-xl transition-colors"
              >
                <div className="w-12">
                  <img src={member.avatar || "/placeholder.svg"} alt={member.name} className="w-8 h-8 rounded-full" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">{member.name}</div>
                  <div className="text-xs text-zinc-400">{member.skills.join(", ")}</div>
                </div>
                <div className="w-32 text-center">
                  <span className="text-sm">{member.role}</span>
                </div>
                <div className="w-24 text-center">{getStatusBadge(member.status)}</div>
                <div className="w-32 flex justify-center space-x-1">
                  <Button variant="ghost" size="sm" className="h-7 w-7 rounded-lg p-0">
                    <Mail className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-7 w-7 rounded-lg p-0">
                    <MessageSquare className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-7 w-7 rounded-lg p-0">
                    <Phone className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </DataCard>
    </div>
  )
}
