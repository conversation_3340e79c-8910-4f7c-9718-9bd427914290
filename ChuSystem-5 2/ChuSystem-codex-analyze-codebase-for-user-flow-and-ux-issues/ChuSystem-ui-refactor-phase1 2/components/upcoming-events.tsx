import { CalendarIcon, ClockIcon, MapPinIcon, UserGroupIcon } from "@heroicons/react/24/outline"

export default function UpcomingEvents() {
  const events = [
    {
      id: 1,
      title: "Sunday Service",
      date: "Sunday, May 19",
      time: "9:00 AM - 11:00 AM",
      location: "Main Sanctuary",
      attendees: 120,
    },
    {
      id: 2,
      title: "Youth Group Meeting",
      date: "Wednesday, May 22",
      time: "6:30 PM - 8:00 PM",
      location: "Youth Center",
      attendees: 35,
    },
    {
      id: 3,
      title: "Worship Team Rehearsal",
      date: "Thursday, May 23",
      time: "7:00 PM - 9:00 PM",
      location: "Main Sanctuary",
      attendees: 12,
    },
  ]

  return (
    <div className="space-y-4">
      {events.map((event) => (
        <div
          key={event.id}
          className="bg-zinc-800/50 rounded-xl p-3 border border-zinc-700/20 hover:border-zinc-700/40 transition-all cursor-pointer"
        >
          <h4 className="font-medium mb-2">{event.title}</h4>
          <div className="grid grid-cols-2 gap-y-2 text-xs text-zinc-400">
            <div className="flex items-center gap-1.5">
              <CalendarIcon className="h-3.5 w-3.5" />
              <span>{event.date}</span>
            </div>
            <div className="flex items-center gap-1.5">
              <ClockIcon className="h-3.5 w-3.5" />
              <span>{event.time}</span>
            </div>
            <div className="flex items-center gap-1.5">
              <MapPinIcon className="h-3.5 w-3.5" />
              <span>{event.location}</span>
            </div>
            <div className="flex items-center gap-1.5">
              <UserGroupIcon className="h-3.5 w-3.5" />
              <span>{event.attendees} attendees</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
