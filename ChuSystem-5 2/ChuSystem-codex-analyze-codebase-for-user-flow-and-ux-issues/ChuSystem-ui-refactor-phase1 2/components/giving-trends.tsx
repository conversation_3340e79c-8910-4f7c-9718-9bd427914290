import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline"

export default function GivingTrends() {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]
  const generalFund = [18500, 19200, 17800, 21500, 24850, 0]
  const buildingFund = [5200, 4800, 6100, 5700, 6500, 0]
  const missionsFund = [3100, 2900, 3500, 3200, 3800, 0]

  const fundComparisons = [
    {
      name: "General Fund",
      current: "$24,850",
      previous: "$21,500",
      change: "+15.6%",
      trend: "up",
    },
    {
      name: "Building Fund",
      current: "$6,500",
      previous: "$5,700",
      change: "+14.0%",
      trend: "up",
    },
    {
      name: "Missions Fund",
      current: "$3,800",
      previous: "$3,200",
      change: "+18.8%",
      trend: "up",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="bg-zinc-900/90 rounded-xl p-4 border border-zinc-800/20">
        <h3 className="text-sm font-medium mb-4">Monthly Giving Trends</h3>
        <div className="h-64 relative">
          {/* Chart bars */}
          <div className="absolute inset-0 flex items-end justify-between px-2">
            {months.map((month, index) => (
              <div key={index} className="flex flex-col items-center space-y-1 w-1/6">
                {/* General Fund */}
                <div
                  className="w-full max-w-[30px] bg-blue-500 rounded-t-sm"
                  style={{
                    height: `${(generalFund[index] / 30000) * 100}%`,
                    opacity: month === "Jun" ? 0.5 : 1,
                  }}
                ></div>
                {/* Building Fund */}
                <div
                  className="w-full max-w-[30px] bg-purple-500 rounded-t-sm"
                  style={{
                    height: `${(buildingFund[index] / 30000) * 100}%`,
                    opacity: month === "Jun" ? 0.5 : 1,
                  }}
                ></div>
                {/* Missions Fund */}
                <div
                  className="w-full max-w-[30px] bg-green-500 rounded-t-sm"
                  style={{
                    height: `${(missionsFund[index] / 30000) * 100}%`,
                    opacity: month === "Jun" ? 0.5 : 1,
                  }}
                ></div>
                <div className="text-xs text-zinc-500 mt-2">{month}</div>
              </div>
            ))}
          </div>

          {/* Y-axis labels */}
          <div className="absolute inset-y-0 left-0 flex flex-col justify-between text-xs text-zinc-500 py-4">
            <div>$30k</div>
            <div>$20k</div>
            <div>$10k</div>
            <div>$0</div>
          </div>
        </div>

        <div className="flex items-center justify-center space-x-6 mt-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-sm mr-2"></div>
            <span className="text-xs">General Fund</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-purple-500 rounded-sm mr-2"></div>
            <span className="text-xs">Building Fund</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-sm mr-2"></div>
            <span className="text-xs">Missions Fund</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        {fundComparisons.map((fund, index) => (
          <div
            key={index}
            className="bg-zinc-900/90 rounded-xl p-4 border border-zinc-800/20 hover:border-zinc-700/30 transition-all"
          >
            <h3 className="text-sm font-medium mb-2">{fund.name}</h3>
            <div className="flex items-end gap-2">
              <div className="text-xl font-semibold">{fund.current}</div>
              <div
                className={`flex items-center text-xs font-medium ${
                  fund.trend === "up" ? "text-green-500" : "text-red-500"
                }`}
              >
                {fund.trend === "up" ? (
                  <ArrowUpIcon className="h-3.5 w-3.5 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-3.5 w-3.5 mr-1" />
                )}
                {fund.change}
              </div>
            </div>
            <div className="text-xs text-zinc-500 mt-1">vs {fund.previous} last month</div>
          </div>
        ))}
      </div>
    </div>
  )
}
