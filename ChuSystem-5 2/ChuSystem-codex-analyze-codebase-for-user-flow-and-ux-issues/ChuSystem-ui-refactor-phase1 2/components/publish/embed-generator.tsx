"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Card from "@/components/ui/card"
import { motion } from "framer-motion"
import { 
  CodeBracketIcon,
  DocumentDuplicateIcon,
  CheckCircleIcon,
  AdjustmentsHorizontalIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  BanknotesIcon,
  CalendarDaysIcon,
  HeartIcon,
  HandRaisedIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  ClockIcon,
  UserGroupIcon,
  GlobeAltIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  EyeIcon,
  PaintBrushIcon,
  ShieldCheckIcon,
  SparklesIcon
} from "@heroicons/react/24/outline"

interface EmbedGeneratorProps {
  project: any
}

type WidgetType = "giving" | "events" | "connect" | "prayer" | "stream" | "times" | "full"
type EmbedStyle = "popup" | "inline" | "floating" | "banner"
type ColorScheme = "auto" | "light" | "dark" | "custom"

interface WidgetConfig {
  type: WidgetType
  style: EmbedStyle
  position?: string
  width?: string
  height?: string
  colorScheme: ColorScheme
  primaryColor?: string
  showHeader?: boolean
  showBranding?: boolean
  autoOpen?: boolean
  delay?: number
  customCSS?: string
}

export default function EmbedGenerator({ project }: EmbedGeneratorProps) {
  const [selectedWidget, setSelectedWidget] = useState<WidgetType>("giving")
  const [widgetConfig, setWidgetConfig] = useState<WidgetConfig>({
    type: "giving",
    style: "popup",
    position: "bottom-right",
    width: "400px",
    height: "auto",
    colorScheme: "auto",
    showHeader: true,
    showBranding: true,
    autoOpen: false,
    delay: 3000
  })
  const [copied, setCopied] = useState(false)
  const [showPreview, setShowPreview] = useState(true)

  const widgets = [
    { id: "giving", label: "Giving Widget", icon: BanknotesIcon, description: "Accept donations directly on any website" },
    { id: "events", label: "Events Calendar", icon: CalendarDaysIcon, description: "Display upcoming events" },
    { id: "connect", label: "Connect Card", icon: HandRaisedIcon, description: "Capture visitor information" },
    { id: "prayer", label: "Prayer Requests", icon: HeartIcon, description: "Collect prayer requests" },
    { id: "stream", label: "Live Stream", icon: VideoCameraIcon, description: "Embed live services" },
    { id: "times", label: "Service Times", icon: ClockIcon, description: "Show service schedules" },
    { id: "full", label: "Full App", icon: GlobeAltIcon, description: "Embed entire app experience" }
  ]

  const embedStyles = [
    { id: "popup", label: "Popup Modal", description: "Opens in a modal overlay" },
    { id: "inline", label: "Inline Embed", description: "Embedded directly in page" },
    { id: "floating", label: "Floating Button", description: "Fixed position button" },
    { id: "banner", label: "Banner", description: "Top or bottom banner" }
  ]

  const generateEmbedCode = () => {
    const baseUrl = `https://${project.url || "yourchurch.app"}`
    const widgetUrl = `${baseUrl}/widgets/${widgetConfig.type}`
    
    const config = {
      ...widgetConfig,
      churchId: project.id,
      theme: project.theme
    }

    const scriptTag = `<script src="${baseUrl}/widget.js" async></script>`
    const divTag = `<div 
  id="church-widget" 
  data-widget='${JSON.stringify(config)}'
></div>`

    return `<!-- ${project.name} ${widgets.find(w => w.id === selectedWidget)?.label} -->
${scriptTag}
${divTag}
<!-- End Widget -->`
  }

  const generateIframeCode = () => {
    const baseUrl = `https://${project.url || "yourchurch.app"}`
    const widgetUrl = `${baseUrl}/widgets/${widgetConfig.type}`
    
    return `<iframe 
  src="${widgetUrl}?theme=${project.theme}"
  width="${widgetConfig.width}"
  height="${widgetConfig.height}"
  frameborder="0"
  style="border: none; border-radius: 12px; overflow: hidden;"
  allow="payment"
></iframe>`
  }

  const handleCopy = (code: string) => {
    navigator.clipboard.writeText(code)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const updateConfig = (key: keyof WidgetConfig, value: any) => {
    setWidgetConfig({ ...widgetConfig, [key]: value })
  }

  return (
    <Card variant="glass">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold">Embed Widgets</h3>
            <p className="text-sm text-zinc-400">Add church features to any website</p>
          </div>
          <Badge variant="secondary">
            <ShieldCheckIcon className="h-3 w-3 mr-1" />
            Secure Embed
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-6">
          {/* Left Column - Configuration */}
          <div className="space-y-6">
            {/* Widget Type Selection */}
            <div>
              <label className="text-sm font-medium mb-3 block">Choose Widget</label>
              <div className="grid grid-cols-2 gap-2">
                {widgets.map((widget) => (
                  <button
                    key={widget.id}
                    onClick={() => {
                      setSelectedWidget(widget.id as WidgetType)
                      updateConfig("type", widget.id)
                    }}
                    className={cn(
                      "p-3 rounded-lg border text-left transition-all",
                      selectedWidget === widget.id
                        ? "bg-[var(--color-primary)]/10 border-[var(--color-primary)]"
                        : "bg-white/5 border-white/10 hover:border-white/20"
                    )}
                  >
                    <widget.icon className="h-5 w-5 mb-2" />
                    <p className="text-sm font-medium">{widget.label}</p>
                    <p className="text-xs text-zinc-400 mt-1">{widget.description}</p>
                  </button>
                ))}
              </div>
            </div>

            {/* Embed Style */}
            <div>
              <label className="text-sm font-medium mb-3 block">Embed Style</label>
              <div className="space-y-2">
                {embedStyles.map((style) => (
                  <label
                    key={style.id}
                    className={cn(
                      "flex items-center p-3 rounded-lg border cursor-pointer transition-all",
                      widgetConfig.style === style.id
                        ? "bg-[var(--color-primary)]/10 border-[var(--color-primary)]"
                        : "bg-white/5 border-white/10 hover:border-white/20"
                    )}
                  >
                    <input
                      type="radio"
                      name="embedStyle"
                      value={style.id}
                      checked={widgetConfig.style === style.id}
                      onChange={(e) => updateConfig("style", e.target.value as EmbedStyle)}
                      className="mr-3"
                    />
                    <div>
                      <p className="text-sm font-medium">{style.label}</p>
                      <p className="text-xs text-zinc-400">{style.description}</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Appearance Options */}
            <div>
              <label className="text-sm font-medium mb-3 block">Appearance</label>
              <div className="space-y-3">
                {/* Color Scheme */}
                <div>
                  <label className="text-xs text-zinc-400 block mb-1">Color Scheme</label>
                  <select
                    value={widgetConfig.colorScheme}
                    onChange={(e) => updateConfig("colorScheme", e.target.value as ColorScheme)}
                    className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg"
                  >
                    <option value="auto">Auto (Match Website)</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>

                {widgetConfig.colorScheme === "custom" && (
                  <div>
                    <label className="text-xs text-zinc-400 block mb-1">Primary Color</label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        value={widgetConfig.primaryColor || "#6366f1"}
                        onChange={(e) => updateConfig("primaryColor", e.target.value)}
                        className="w-10 h-10 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={widgetConfig.primaryColor || "#6366f1"}
                        onChange={(e) => updateConfig("primaryColor", e.target.value)}
                        className="flex-1 px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-sm"
                      />
                    </div>
                  </div>
                )}

                {/* Size Options */}
                {widgetConfig.style === "inline" && (
                  <>
                    <div>
                      <label className="text-xs text-zinc-400 block mb-1">Width</label>
                      <input
                        type="text"
                        value={widgetConfig.width}
                        onChange={(e) => updateConfig("width", e.target.value)}
                        placeholder="400px or 100%"
                        className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-sm"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-zinc-400 block mb-1">Height</label>
                      <input
                        type="text"
                        value={widgetConfig.height}
                        onChange={(e) => updateConfig("height", e.target.value)}
                        placeholder="500px or auto"
                        className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-sm"
                      />
                    </div>
                  </>
                )}

                {/* Position for floating */}
                {widgetConfig.style === "floating" && (
                  <div>
                    <label className="text-xs text-zinc-400 block mb-1">Position</label>
                    <select
                      value={widgetConfig.position}
                      onChange={(e) => updateConfig("position", e.target.value)}
                      className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg"
                    >
                      <option value="bottom-right">Bottom Right</option>
                      <option value="bottom-left">Bottom Left</option>
                      <option value="top-right">Top Right</option>
                      <option value="top-left">Top Left</option>
                    </select>
                  </div>
                )}

                {/* Options */}
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={widgetConfig.showHeader}
                      onChange={(e) => updateConfig("showHeader", e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Show header</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={widgetConfig.showBranding}
                      onChange={(e) => updateConfig("showBranding", e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Show church branding</span>
                  </label>
                  {widgetConfig.style === "popup" && (
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={widgetConfig.autoOpen}
                        onChange={(e) => updateConfig("autoOpen", e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm">Auto-open after delay</span>
                    </label>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Code & Preview */}
          <div className="space-y-6">
            {/* Preview Toggle */}
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Generated Code</h4>
              <Button
                variant="glass"
                size="sm"
                leftIcon={<EyeIcon className="h-4 w-4" />}
                onClick={() => setShowPreview(!showPreview)}
              >
                {showPreview ? "Hide" : "Show"} Preview
              </Button>
            </div>

            {/* Embed Code */}
            <div className="space-y-3">
              <div className="relative">
                <div className="bg-black/50 rounded-lg p-4 font-mono text-xs overflow-x-auto">
                  <pre className="text-green-400">{generateEmbedCode()}</pre>
                </div>
                <Button
                  variant="glass"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={() => handleCopy(generateEmbedCode())}
                >
                  {copied ? (
                    <>
                      <CheckCircleIcon className="h-4 w-4 mr-1" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <DocumentDuplicateIcon className="h-4 w-4 mr-1" />
                      Copy
                    </>
                  )}
                </Button>
              </div>

              {/* Alternative iframe code */}
              <details className="group">
                <summary className="cursor-pointer text-sm text-zinc-400 hover:text-white">
                  Show iframe code (alternative)
                </summary>
                <div className="mt-3 relative">
                  <div className="bg-black/50 rounded-lg p-4 font-mono text-xs overflow-x-auto">
                    <pre className="text-blue-400">{generateIframeCode()}</pre>
                  </div>
                  <Button
                    variant="glass"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={() => handleCopy(generateIframeCode())}
                  >
                    <DocumentDuplicateIcon className="h-4 w-4" />
                  </Button>
                </div>
              </details>
            </div>

            {/* Preview */}
            {showPreview && (
              <div>
                <h4 className="text-sm font-medium mb-3">Preview</h4>
                <div className="bg-zinc-900 rounded-lg p-4 h-64 relative overflow-hidden">
                  {widgetConfig.style === "floating" && (
                    <motion.button
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className={cn(
                        "absolute p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full shadow-lg",
                        widgetConfig.position === "bottom-right" && "bottom-4 right-4",
                        widgetConfig.position === "bottom-left" && "bottom-4 left-4",
                        widgetConfig.position === "top-right" && "top-4 right-4",
                        widgetConfig.position === "top-left" && "top-4 left-4"
                      )}
                    >
                      {widgets.find(w => w.id === selectedWidget)?.icon && 
                        React.createElement(widgets.find(w => w.id === selectedWidget)!.icon, {
                          className: "h-6 w-6 text-white"
                        })
                      }
                    </motion.button>
                  )}

                  {widgetConfig.style === "inline" && (
                    <div className="bg-white/5 rounded-lg p-4 h-full flex items-center justify-center">
                      <div className="text-center">
                        {widgets.find(w => w.id === selectedWidget)?.icon && 
                          React.createElement(widgets.find(w => w.id === selectedWidget)!.icon, {
                            className: "h-12 w-12 mx-auto mb-3 text-zinc-400"
                          })
                        }
                        <p className="text-sm text-zinc-400">
                          {widgets.find(w => w.id === selectedWidget)?.label} Preview
                        </p>
                      </div>
                    </div>
                  )}

                  {widgetConfig.style === "popup" && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <motion.div
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        className="bg-[var(--bg-card)] border border-white/10 rounded-xl p-6 max-w-sm w-full"
                      >
                        <h3 className="font-semibold mb-2">
                          {widgets.find(w => w.id === selectedWidget)?.label}
                        </h3>
                        <p className="text-sm text-zinc-400">Modal preview</p>
                      </motion.div>
                    </div>
                  )}

                  {widgetConfig.style === "banner" && (
                    <div className="absolute top-0 inset-x-0 bg-gradient-to-r from-blue-500 to-purple-600 p-3">
                      <p className="text-center text-sm font-medium">
                        {widgets.find(w => w.id === selectedWidget)?.label} Banner
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Integration Help */}
            <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <div className="flex items-start gap-3">
                <SparklesIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium mb-1">Quick Integration</p>
                  <p className="text-xs text-zinc-400">
                    Simply paste the code into your website's HTML, preferably before the closing &lt;/body&gt; tag.
                    The widget will automatically load and configure itself.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}