"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Card from "@/components/ui/card"
import { AnimatePresence, motion } from "framer-motion"
import { 
  SwatchIcon,
  SparklesIcon,
  CheckCircleIcon,
  PaintBrushIcon,
  EyeIcon,
  CodeBracketIcon,
  CommandLineIcon,
  AdjustmentsHorizontalIcon,
  LightBulbIcon,
  SunIcon,
  MoonIcon,
  HeartIcon,
  GlobeAltIcon,
  FireIcon,
  DocumentDuplicateIcon
} from "@heroicons/react/24/outline"

interface Theme {
  id: string
  name: string
  description: string
  preview: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
    textMuted: string
    border: string
  }
  fonts: {
    heading: string
    body: string
    mono: string
  }
  style: "modern" | "classic" | "minimal" | "bold" | "elegant" | "playful"
  features: string[]
  animations: {
    enabled: boolean
    speed: "slow" | "normal" | "fast"
    type: "subtle" | "smooth" | "energetic"
  }
}

interface ThemeSelectorProps {
  selectedTheme?: string
  onSelectTheme: (themeId: string) => void
}

export default function ThemeSelector({ selectedTheme, onSelectTheme }: ThemeSelectorProps) {
  const [showCustomizer, setShowCustomizer] = useState(false)
  const [customizingTheme, setCustomizingTheme] = useState<Theme | null>(null)
  const [previewMode, setPreviewMode] = useState<"light" | "dark">("dark")

  // Stunning theme collection
  const themes: Theme[] = [
    {
      id: "modern-glass",
      name: "Modern Glass",
      description: "Sleek glassmorphism with vibrant gradients",
      preview: "/theme-modern-glass.jpg",
      colors: {
        primary: "#6366f1",
        secondary: "#ec4899",
        accent: "#10b981",
        background: "#000000",
        surface: "rgba(255, 255, 255, 0.05)",
        text: "#ffffff",
        textMuted: "#94a3b8",
        border: "rgba(255, 255, 255, 0.1)"
      },
      fonts: {
        heading: "Inter",
        body: "Inter",
        mono: "JetBrains Mono"
      },
      style: "modern",
      features: ["Glassmorphism", "Gradients", "Dark Mode", "Animations"],
      animations: {
        enabled: true,
        speed: "normal",
        type: "smooth"
      }
    },
    {
      id: "celebration",
      name: "Celebration",
      description: "Vibrant and joyful for special events",
      preview: "/theme-celebration.jpg",
      colors: {
        primary: "#f59e0b",
        secondary: "#3b82f6",
        accent: "#ec4899",
        background: "#fef3c7",
        surface: "#ffffff",
        text: "#1f2937",
        textMuted: "#6b7280",
        border: "#fde68a"
      },
      fonts: {
        heading: "Poppins",
        body: "Open Sans",
        mono: "Fira Code"
      },
      style: "playful",
      features: ["Bright Colors", "Festive", "Confetti", "Light Mode"],
      animations: {
        enabled: true,
        speed: "fast",
        type: "energetic"
      }
    },
    {
      id: "trust",
      name: "Trust & Serenity",
      description: "Calm and trustworthy for giving and connections",
      preview: "/theme-trust.jpg",
      colors: {
        primary: "#10b981",
        secondary: "#6366f1",
        accent: "#0ea5e9",
        background: "#f0f9ff",
        surface: "#ffffff",
        text: "#0f172a",
        textMuted: "#64748b",
        border: "#e0f2fe"
      },
      fonts: {
        heading: "Roboto",
        body: "Roboto",
        mono: "Roboto Mono"
      },
      style: "classic",
      features: ["Professional", "Clean", "Trustworthy", "Accessible"],
      animations: {
        enabled: true,
        speed: "slow",
        type: "subtle"
      }
    },
    {
      id: "minimal-light",
      name: "Minimal Light",
      description: "Clean and minimalist with perfect typography",
      preview: "/theme-minimal.jpg",
      colors: {
        primary: "#000000",
        secondary: "#6b7280",
        accent: "#ef4444",
        background: "#ffffff",
        surface: "#fafafa",
        text: "#000000",
        textMuted: "#6b7280",
        border: "#e5e7eb"
      },
      fonts: {
        heading: "Helvetica Neue",
        body: "Helvetica Neue",
        mono: "SF Mono"
      },
      style: "minimal",
      features: ["Minimalist", "Typography Focus", "White Space", "Clean"],
      animations: {
        enabled: false,
        speed: "normal",
        type: "subtle"
      }
    },
    {
      id: "elegant-dark",
      name: "Elegant Noir",
      description: "Sophisticated dark theme with gold accents",
      preview: "/theme-elegant.jpg",
      colors: {
        primary: "#d4af37",
        secondary: "#ffffff",
        accent: "#ef4444",
        background: "#0a0a0a",
        surface: "#1a1a1a",
        text: "#ffffff",
        textMuted: "#a3a3a3",
        border: "#262626"
      },
      fonts: {
        heading: "Playfair Display",
        body: "Lato",
        mono: "IBM Plex Mono"
      },
      style: "elegant",
      features: ["Luxury Feel", "Gold Accents", "Sophisticated", "Premium"],
      animations: {
        enabled: true,
        speed: "slow",
        type: "subtle"
      }
    },
    {
      id: "nature",
      name: "Nature's Grace",
      description: "Organic and warm with earthy tones",
      preview: "/theme-nature.jpg",
      colors: {
        primary: "#059669",
        secondary: "#d97706",
        accent: "#7c3aed",
        background: "#fefef9",
        surface: "#ffffff",
        text: "#064e3b",
        textMuted: "#059669",
        border: "#d1fae5"
      },
      fonts: {
        heading: "Merriweather",
        body: "Source Sans Pro",
        mono: "Source Code Pro"
      },
      style: "classic",
      features: ["Organic", "Warm", "Natural", "Eco-friendly"],
      animations: {
        enabled: true,
        speed: "normal",
        type: "smooth"
      }
    },
    {
      id: "bold-impact",
      name: "Bold Impact",
      description: "High contrast and bold for maximum impact",
      preview: "/theme-bold.jpg",
      colors: {
        primary: "#dc2626",
        secondary: "#1e40af",
        accent: "#facc15",
        background: "#ffffff",
        surface: "#f3f4f6",
        text: "#000000",
        textMuted: "#4b5563",
        border: "#d1d5db"
      },
      fonts: {
        heading: "Bebas Neue",
        body: "Montserrat",
        mono: "Space Mono"
      },
      style: "bold",
      features: ["High Contrast", "Bold Typography", "Impactful", "Dynamic"],
      animations: {
        enabled: true,
        speed: "fast",
        type: "energetic"
      }
    },
    {
      id: "midnight-aurora",
      name: "Midnight Aurora",
      description: "Dark theme with aurora-inspired gradients",
      preview: "/theme-aurora.jpg",
      colors: {
        primary: "#8b5cf6",
        secondary: "#06b6d4",
        accent: "#10b981",
        background: "#030712",
        surface: "rgba(139, 92, 246, 0.05)",
        text: "#f9fafb",
        textMuted: "#9ca3af",
        border: "rgba(139, 92, 246, 0.2)"
      },
      fonts: {
        heading: "Space Grotesk",
        body: "Inter",
        mono: "Fira Code"
      },
      style: "modern",
      features: ["Aurora Gradients", "Dark Mode", "Futuristic", "Glow Effects"],
      animations: {
        enabled: true,
        speed: "normal",
        type: "smooth"
      }
    }
  ]

  const handleCustomizeTheme = (theme: Theme) => {
    setCustomizingTheme(theme)
    setShowCustomizer(true)
  }

  const duplicateTheme = (theme: Theme) => {
    const newTheme = {
      ...theme,
      id: `${theme.id}-custom-${Date.now()}`,
      name: `${theme.name} (Custom)`
    }
    console.log("Duplicate theme:", newTheme)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold mb-2">Choose Your Theme</h2>
          <p className="text-zinc-400">Select a theme that matches your church's personality and brand</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="glass"
            size="sm"
            leftIcon={previewMode === "dark" ? <SunIcon className="h-4 w-4" /> : <MoonIcon className="h-4 w-4" />}
            onClick={() => setPreviewMode(previewMode === "dark" ? "light" : "dark")}
          >
            {previewMode === "dark" ? "Light" : "Dark"} Preview
          </Button>
          <Button
            variant="primary"
            size="sm"
            leftIcon={<SparklesIcon className="h-4 w-4" />}
            onClick={() => console.log("Generate with AI")}
          >
            AI Theme Generator
          </Button>
        </div>
      </div>

      {/* Themes Grid */}
      <div className="grid grid-cols-2 xl:grid-cols-3 gap-6">
        {themes.map((theme) => (
          <motion.div
            key={theme.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ y: -4 }}
          >
            <Card
              variant="glass"
              className={cn(
                "relative overflow-hidden cursor-pointer transition-all",
                selectedTheme === theme.id && "ring-2 ring-[var(--color-primary)]"
              )}
              onClick={() => onSelectTheme(theme.id)}
            >
              {/* Theme Preview */}
              <div className="aspect-[4/3] relative overflow-hidden">
                <div 
                  className="absolute inset-0"
                  style={{ backgroundColor: theme.colors.background }}
                >
                  {/* Preview Content */}
                  <div className="p-6 h-full flex flex-col">
                    {/* Navigation Bar */}
                    <div 
                      className="flex items-center justify-between p-3 rounded-lg mb-4"
                      style={{ backgroundColor: theme.colors.surface, borderColor: theme.colors.border, borderWidth: 1 }}
                    >
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-8 h-8 rounded-lg"
                          style={{ backgroundColor: theme.colors.primary }}
                        />
                        <div className="space-y-1">
                          <div 
                            className="h-2 w-20 rounded"
                            style={{ backgroundColor: theme.colors.text, opacity: 0.8 }}
                          />
                          <div 
                            className="h-1.5 w-16 rounded"
                            style={{ backgroundColor: theme.colors.textMuted }}
                          />
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {[theme.colors.primary, theme.colors.secondary, theme.colors.accent].map((color, idx) => (
                          <div 
                            key={idx}
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: color }}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Hero Section */}
                    <div 
                      className="flex-1 rounded-lg p-4 flex flex-col items-center justify-center text-center"
                      style={{ 
                        background: `linear-gradient(135deg, ${theme.colors.primary}20, ${theme.colors.secondary}20)`,
                        borderColor: theme.colors.border,
                        borderWidth: 1
                      }}
                    >
                      <div 
                        className="w-12 h-12 rounded-full mb-3"
                        style={{ backgroundColor: theme.colors.primary }}
                      />
                      <div 
                        className="h-3 w-24 rounded mb-2"
                        style={{ backgroundColor: theme.colors.text }}
                      />
                      <div 
                        className="h-2 w-32 rounded"
                        style={{ backgroundColor: theme.colors.textMuted }}
                      />
                    </div>

                    {/* Cards */}
                    <div className="grid grid-cols-3 gap-2 mt-4">
                      {[theme.colors.primary, theme.colors.secondary, theme.colors.accent].map((color, idx) => (
                        <div 
                          key={idx}
                          className="h-12 rounded"
                          style={{ backgroundColor: theme.colors.surface, borderColor: color, borderWidth: 1 }}
                        />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Selected Indicator */}
                {selectedTheme === theme.id && (
                  <div className="absolute top-3 right-3 w-8 h-8 bg-[var(--color-primary)] rounded-full flex items-center justify-center">
                    <CheckCircleIcon className="h-5 w-5 text-black" />
                  </div>
                )}
              </div>

              {/* Theme Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="font-semibold text-lg">{theme.name}</h3>
                    <p className="text-sm text-zinc-400">{theme.description}</p>
                  </div>
                  <Badge variant="secondary" size="sm">{theme.style}</Badge>
                </div>

                {/* Features */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {theme.features.slice(0, 3).map((feature) => (
                    <span key={feature} className="text-xs bg-white/5 px-2 py-1 rounded">
                      {feature}
                    </span>
                  ))}
                  {theme.features.length > 3 && (
                    <span className="text-xs text-zinc-400">+{theme.features.length - 3}</span>
                  )}
                </div>

                {/* Fonts */}
                <div className="flex items-center justify-between text-xs text-zinc-400 mb-3">
                  <span style={{ fontFamily: theme.fonts.heading }}>{theme.fonts.heading}</span>
                  <span>•</span>
                  <span style={{ fontFamily: theme.fonts.body }}>{theme.fonts.body}</span>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="glass"
                    size="sm"
                    className="flex-1"
                    leftIcon={<EyeIcon className="h-4 w-4" />}
                    onClick={(e) => {
                      e.stopPropagation()
                      console.log("Preview theme")
                    }}
                  >
                    Preview
                  </Button>
                  <Button
                    variant="glass"
                    size="sm"
                    leftIcon={<PaintBrushIcon className="h-4 w-4" />}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleCustomizeTheme(theme)
                    }}
                  >
                    Customize
                  </Button>
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      duplicateTheme(theme)
                    }}
                  >
                    <DocumentDuplicateIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}

        {/* AI Theme Generator Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          whileHover={{ y: -4 }}
        >
          <Card variant="glass" className="h-full">
            <div className="aspect-[4/3] relative overflow-hidden bg-gradient-to-br from-purple-900/20 to-pink-900/20 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <SparklesIcon className="h-8 w-8 text-purple-400" />
                </div>
                <h3 className="font-semibold text-lg mb-2">AI Theme Generator</h3>
                <p className="text-sm text-zinc-400 px-6">Create a custom theme based on your preferences</p>
              </div>
            </div>
            <div className="p-4">
              <Button
                variant="primary"
                size="sm"
                className="w-full"
                leftIcon={<LightBulbIcon className="h-4 w-4" />}
              >
                Generate with AI
              </Button>
            </div>
          </Card>
        </motion.div>
      </div>

      {/* Theme Customizer Modal */}
      <AnimatePresence>
        {showCustomizer && customizingTheme && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-8"
            onClick={() => setShowCustomizer(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-[var(--bg-card)] border border-[var(--border-subtle)] rounded-2xl p-6 max-w-4xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <ThemeCustomizer
                theme={customizingTheme}
                onClose={() => setShowCustomizer(false)}
                onSave={(theme) => {
                  console.log("Save customized theme:", theme)
                  setShowCustomizer(false)
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Theme Customizer Component
function ThemeCustomizer({ theme, onClose, onSave }: any) {
  const [customTheme, setCustomTheme] = useState(theme)

  const updateColor = (colorKey: string, value: string) => {
    setCustomTheme({
      ...customTheme,
      colors: {
        ...customTheme.colors,
        [colorKey]: value
      }
    })
  }

  const updateFont = (fontKey: string, value: string) => {
    setCustomTheme({
      ...customTheme,
      fonts: {
        ...customTheme.fonts,
        [fontKey]: value
      }
    })
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-semibold">Customize {theme.name}</h2>
          <p className="text-zinc-400">Fine-tune colors, fonts, and styling</p>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-white/10 rounded-lg transition-colors"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      <div className="grid grid-cols-2 gap-6">
        {/* Left - Controls */}
        <div className="space-y-6">
          {/* Colors */}
          <div>
            <h3 className="font-medium mb-4">Colors</h3>
            <div className="space-y-3">
              {Object.entries(customTheme.colors).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <label className="text-sm capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={value as string}
                      onChange={(e) => updateColor(key, e.target.value)}
                      className="w-10 h-10 rounded cursor-pointer"
                    />
                    <input
                      type="text"
                      value={value as string}
                      onChange={(e) => updateColor(key, e.target.value)}
                      className="w-28 px-2 py-1 bg-white/5 border border-[var(--border-subtle)] rounded text-sm"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Typography */}
          <div>
            <h3 className="font-medium mb-4">Typography</h3>
            <div className="space-y-3">
              {Object.entries(customTheme.fonts).map(([key, value]) => (
                <div key={key}>
                  <label className="text-sm capitalize block mb-1">
                    {key} Font
                  </label>
                  <select 
                    value={value as string}
                    onChange={(e) => updateFont(key, e.target.value)}
                    className="w-full px-3 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg"
                  >
                    <option value="Inter">Inter</option>
                    <option value="Poppins">Poppins</option>
                    <option value="Roboto">Roboto</option>
                    <option value="Open Sans">Open Sans</option>
                    <option value="Lato">Lato</option>
                    <option value="Montserrat">Montserrat</option>
                    <option value="Playfair Display">Playfair Display</option>
                    <option value="Merriweather">Merriweather</option>
                    <option value="Space Grotesk">Space Grotesk</option>
                    <option value="Bebas Neue">Bebas Neue</option>
                  </select>
                </div>
              ))}
            </div>
          </div>

          {/* Advanced */}
          <div>
            <h3 className="font-medium mb-4">Advanced</h3>
            <div className="space-y-2">
              <Button variant="glass" size="sm" className="w-full justify-start">
                <CodeBracketIcon className="h-4 w-4 mr-2" />
                Custom CSS
              </Button>
              <Button variant="glass" size="sm" className="w-full justify-start">
                <CommandLineIcon className="h-4 w-4 mr-2" />
                Custom JavaScript
              </Button>
              <Button variant="glass" size="sm" className="w-full justify-start">
                <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                Animation Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Right - Preview */}
        <div>
          <h3 className="font-medium mb-4">Live Preview</h3>
          <div 
            className="rounded-xl overflow-hidden border"
            style={{ borderColor: customTheme.colors.border }}
          >
            <div 
              className="aspect-[4/3] p-6"
              style={{ backgroundColor: customTheme.colors.background }}
            >
              {/* Preview content using custom theme */}
              <div className="h-full flex flex-col">
                {/* Header */}
                <div 
                  className="p-4 rounded-lg mb-4"
                  style={{ 
                    backgroundColor: customTheme.colors.surface,
                    borderColor: customTheme.colors.border,
                    borderWidth: 1
                  }}
                >
                  <h1 
                    className="text-2xl font-bold mb-2"
                    style={{ 
                      color: customTheme.colors.text,
                      fontFamily: customTheme.fonts.heading
                    }}
                  >
                    Welcome Home
                  </h1>
                  <p 
                    style={{ 
                      color: customTheme.colors.textMuted,
                      fontFamily: customTheme.fonts.body
                    }}
                  >
                    Join us this Sunday for worship
                  </p>
                </div>

                {/* Buttons */}
                <div className="flex gap-3 mb-4">
                  <button 
                    className="px-4 py-2 rounded-lg font-medium"
                    style={{ 
                      backgroundColor: customTheme.colors.primary,
                      color: customTheme.colors.background
                    }}
                  >
                    Primary
                  </button>
                  <button 
                    className="px-4 py-2 rounded-lg font-medium"
                    style={{ 
                      backgroundColor: customTheme.colors.secondary,
                      color: customTheme.colors.background
                    }}
                  >
                    Secondary
                  </button>
                  <button 
                    className="px-4 py-2 rounded-lg font-medium"
                    style={{ 
                      backgroundColor: customTheme.colors.accent,
                      color: customTheme.colors.background
                    }}
                  >
                    Accent
                  </button>
                </div>

                {/* Cards */}
                <div className="grid grid-cols-2 gap-3 flex-1">
                  <div 
                    className="p-4 rounded-lg"
                    style={{ 
                      backgroundColor: customTheme.colors.surface,
                      borderColor: customTheme.colors.border,
                      borderWidth: 1
                    }}
                  >
                    <div 
                      className="w-12 h-12 rounded-lg mb-3"
                      style={{ backgroundColor: customTheme.colors.primary }}
                    />
                    <h3 
                      className="font-semibold mb-1"
                      style={{ 
                        color: customTheme.colors.text,
                        fontFamily: customTheme.fonts.heading
                      }}
                    >
                      Service Times
                    </h3>
                    <p 
                      className="text-sm"
                      style={{ 
                        color: customTheme.colors.textMuted,
                        fontFamily: customTheme.fonts.body
                      }}
                    >
                      Sunday 9AM & 11AM
                    </p>
                  </div>
                  <div 
                    className="p-4 rounded-lg"
                    style={{ 
                      backgroundColor: customTheme.colors.surface,
                      borderColor: customTheme.colors.border,
                      borderWidth: 1
                    }}
                  >
                    <div 
                      className="w-12 h-12 rounded-lg mb-3"
                      style={{ backgroundColor: customTheme.colors.secondary }}
                    />
                    <h3 
                      className="font-semibold mb-1"
                      style={{ 
                        color: customTheme.colors.text,
                        fontFamily: customTheme.fonts.heading
                      }}
                    >
                      Events
                    </h3>
                    <p 
                      className="text-sm"
                      style={{ 
                        color: customTheme.colors.textMuted,
                        fontFamily: customTheme.fonts.body
                      }}
                    >
                      Upcoming activities
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-[var(--border-subtle)]">
        <Button variant="glass" onClick={onClose}>
          Cancel
        </Button>
        <Button variant="primary" onClick={() => onSave(customTheme)}>
          Save Theme
        </Button>
      </div>
    </div>
  )
}