"use client"

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Card from "@/components/ui/card"
import { AnimatePresence, motion } from "framer-motion"
import { 
  CubeTransparentIcon,
  PlusIcon,
  XMarkIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  TrashIcon,
  CheckCircleIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  CalendarDaysIcon,
  UserGroupIcon,
  BanknotesIcon,
  NewspaperIcon,
  HeartIcon,
  MapPinIcon,
  ClockIcon,
  WifiIcon,
  HandRaisedIcon,
  TicketIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  BuildingStorefrontIcon,
  MegaphoneIcon,
  ShieldCheckIcon,
  SparklesIcon,
  LightBulbIcon,
  RocketLaunchIcon,
  CommandLineIcon,
  ArrowsPointingOutIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  Squares2X2Icon
} from "@heroicons/react/24/outline"

interface ComponentBlock {
  id: string
  type: string
  label: string
  icon: React.ComponentType<any>
  category: string
  dataSource?: string
  config: any
  aiSuggested?: boolean
}

interface DataModule {
  id: string
  label: string
  icon: React.ComponentType<any>
  enabled: boolean
  description: string
  required?: boolean
}

interface SparkBuilderProps {
  project: any
  onUpdate: (updates: any) => void
  onPublish: () => void
}

export default function SparkBuilder({ project, onUpdate, onPublish }: SparkBuilderProps) {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [pageBlocks, setPageBlocks] = useState<ComponentBlock[]>([])
  const [showComponentPanel, setShowComponentPanel] = useState(true)
  const [showDataPanel, setShowDataPanel] = useState(false)
  const [showAISuggestions, setShowAISuggestions] = useState(false)
  const [devicePreview, setDevicePreview] = useState<"mobile" | "tablet" | "desktop" | "all">("desktop")
  const [isBuilding, setIsBuilding] = useState(false)
  const [selectedBlock, setSelectedBlock] = useState<string | null>(null)

  // Component categories
  const categories = ["All", "Hero", "Content", "Interactive", "Media", "Social", "Navigation", "Forms"]

  // Available component blocks
  const componentBlocks: ComponentBlock[] = [
    // Hero Sections
    { id: "hero-1", type: "hero", label: "Hero Banner", icon: PhotoIcon, category: "Hero", config: { title: "Welcome Home", subtitle: "Join us this Sunday" } },
    { id: "hero-2", type: "hero-video", label: "Video Hero", icon: VideoCameraIcon, category: "Hero", config: { videoUrl: "" } },
    { id: "hero-3", type: "hero-countdown", label: "Countdown Hero", icon: ClockIcon, category: "Hero", config: { targetDate: "" } },
    { id: "hero-4", type: "hero-parallax", label: "Parallax Hero", icon: ArrowsPointingOutIcon, category: "Hero", config: { image: "", parallaxSpeed: 0.5 } },
    
    // Content Blocks
    { id: "content-1", type: "service-times", label: "Service Times", icon: ClockIcon, category: "Content", dataSource: "services", config: {} },
    { id: "content-2", type: "upcoming-events", label: "Events Grid", icon: CalendarDaysIcon, category: "Content", dataSource: "events", config: { limit: 6, layout: "grid" } },
    { id: "content-3", type: "latest-sermon", label: "Latest Sermon", icon: VideoCameraIcon, category: "Content", dataSource: "media", config: {} },
    { id: "content-4", type: "staff-grid", label: "Meet Our Team", icon: UserGroupIcon, category: "Content", dataSource: "people", config: { departments: [] } },
    { id: "content-5", type: "groups-finder", label: "Find a Group", icon: MapPinIcon, category: "Content", dataSource: "groups", config: { showMap: true } },
    { id: "content-6", type: "testimonials", label: "Stories & Testimonials", icon: HeartIcon, category: "Content", config: { autoPlay: true } },
    { id: "content-7", type: "blog-posts", label: "Recent Articles", icon: NewspaperIcon, category: "Content", dataSource: "blog", config: { limit: 3 } },
    
    // Interactive
    { id: "int-1", type: "giving-form", label: "Quick Give", icon: BanknotesIcon, category: "Interactive", dataSource: "giving", config: { funds: [] } },
    { id: "int-2", type: "prayer-request", label: "Prayer Request", icon: HeartIcon, category: "Interactive", config: { categories: ["General", "Health", "Family"] } },
    { id: "int-3", type: "connect-card", label: "Connect Card", icon: HandRaisedIcon, category: "Interactive", config: {} },
    { id: "int-4", type: "live-chat", label: "Live Chat", icon: ChatBubbleLeftRightIcon, category: "Interactive", config: { welcomeMessage: "How can we help?" } },
    { id: "int-5", type: "event-registration", label: "Event Registration", icon: TicketIcon, category: "Interactive", dataSource: "events", config: {} },
    { id: "int-6", type: "volunteer-signup", label: "Volunteer Signup", icon: HandRaisedIcon, category: "Interactive", config: {} },
    
    // Media
    { id: "media-1", type: "live-stream", label: "Live Stream", icon: WifiIcon, category: "Media", dataSource: "stream", config: {} },
    { id: "media-2", type: "sermon-archive", label: "Sermon Archive", icon: VideoCameraIcon, category: "Media", dataSource: "media", config: {} },
    { id: "media-3", type: "photo-gallery", label: "Photo Gallery", icon: PhotoIcon, category: "Media", config: { layout: "masonry" } },
    { id: "media-4", type: "podcast-player", label: "Podcast Player", icon: MusicalNoteIcon, category: "Media", config: {} },
    
    // Social
    { id: "social-1", type: "instagram-feed", label: "Instagram Feed", icon: PhotoIcon, category: "Social", config: { username: "" } },
    { id: "social-2", type: "social-links", label: "Social Links", icon: GlobeAltIcon, category: "Social", config: {} },
    { id: "social-3", type: "social-wall", label: "Social Media Wall", icon: Squares2X2Icon, category: "Social", config: {} },
    
    // Navigation
    { id: "nav-1", type: "navbar", label: "Navigation Bar", icon: BuildingStorefrontIcon, category: "Navigation", config: { sticky: true } },
    { id: "nav-2", type: "footer", label: "Footer", icon: DocumentTextIcon, category: "Navigation", config: {} },
    { id: "nav-3", type: "quick-links", label: "Quick Links", icon: LinkIcon, category: "Navigation", config: {} },
    
    // Forms
    { id: "form-1", type: "contact-form", label: "Contact Form", icon: ChatBubbleLeftRightIcon, category: "Forms", config: {} },
    { id: "form-2", type: "newsletter", label: "Newsletter Signup", icon: MegaphoneIcon, category: "Forms", config: {} },
    { id: "form-3", type: "rsvp-form", label: "RSVP Form", icon: CheckCircleIcon, category: "Forms", config: {} }
  ]

  // Data modules
  const dataModules: DataModule[] = [
    { id: "services", label: "Service Times & Plans", icon: MusicalNoteIcon, enabled: true, description: "Display service schedules and plans", required: true },
    { id: "events", label: "Upcoming Events", icon: CalendarDaysIcon, enabled: true, description: "Show church events and calendars" },
    { id: "people", label: "Staff & Leaders", icon: UserGroupIcon, enabled: true, description: "Display team members and staff" },
    { id: "giving", label: "Online Giving", icon: BanknotesIcon, enabled: true, description: "Accept donations and tithes" },
    { id: "groups", label: "Small Groups", icon: UserGroupIcon, enabled: true, description: "Connect people with groups" },
    { id: "media", label: "Sermons & Media", icon: VideoCameraIcon, enabled: true, description: "Share sermons and media content" },
    { id: "checkin", label: "Check-in System", icon: TicketIcon, enabled: false, description: "Event and service check-ins" },
    { id: "stream", label: "Live Streaming", icon: WifiIcon, enabled: true, description: "Stream services live" },
    { id: "blog", label: "Blog & Articles", icon: NewspaperIcon, enabled: false, description: "Share articles and updates" }
  ]

  // AI suggestions based on app type
  const getAISuggestions = () => {
    const suggestions: ComponentBlock[] = []
    
    if (project.type === "landing") {
      suggestions.push(
        { ...componentBlocks.find(b => b.id === "hero-1")!, aiSuggested: true },
        { ...componentBlocks.find(b => b.id === "content-1")!, aiSuggested: true },
        { ...componentBlocks.find(b => b.id === "content-2")!, aiSuggested: true },
        { ...componentBlocks.find(b => b.id === "int-1")!, aiSuggested: true }
      )
    } else if (project.type === "events") {
      suggestions.push(
        { ...componentBlocks.find(b => b.id === "hero-3")!, aiSuggested: true },
        { ...componentBlocks.find(b => b.id === "content-2")!, aiSuggested: true },
        { ...componentBlocks.find(b => b.id === "int-5")!, aiSuggested: true }
      )
    } else if (project.type === "giving") {
      suggestions.push(
        { ...componentBlocks.find(b => b.id === "hero-1")!, aiSuggested: true },
        { ...componentBlocks.find(b => b.id === "int-1")!, aiSuggested: true },
        { ...componentBlocks.find(b => b.id === "content-6")!, aiSuggested: true }
      )
    }
    
    return suggestions.filter(Boolean)
  }

  const addBlock = (block: ComponentBlock) => {
    const newBlock = {
      ...block,
      id: `${block.type}-${Date.now()}`,
      aiSuggested: false
    }
    setPageBlocks([...pageBlocks, newBlock])
  }

  const removeBlock = (blockId: string) => {
    setPageBlocks(pageBlocks.filter(b => b.id !== blockId))
  }

  const moveBlock = (blockId: string, direction: "up" | "down") => {
    const index = pageBlocks.findIndex(b => b.id === blockId)
    if (index === -1) return
    
    const newBlocks = [...pageBlocks]
    if (direction === "up" && index > 0) {
      [newBlocks[index], newBlocks[index - 1]] = [newBlocks[index - 1], newBlocks[index]]
    } else if (direction === "down" && index < newBlocks.length - 1) {
      [newBlocks[index], newBlocks[index + 1]] = [newBlocks[index + 1], newBlocks[index]]
    }
    setPageBlocks(newBlocks)
  }

  const duplicateBlock = (blockId: string) => {
    const block = pageBlocks.find(b => b.id === blockId)
    if (!block) return
    
    const newBlock = {
      ...block,
      id: `${block.type}-${Date.now()}`
    }
    const index = pageBlocks.findIndex(b => b.id === blockId)
    const newBlocks = [...pageBlocks]
    newBlocks.splice(index + 1, 0, newBlock)
    setPageBlocks(newBlocks)
  }

  const filteredBlocks = selectedCategory === "All" 
    ? componentBlocks 
    : componentBlocks.filter(b => b.category === selectedCategory)

  return (
    <div className="h-full flex">
      {/* Left Panel - Components */}
      <AnimatePresence>
        {showComponentPanel && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 320, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border-r border-[var(--border-subtle)] overflow-hidden"
          >
            <div className="w-[320px] h-full flex flex-col">
              {/* Panel Header */}
              <div className="p-4 border-b border-[var(--border-subtle)]">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium">Components</h3>
                  <Button
                    variant="glass"
                    size="sm"
                    leftIcon={<SparklesIcon className="h-4 w-4" />}
                    onClick={() => setShowAISuggestions(!showAISuggestions)}
                  >
                    AI Suggest
                  </Button>
                </div>

                {/* Categories */}
                <div className="flex flex-wrap gap-1">
                  {categories.map((cat) => (
                    <button
                      key={cat}
                      onClick={() => setSelectedCategory(cat)}
                      className={cn(
                        "px-3 py-1 rounded-lg text-xs transition-colors",
                        selectedCategory === cat
                          ? "bg-[var(--color-primary)] text-black"
                          : "bg-white/5 hover:bg-white/10"
                      )}
                    >
                      {cat}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Components List */}
              <div className="flex-1 p-4 overflow-y-auto custom-scrollbar">
                {showAISuggestions && (
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <LightBulbIcon className="h-4 w-4 text-amber-400" />
                      <p className="text-sm font-medium">AI Recommendations</p>
                    </div>
                    <div className="space-y-2">
                      {getAISuggestions().map((block) => (
                        <ComponentItem
                          key={block.id}
                          block={block}
                          onAdd={() => addBlock(block)}
                          isAISuggested
                        />
                      ))}
                    </div>
                    <div className="my-4 border-t border-[var(--border-subtle)]" />
                  </div>
                )}

                <div className="space-y-2">
                  {filteredBlocks.map((block) => (
                    <ComponentItem
                      key={block.id}
                      block={block}
                      onAdd={() => addBlock(block)}
                    />
                  ))}
                </div>
              </div>

              {/* Data Sources Toggle */}
              <div className="p-4 border-t border-[var(--border-subtle)]">
                <Button
                  variant="glass"
                  size="sm"
                  className="w-full"
                  leftIcon={<AdjustmentsHorizontalIcon className="h-4 w-4" />}
                  onClick={() => setShowDataPanel(!showDataPanel)}
                >
                  Configure Data Sources
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Center - Canvas */}
      <div className="flex-1 flex flex-col">
        {/* Canvas Header */}
        <div className="p-4 border-b border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="glass"
                size="sm"
                leftIcon={<CubeTransparentIcon className="h-4 w-4" />}
                onClick={() => setShowComponentPanel(!showComponentPanel)}
              >
                {showComponentPanel ? "Hide" : "Show"} Components
              </Button>
              
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "mobile", icon: DevicePhoneMobileIcon },
                  { id: "tablet", icon: DeviceTabletIcon },
                  { id: "desktop", icon: ComputerDesktopIcon },
                  { id: "all", icon: Squares2X2Icon }
                ].map((device) => (
                  <button
                    key={device.id}
                    onClick={() => setDevicePreview(device.id as any)}
                    className={cn(
                      "p-2 rounded transition-colors",
                      devicePreview === device.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <device.icon className="h-4 w-4" />
                  </button>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {pageBlocks.length} components
              </Badge>
              <Button
                variant="glass"
                size="sm"
                leftIcon={<EyeIcon className="h-4 w-4" />}
                onClick={() => console.log("Preview")}
              >
                Preview
              </Button>
            </div>
          </div>
        </div>

        {/* Canvas Content */}
        <div className="flex-1 p-6 overflow-hidden">
          <div className={cn(
            "h-full transition-all",
            devicePreview === "mobile" ? "max-w-[375px] mx-auto" :
            devicePreview === "tablet" ? "max-w-[768px] mx-auto" :
            devicePreview === "desktop" ? "max-w-[1200px] mx-auto" :
            ""
          )}>
            {devicePreview === "all" ? (
              <div className="h-full grid grid-cols-3 gap-4">
                {["mobile", "tablet", "desktop"].map((device) => (
                  <div key={device} className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                    <h3 className="text-sm font-medium mb-3 capitalize">{device}</h3>
                    <div className="h-[calc(100%-2rem)] overflow-y-auto custom-scrollbar">
                      <PagePreview blocks={pageBlocks} device={device} />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl overflow-hidden">
                <div className="h-full overflow-y-auto custom-scrollbar p-4">
                  {pageBlocks.length === 0 ? (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center">
                        <CubeTransparentIcon className="h-16 w-16 mx-auto mb-4 text-zinc-400" />
                        <p className="text-lg font-medium mb-2">Start Building Your App</p>
                        <p className="text-sm text-zinc-400 mb-4">Add components from the left panel</p>
                        <Button
                          variant="primary"
                          size="sm"
                          leftIcon={<SparklesIcon className="h-4 w-4" />}
                          onClick={() => setShowAISuggestions(true)}
                        >
                          Get AI Suggestions
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {pageBlocks.map((block, idx) => (
                        <BlockItem
                          key={block.id}
                          block={block}
                          index={idx}
                          total={pageBlocks.length}
                          isSelected={selectedBlock === block.id}
                          onSelect={() => setSelectedBlock(block.id)}
                          onRemove={() => removeBlock(block.id)}
                          onMove={(direction) => moveBlock(block.id, direction)}
                          onDuplicate={() => duplicateBlock(block.id)}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Right Panel - Data Sources */}
      <AnimatePresence>
        {showDataPanel && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 320, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border-l border-[var(--border-subtle)] overflow-hidden"
          >
            <div className="w-[320px] h-full flex flex-col p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Data Sources</h3>
                <button
                  onClick={() => setShowDataPanel(false)}
                  className="p-1 hover:bg-white/10 rounded"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
              
              <div className="space-y-2">
                {dataModules.map((module) => (
                  <div
                    key={module.id}
                    className={cn(
                      "p-3 rounded-lg border transition-all",
                      module.enabled
                        ? "bg-white/5 border-[var(--border-subtle)]"
                        : "bg-white/5 border-[var(--border-subtle)] opacity-50"
                    )}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        <module.icon className="h-5 w-5 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium">{module.label}</p>
                          <p className="text-xs text-zinc-400">{module.description}</p>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={module.enabled}
                        onChange={() => console.log("Toggle module")}
                        disabled={module.required}
                        className="w-5 h-5 rounded"
                      />
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-auto pt-4 border-t border-[var(--border-subtle)]">
                <Button variant="glass" size="sm" className="w-full">
                  <ShieldCheckIcon className="h-4 w-4 mr-2" />
                  Configure Permissions
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Component Item
function ComponentItem({ block, onAdd, isAISuggested = false }: any) {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={cn(
        "p-3 rounded-lg cursor-pointer transition-all",
        isAISuggested 
          ? "bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 hover:border-amber-500/40"
          : "bg-white/5 hover:bg-white/10"
      )}
      onClick={onAdd}
    >
      <div className="flex items-center gap-3">
        <div className={cn(
          "w-10 h-10 rounded-lg flex items-center justify-center",
          isAISuggested ? "bg-amber-500/20" : "bg-white/10"
        )}>
          <block.icon className="h-5 w-5" />
        </div>
        <div className="flex-1">
          <p className="text-sm font-medium">{block.label}</p>
          <p className="text-xs text-zinc-400">{block.category}</p>
        </div>
        {isAISuggested && (
          <Badge variant="warning" size="sm">AI</Badge>
        )}
        <PlusIcon className="h-4 w-4 text-zinc-400" />
      </div>
    </motion.div>
  )
}

// Block Item in Canvas
function BlockItem({ block, index, total, isSelected, onSelect, onRemove, onMove, onDuplicate }: any) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "relative group",
        isSelected && "ring-2 ring-[var(--color-primary)] rounded-lg"
      )}
      onClick={onSelect}
    >
      <div className="p-6 bg-white/5 rounded-lg border border-[var(--border-subtle)] group-hover:border-[var(--color-primary)]/30 transition-colors">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center">
              <block.icon className="h-5 w-5" />
            </div>
            <div>
              <h4 className="font-medium">{block.label}</h4>
              <p className="text-xs text-zinc-400">{block.category}</p>
            </div>
          </div>
          
          {/* Block Controls */}
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onMove("up")
              }}
              disabled={index === 0}
              className="p-1 hover:bg-white/10 rounded disabled:opacity-50"
            >
              <ArrowUpIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onMove("down")
              }}
              disabled={index === total - 1}
              className="p-1 hover:bg-white/10 rounded disabled:opacity-50"
            >
              <ArrowDownIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onDuplicate()
              }}
              className="p-1 hover:bg-white/10 rounded"
            >
              <DocumentDuplicateIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                console.log("Edit block")
              }}
              className="p-1 hover:bg-white/10 rounded"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onRemove()
              }}
              className="p-1 hover:bg-white/10 rounded text-red-400"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Block Preview */}
        <div className="h-32 bg-white/5 rounded-lg flex items-center justify-center">
          <p className="text-sm text-zinc-400">
            {block.dataSource && `Connected to ${block.dataSource}`}
          </p>
        </div>
      </div>
    </motion.div>
  )
}

// Page Preview
function PagePreview({ blocks, device }: { blocks: ComponentBlock[], device: string }) {
  if (blocks.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-sm text-zinc-400">No components added</p>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {blocks.map((block) => (
        <div key={block.id} className="p-4 bg-white/5 rounded border border-[var(--border-subtle)]">
          <div className="flex items-center gap-2 mb-2">
            <block.icon className="h-4 w-4 text-zinc-400" />
            <p className="text-xs font-medium">{block.label}</p>
          </div>
          <div className="h-16 bg-white/5 rounded" />
        </div>
      ))}
    </div>
  )
}

// Icons
function DeviceTabletIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
    </svg>
  )
}

function DocumentDuplicateIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
    </svg>
  )
}

function LinkIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
    </svg>
  )
}