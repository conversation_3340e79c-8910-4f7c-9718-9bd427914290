"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Card from "@/components/ui/card"
import { motion } from "framer-motion"
import { 
  ShareIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  LinkIcon,
  CalendarDaysIcon,
  ClockIcon,
  PhotoIcon,
  VideoCameraIcon,
  HashtagIcon,
  UsersIcon,
  ChartBarIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  BellIcon,
  ExclamationTriangleIcon
} from "@heroicons/react/24/outline"

interface SocialMediaIntegrationProps {
  project: any
}

type Platform = "facebook" | "instagram" | "twitter" | "youtube" | "tiktok"
type PostStatus = "draft" | "scheduled" | "published" | "failed"

interface SocialAccount {
  id: string
  platform: Platform
  name: string
  handle: string
  connected: boolean
  followers: number
  avatar: string
}

interface SocialPost {
  id: string
  content: string
  platforms: Platform[]
  status: PostStatus
  scheduledFor?: Date
  publishedAt?: Date
  media?: { type: "image" | "video"; url: string }[]
  engagement?: {
    likes: number
    comments: number
    shares: number
    views: number
  }
}

export default function SocialMediaIntegration({ project }: SocialMediaIntegrationProps) {
  const [connectedAccounts, setConnectedAccounts] = useState<SocialAccount[]>([
    {
      id: "1",
      platform: "facebook",
      name: "Grace Church",
      handle: "@gracechurch",
      connected: true,
      followers: 12543,
      avatar: "/church-logo.png"
    },
    {
      id: "2",
      platform: "instagram",
      name: "gracechurch",
      handle: "@gracechurch",
      connected: true,
      followers: 8923,
      avatar: "/church-logo.png"
    },
    {
      id: "3",
      platform: "youtube",
      name: "Grace Church",
      handle: "@GraceChurchOfficial",
      connected: false,
      followers: 5234,
      avatar: "/church-logo.png"
    }
  ])

  const [posts, setPosts] = useState<SocialPost[]>([
    {
      id: "1",
      content: "Join us this Sunday for a powerful message on hope and renewal! Service times: 9AM & 11AM. Everyone welcome! 🙏 #SundayService #Church #Hope",
      platforms: ["facebook", "instagram"],
      status: "scheduled",
      scheduledFor: new Date(Date.now() + ********), // Tomorrow
      media: [{ type: "image", url: "/sunday-service.jpg" }]
    },
    {
      id: "2",
      content: "Thank you to everyone who joined us for the community outreach! Together we served over 500 families. Your generosity makes a difference! ❤️",
      platforms: ["facebook", "instagram", "twitter"],
      status: "published",
      publishedAt: new Date(Date.now() - ********), // Yesterday
      engagement: {
        likes: 423,
        comments: 67,
        shares: 45,
        views: 3421
      }
    }
  ])

  const [showComposer, setShowComposer] = useState(false)
  const [selectedPost, setSelectedPost] = useState<SocialPost | null>(null)

  const platformConfig = {
    facebook: {
      name: "Facebook",
      color: "bg-blue-600",
      icon: FacebookIcon,
      maxLength: 63206
    },
    instagram: {
      name: "Instagram",
      color: "bg-gradient-to-br from-purple-600 to-pink-500",
      icon: InstagramIcon,
      maxLength: 2200
    },
    twitter: {
      name: "Twitter/X",
      color: "bg-black",
      icon: TwitterIcon,
      maxLength: 280
    },
    youtube: {
      name: "YouTube",
      color: "bg-red-600",
      icon: YouTubeIcon,
      maxLength: 5000
    },
    tiktok: {
      name: "TikTok",
      color: "bg-black",
      icon: TikTokIcon,
      maxLength: 2200
    }
  }

  const connectPlatform = (platform: Platform) => {
    console.log(`Connecting to ${platform}...`)
    // OAuth flow would go here
  }

  const schedulePost = (post: SocialPost) => {
    console.log("Scheduling post:", post)
  }

  const getStatusColor = (status: PostStatus) => {
    switch (status) {
      case "draft": return "text-zinc-400"
      case "scheduled": return "text-blue-400"
      case "published": return "text-green-400"
      case "failed": return "text-red-400"
      default: return "text-zinc-400"
    }
  }

  return (
    <Card variant="glass">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold">Social Media Integration</h3>
            <p className="text-sm text-zinc-400">Share your content across platforms</p>
          </div>
          <Button
            variant="primary"
            size="sm"
            leftIcon={<PlusIcon className="h-4 w-4" />}
            onClick={() => setShowComposer(true)}
          >
            Create Post
          </Button>
        </div>

        <div className="space-y-6">
          {/* Connected Accounts */}
          <div>
            <h4 className="text-sm font-medium mb-3">Connected Accounts</h4>
            <div className="grid grid-cols-3 gap-3">
              {Object.entries(platformConfig).map(([platform, config]) => {
                const account = connectedAccounts.find(a => a.platform === platform)
                const isConnected = account?.connected || false

                return (
                  <div
                    key={platform}
                    className={cn(
                      "p-4 rounded-lg border transition-all",
                      isConnected
                        ? "bg-white/5 border-white/20"
                        : "bg-white/5 border-white/10 opacity-60"
                    )}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center", config.color)}>
                        <config.icon className="h-5 w-5 text-white" />
                      </div>
                      {isConnected ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-400" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-zinc-400" />
                      )}
                    </div>
                    
                    <h5 className="font-medium text-sm mb-1">{config.name}</h5>
                    
                    {account ? (
                      <div>
                        <p className="text-xs text-zinc-400">{account.handle}</p>
                        <p className="text-xs text-zinc-400">{account.followers.toLocaleString()} followers</p>
                      </div>
                    ) : (
                      <Button
                        variant="glass"
                        size="sm"
                        className="mt-2"
                        onClick={() => connectPlatform(platform as Platform)}
                      >
                        Connect
                      </Button>
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* Recent Posts */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium">Recent Posts</h4>
              <Button variant="glass" size="sm">
                View All
              </Button>
            </div>
            
            <div className="space-y-3">
              {posts.map((post) => (
                <div
                  key={post.id}
                  className="p-4 bg-white/5 rounded-lg border border-white/10 hover:border-white/20 transition-all cursor-pointer"
                  onClick={() => setSelectedPost(post)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {post.platforms.map((platform) => {
                        const config = platformConfig[platform]
                        return (
                          <div
                            key={platform}
                            className={cn("w-6 h-6 rounded flex items-center justify-center", config.color)}
                          >
                            <config.icon className="h-3 w-3 text-white" />
                          </div>
                        )
                      })}
                    </div>
                    <Badge variant="secondary" size="sm" className={getStatusColor(post.status)}>
                      {post.status}
                    </Badge>
                  </div>

                  <p className="text-sm mb-3 line-clamp-2">{post.content}</p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-xs text-zinc-400">
                      {post.scheduledFor && (
                        <div className="flex items-center gap-1">
                          <CalendarDaysIcon className="h-3 w-3" />
                          <span>{new Date(post.scheduledFor).toLocaleDateString()}</span>
                        </div>
                      )}
                      {post.publishedAt && (
                        <div className="flex items-center gap-1">
                          <ClockIcon className="h-3 w-3" />
                          <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
                        </div>
                      )}
                      {post.media && (
                        <div className="flex items-center gap-1">
                          {post.media[0].type === "image" ? <PhotoIcon className="h-3 w-3" /> : <VideoCameraIcon className="h-3 w-3" />}
                          <span>{post.media.length}</span>
                        </div>
                      )}
                    </div>

                    {post.engagement && (
                      <div className="flex items-center gap-3 text-xs">
                        <span>{post.engagement.likes} likes</span>
                        <span>{post.engagement.comments} comments</span>
                        <span>{post.engagement.shares} shares</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Analytics Summary */}
          <div>
            <h4 className="text-sm font-medium mb-3">Social Analytics (Last 30 Days)</h4>
            <div className="grid grid-cols-4 gap-3">
              <div className="p-3 bg-white/5 rounded-lg text-center">
                <p className="text-2xl font-bold">45.2K</p>
                <p className="text-xs text-zinc-400">Total Reach</p>
              </div>
              <div className="p-3 bg-white/5 rounded-lg text-center">
                <p className="text-2xl font-bold">8.9K</p>
                <p className="text-xs text-zinc-400">Engagements</p>
              </div>
              <div className="p-3 bg-white/5 rounded-lg text-center">
                <p className="text-2xl font-bold">523</p>
                <p className="text-xs text-zinc-400">New Followers</p>
              </div>
              <div className="p-3 bg-white/5 rounded-lg text-center">
                <p className="text-2xl font-bold">19.5%</p>
                <p className="text-xs text-zinc-400">Engagement Rate</p>
              </div>
            </div>
          </div>

          {/* Auto-Share Settings */}
          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="flex items-start gap-3">
              <BellIcon className="h-5 w-5 text-blue-400 mt-0.5" />
              <div className="flex-1">
                <h5 className="text-sm font-medium mb-1">Auto-Share Settings</h5>
                <p className="text-xs text-zinc-400 mb-3">
                  Automatically share new events, sermons, and announcements to your connected social accounts.
                </p>
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className="text-sm">Auto-share new events</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className="text-sm">Auto-share sermon recordings</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">Auto-share blog posts</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}

// Platform Icons
function FacebookIcon(props: any) {
  return (
    <svg {...props} fill="currentColor" viewBox="0 0 24 24">
      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
    </svg>
  )
}

function InstagramIcon(props: any) {
  return (
    <svg {...props} fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zM5.838 12a6.162 6.162 0 1112.324 0 6.162 6.162 0 01-12.324 0zM12 16a4 4 0 110-8 4 4 0 010 8zm4.965-10.405a1.44 1.44 0 112.881.001 1.44 1.44 0 01-2.881-.001z"/>
    </svg>
  )
}

function TwitterIcon(props: any) {
  return (
    <svg {...props} fill="currentColor" viewBox="0 0 24 24">
      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
    </svg>
  )
}

function YouTubeIcon(props: any) {
  return (
    <svg {...props} fill="currentColor" viewBox="0 0 24 24">
      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
    </svg>
  )
}

function TikTokIcon(props: any) {
  return (
    <svg {...props} fill="currentColor" viewBox="0 0 24 24">
      <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
    </svg>
  )
}