"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Card from "@/components/ui/card"
import { motion } from "framer-motion"
import { 
  GlobeAltIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  LinkIcon,
  ShieldCheckIcon,
  LockClosedIcon,
  ClockIcon,
  ServerIcon,
  DocumentDuplicateIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon,
  InformationCircleIcon,
  ArrowTopRightOnSquareIcon,
  WifiIcon,
  CloudIcon
} from "@heroicons/react/24/outline"

interface DomainManagerProps {
  project: any
}

type DomainStatus = "active" | "pending" | "error" | "expired"
type SSLStatus = "active" | "pending" | "none"

interface Domain {
  id: string
  domain: string
  status: DomainStatus
  ssl: SSLStatus
  provider: string
  expiresAt?: Date
  createdAt: Date
  isPrimary: boolean
  redirectTo?: string
}

interface DNSRecord {
  type: "A" | "CNAME" | "TXT" | "MX"
  name: string
  value: string
  ttl: number
  status: "active" | "pending"
}

export default function DomainManager({ project }: DomainManagerProps) {
  const [domains, setDomains] = useState<Domain[]>([
    {
      id: "1",
      domain: "gracechurch.com",
      status: "active",
      ssl: "active",
      provider: "Custom",
      expiresAt: new Date("2025-12-31"),
      createdAt: new Date("2023-01-15"),
      isPrimary: true
    },
    {
      id: "2",
      domain: "gracechurch.live",
      status: "active",
      ssl: "active",
      provider: "Church App",
      createdAt: new Date("2024-06-20"),
      isPrimary: false
    },
    {
      id: "3",
      domain: "give.gracechurch.com",
      status: "pending",
      ssl: "pending",
      provider: "Custom",
      createdAt: new Date("2025-01-20"),
      isPrimary: false,
      redirectTo: "gracechurch.com/give"
    }
  ])

  const [showAddDomain, setShowAddDomain] = useState(false)
  const [newDomain, setNewDomain] = useState("")
  const [selectedDomain, setSelectedDomain] = useState<Domain | null>(null)
  const [isVerifying, setIsVerifying] = useState(false)

  const dnsRecords: DNSRecord[] = [
    { type: "A", name: "@", value: "76.76.21.21", ttl: 3600, status: "active" },
    { type: "CNAME", name: "www", value: "cname.church.app", ttl: 3600, status: "active" },
    { type: "TXT", name: "@", value: "church-app-verify=abc123", ttl: 3600, status: "active" },
    { type: "MX", name: "@", value: "10 mail.gracechurch.com", ttl: 3600, status: "active" }
  ]

  const verifyDomain = async (domain: Domain) => {
    setIsVerifying(true)
    // Simulate verification
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsVerifying(false)
    
    // Update domain status
    setDomains(domains.map(d => 
      d.id === domain.id ? { ...d, status: "active", ssl: "active" } : d
    ))
  }

  const addDomain = () => {
    if (!newDomain) return
    
    const domain: Domain = {
      id: Date.now().toString(),
      domain: newDomain,
      status: "pending",
      ssl: "none",
      provider: "Custom",
      createdAt: new Date(),
      isPrimary: false
    }
    
    setDomains([...domains, domain])
    setNewDomain("")
    setShowAddDomain(false)
  }

  const deleteDomain = (domainId: string) => {
    setDomains(domains.filter(d => d.id !== domainId))
  }

  const makePrimary = (domainId: string) => {
    setDomains(domains.map(d => ({
      ...d,
      isPrimary: d.id === domainId
    })))
  }

  const getStatusColor = (status: DomainStatus) => {
    switch (status) {
      case "active": return "text-green-400"
      case "pending": return "text-amber-400"
      case "error": return "text-red-400"
      case "expired": return "text-red-400"
      default: return "text-zinc-400"
    }
  }

  const getSSLColor = (status: SSLStatus) => {
    switch (status) {
      case "active": return "text-green-400"
      case "pending": return "text-amber-400"
      case "none": return "text-zinc-400"
      default: return "text-zinc-400"
    }
  }

  return (
    <Card variant="glass">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold">Custom Domains</h3>
            <p className="text-sm text-zinc-400">Manage your church's web addresses</p>
          </div>
          <Button
            variant="primary"
            size="sm"
            leftIcon={<PlusIcon className="h-4 w-4" />}
            onClick={() => setShowAddDomain(true)}
          >
            Add Domain
          </Button>
        </div>

        <div className="space-y-4">
          {/* Domain List */}
          {domains.map((domain) => (
            <motion.div
              key={domain.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                "p-4 bg-white/5 rounded-lg border transition-all cursor-pointer",
                selectedDomain?.id === domain.id
                  ? "border-[var(--color-primary)]"
                  : "border-white/10 hover:border-white/20"
              )}
              onClick={() => setSelectedDomain(domain)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <GlobeAltIcon className="h-5 w-5 mt-0.5 text-zinc-400" />
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{domain.domain}</h4>
                      {domain.isPrimary && (
                        <Badge variant="primary" size="sm">Primary</Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        {domain.status === "active" ? (
                          <CheckCircleIcon className={cn("h-4 w-4", getStatusColor(domain.status))} />
                        ) : (
                          <ExclamationTriangleIcon className={cn("h-4 w-4", getStatusColor(domain.status))} />
                        )}
                        <span className={getStatusColor(domain.status)}>
                          {domain.status}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <LockClosedIcon className={cn("h-4 w-4", getSSLColor(domain.ssl))} />
                        <span className={cn("text-sm", getSSLColor(domain.ssl))}>
                          SSL {domain.ssl}
                        </span>
                      </div>
                      
                      <span className="text-zinc-400">
                        {domain.provider}
                      </span>
                    </div>

                    {domain.redirectTo && (
                      <div className="flex items-center gap-1 mt-2 text-xs text-zinc-400">
                        <ArrowTopRightOnSquareIcon className="h-3 w-3" />
                        Redirects to {domain.redirectTo}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {domain.status === "pending" && (
                    <Button
                      variant="glass"
                      size="sm"
                      leftIcon={isVerifying ? <ArrowPathIcon className="h-4 w-4 animate-spin" /> : <CheckCircleIcon className="h-4 w-4" />}
                      onClick={(e) => {
                        e.stopPropagation()
                        verifyDomain(domain)
                      }}
                      disabled={isVerifying}
                    >
                      {isVerifying ? "Verifying..." : "Verify"}
                    </Button>
                  )}
                  
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      window.open(`https://${domain.domain}`, "_blank")
                    }}
                  >
                    <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}

          {/* Add Domain Form */}
          {showAddDomain && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg"
            >
              <h4 className="font-medium mb-3">Add Custom Domain</h4>
              <div className="space-y-3">
                <div>
                  <label className="text-sm text-zinc-400 block mb-1">Domain Name</label>
                  <input
                    type="text"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    placeholder="yourdomain.com"
                    className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={addDomain}
                    disabled={!newDomain}
                  >
                    Add Domain
                  </Button>
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={() => {
                      setShowAddDomain(false)
                      setNewDomain("")
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          )}

          {/* DNS Configuration */}
          {selectedDomain && selectedDomain.status === "pending" && (
            <Card variant="glass" className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <ServerIcon className="h-5 w-5 text-amber-400" />
                <h4 className="font-medium">DNS Configuration Required</h4>
              </div>
              <p className="text-sm text-zinc-400 mb-4">
                Add these DNS records to your domain provider to complete setup:
              </p>
              
              <div className="space-y-2">
                {dnsRecords.map((record, idx) => (
                  <div key={idx} className="p-3 bg-white/5 rounded-lg font-mono text-xs">
                    <div className="flex items-center justify-between">
                      <div className="grid grid-cols-4 gap-4 flex-1">
                        <span className="text-blue-400">{record.type}</span>
                        <span>{record.name}</span>
                        <span className="text-green-400">{record.value}</span>
                        <span className="text-zinc-400">TTL: {record.ttl}</span>
                      </div>
                      <Button
                        variant="glass"
                        size="sm"
                        onClick={() => navigator.clipboard.writeText(record.value)}
                      >
                        <DocumentDuplicateIcon className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-amber-500/10 border border-amber-500/20 rounded-lg">
                <div className="flex items-start gap-2">
                  <InformationCircleIcon className="h-5 w-5 text-amber-400 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium mb-1">DNS Propagation</p>
                    <p className="text-xs text-zinc-400">
                      DNS changes can take up to 48 hours to propagate worldwide. SSL certificates will be automatically provisioned once DNS verification is complete.
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Domain Settings */}
          {selectedDomain && selectedDomain.status === "active" && (
            <Card variant="glass" className="p-4">
              <h4 className="font-medium mb-4">Domain Settings</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Primary Domain</p>
                    <p className="text-xs text-zinc-400">All traffic will be redirected to this domain</p>
                  </div>
                  <Button
                    variant={selectedDomain.isPrimary ? "secondary" : "primary"}
                    size="sm"
                    onClick={() => makePrimary(selectedDomain.id)}
                    disabled={selectedDomain.isPrimary}
                  >
                    {selectedDomain.isPrimary ? "Is Primary" : "Make Primary"}
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">SSL Certificate</p>
                    <p className="text-xs text-zinc-400">Automatically managed by Church App</p>
                  </div>
                  <Badge variant="success">
                    <LockClosedIcon className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">CDN & Performance</p>
                    <p className="text-xs text-zinc-400">Global content delivery network</p>
                  </div>
                  <Badge variant="secondary">
                    <CloudIcon className="h-3 w-3 mr-1" />
                    Enabled
                  </Badge>
                </div>

                {selectedDomain.expiresAt && (
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Domain Expiration</p>
                      <p className="text-xs text-zinc-400">Renew with your domain provider</p>
                    </div>
                    <span className="text-sm">
                      {selectedDomain.expiresAt.toLocaleDateString()}
                    </span>
                  </div>
                )}

                <div className="pt-3 border-t border-white/10">
                  <Button
                    variant="destructive"
                    size="sm"
                    leftIcon={<TrashIcon className="h-4 w-4" />}
                    onClick={() => {
                      deleteDomain(selectedDomain.id)
                      setSelectedDomain(null)
                    }}
                    disabled={selectedDomain.isPrimary}
                  >
                    Remove Domain
                  </Button>
                </div>
              </div>
            </Card>
          )}

          {/* Help Section */}
          <div className="p-4 bg-white/5 rounded-lg">
            <h4 className="font-medium mb-3">Need Help?</h4>
            <div className="space-y-2 text-sm text-zinc-400">
              <p>• Domain setup usually takes 5-10 minutes</p>
              <p>• SSL certificates are automatically provisioned</p>
              <p>• All domains include CDN and DDoS protection</p>
              <p>• Contact support for assistance with domain transfers</p>
            </div>
            <Button variant="glass" size="sm" className="mt-3">
              View Documentation
            </Button>
          </div>
        </div>
      </div>
    </Card>
  )
}

// Icons
function ArrowTopRightOnSquareIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
    </svg>
  )
}

function CloudIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.25 15a4.5 4.5 0 004.5 4.5H18a3.75 3.75 0 001.332-7.257 3 3 0 00-3.758-3.848 5.25 5.25 0 00-10.233 2.33A4.502 4.502 0 002.25 15z" />
    </svg>
  )
}