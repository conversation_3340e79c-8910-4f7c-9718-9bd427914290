"use client"

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Card from "@/components/ui/card"
import { AnimatePresence, motion } from "framer-motion"
import { 
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  Squares2X2Icon,
  ArrowPathIcon,
  QrCodeIcon,
  LinkIcon,
  ShareIcon,
  ExternalLinkIcon,
  CodeBracketIcon,
  EyeIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  SunIcon,
  MoonIcon,
  GlobeAltIcon,
  ClockIcon,
  UserGroupIcon,
  CalendarDaysIcon,
  BanknotesIcon,
  VideoCameraIcon,
  HeartIcon,
  PhotoIcon,
  ChatBubbleLeftRightIcon,
  HandRaisedIcon,
  WifiIcon,
  MusicalNoteIcon,
  MapPinIcon,
  BuildingStorefrontIcon,
  NewspaperIcon,
  DocumentTextIcon,
  MegaphoneIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline"

interface PublishPreviewProps {
  project: any
}

type DeviceType = "mobile" | "tablet" | "desktop" | "all"
type ViewMode = "preview" | "interact" | "debug"

export default function PublishPreview({ project }: PublishPreviewProps) {
  const [device, setDevice] = useState<DeviceType>("desktop")
  const [viewMode, setViewMode] = useState<ViewMode>("preview")
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(true)
  const [isLoading, setIsLoading] = useState(true)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [refreshKey])

  const handleRefresh = () => {
    setIsLoading(true)
    setRefreshKey(prev => prev + 1)
  }

  const getDeviceStyles = (deviceType: DeviceType = device) => {
    switch (deviceType) {
      case "mobile":
        return {
          width: 375,
          height: 812,
          scale: 0.8,
          label: "iPhone 14"
        }
      case "tablet":
        return {
          width: 768,
          height: 1024,
          scale: 0.6,
          label: "iPad"
        }
      case "desktop":
        return {
          width: 1440,
          height: 900,
          scale: 0.5,
          label: "Desktop"
        }
      default:
        return null
    }
  }

  const renderPreviewContent = () => {
    if (isLoading) {
      return (
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <ArrowPathIcon className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-sm text-zinc-400">Loading preview...</p>
          </div>
        </div>
      )
    }

    // Sample components based on project modules
    const components = [
      { type: "navbar", icon: BuildingStorefrontIcon, label: "Navigation" },
      { type: "hero", icon: PhotoIcon, label: "Hero Section" },
      { type: "service-times", icon: ClockIcon, label: "Service Times" },
      { type: "events", icon: CalendarDaysIcon, label: "Upcoming Events" },
      { type: "giving", icon: BanknotesIcon, label: "Online Giving" },
      { type: "footer", icon: DocumentTextIcon, label: "Footer" }
    ]

    return (
      <div className={cn(
        "min-h-full transition-colors",
        isDarkMode ? "bg-black text-white" : "bg-white text-black"
      )}>
        {/* Navigation */}
        <nav className={cn(
          "sticky top-0 z-10 backdrop-blur-xl border-b",
          isDarkMode ? "bg-white/5 border-white/10" : "bg-black/5 border-black/10"
        )}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-10 h-10 rounded-lg flex items-center justify-center",
                  "bg-gradient-to-br from-blue-500 to-purple-600"
                )}>
                  <GlobeAltIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="font-semibold">{project.name}</h1>
                  <p className="text-xs opacity-60">Live Preview</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <a href="#" className="text-sm hover:opacity-80">About</a>
                <a href="#" className="text-sm hover:opacity-80">Events</a>
                <a href="#" className="text-sm hover:opacity-80">Connect</a>
                <button className={cn(
                  "px-4 py-2 rounded-lg text-sm font-medium",
                  "bg-gradient-to-r from-blue-500 to-purple-600 text-white"
                )}>
                  Give Online
                </button>
              </div>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="relative h-[500px] flex items-center justify-center">
          <div className={cn(
            "absolute inset-0",
            "bg-gradient-to-br from-blue-600/20 to-purple-600/20"
          )} />
          <div className="relative z-10 text-center px-4">
            <h2 className="text-5xl font-bold mb-4">Welcome Home</h2>
            <p className="text-xl mb-8 opacity-80">Join us this Sunday at 9AM & 11AM</p>
            <div className="flex items-center justify-center gap-4">
              <button className={cn(
                "px-6 py-3 rounded-lg font-medium",
                "bg-white text-black hover:bg-gray-100"
              )}>
                Plan Your Visit
              </button>
              <button className={cn(
                "px-6 py-3 rounded-lg font-medium border",
                isDarkMode ? "border-white/20 hover:bg-white/10" : "border-black/20 hover:bg-black/10"
              )}>
                Watch Live
              </button>
            </div>
          </div>
        </section>

        {/* Service Times */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <h3 className="text-3xl font-bold text-center mb-12">Service Times</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {["Sunday Morning", "Wednesday Night", "Youth Service"].map((service) => (
                <div key={service} className={cn(
                  "p-6 rounded-xl text-center",
                  isDarkMode ? "bg-white/5" : "bg-black/5"
                )}>
                  <ClockIcon className="h-12 w-12 mx-auto mb-4 text-blue-500" />
                  <h4 className="font-semibold text-lg mb-2">{service}</h4>
                  <p className="opacity-60">Multiple times available</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Events */}
        <section className={cn(
          "py-16 px-4",
          isDarkMode ? "bg-white/5" : "bg-gray-50"
        )}>
          <div className="max-w-6xl mx-auto">
            <h3 className="text-3xl font-bold text-center mb-12">Upcoming Events</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className={cn(
                  "rounded-xl overflow-hidden",
                  isDarkMode ? "bg-black" : "bg-white"
                )}>
                  <div className="h-48 bg-gradient-to-br from-purple-500 to-pink-500" />
                  <div className="p-6">
                    <h4 className="font-semibold text-lg mb-2">Event Title {i}</h4>
                    <p className="text-sm opacity-60 mb-4">Join us for this special event</p>
                    <button className="text-sm text-blue-500 font-medium">Learn More →</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className={cn(
          "py-12 px-4 border-t",
          isDarkMode ? "bg-white/5 border-white/10" : "bg-black/5 border-black/10"
        )}>
          <div className="max-w-6xl mx-auto text-center">
            <p className="opacity-60">© 2025 {project.name}. All rights reserved.</p>
          </div>
        </footer>
      </div>
    )
  }

  const renderDevice = (deviceType: DeviceType) => {
    const deviceStyle = getDeviceStyles(deviceType)
    if (!deviceStyle) return null

    return (
      <div className="flex flex-col items-center">
        <h3 className="text-sm font-medium mb-3">{deviceStyle.label}</h3>
        <div
          className={cn(
            "relative bg-gray-900 rounded-[2.5rem] p-2 shadow-2xl",
            deviceType === "mobile" && "rounded-[3rem]"
          )}
          style={{
            transform: `scale(${deviceStyle.scale})`,
            transformOrigin: "top center"
          }}
        >
          {/* Device Frame */}
          <div className="absolute inset-x-0 top-0 h-6 bg-gray-900 rounded-t-[2.5rem]" />
          {deviceType === "mobile" && (
            <div className="absolute top-6 left-1/2 -translate-x-1/2 w-20 h-5 bg-gray-900 rounded-full" />
          )}
          
          {/* Screen */}
          <div
            className="bg-white rounded-[2rem] overflow-hidden"
            style={{
              width: `${deviceStyle.width}px`,
              height: `${deviceStyle.height}px`
            }}
          >
            <iframe
              src="#"
              className="w-full h-full border-0"
              title={`${deviceType} preview`}
            />
            <div className="absolute inset-0 pointer-events-none">
              {renderPreviewContent()}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Preview Header */}
      <div className="p-4 border-b border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Device Selector */}
            <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
              {[
                { id: "mobile", icon: DevicePhoneMobileIcon },
                { id: "tablet", icon: DeviceTabletIcon },
                { id: "desktop", icon: ComputerDesktopIcon },
                { id: "all", icon: Squares2X2Icon }
              ].map((d) => (
                <button
                  key={d.id}
                  onClick={() => setDevice(d.id as DeviceType)}
                  className={cn(
                    "p-2 rounded transition-colors",
                    device === d.id
                      ? "bg-[var(--color-primary)] text-black"
                      : "hover:bg-white/10"
                  )}
                >
                  <d.icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* View Mode */}
            <div className="flex items-center gap-1">
              {[
                { id: "preview", label: "Preview" },
                { id: "interact", label: "Interact" },
                { id: "debug", label: "Debug" }
              ].map((mode) => (
                <button
                  key={mode.id}
                  onClick={() => setViewMode(mode.id as ViewMode)}
                  className={cn(
                    "px-3 py-1 rounded text-sm transition-colors",
                    viewMode === mode.id
                      ? "bg-white/10 text-white"
                      : "text-zinc-400 hover:text-white"
                  )}
                >
                  {mode.label}
                </button>
              ))}
            </div>

            <Button
              variant="glass"
              size="sm"
              leftIcon={isDarkMode ? <SunIcon className="h-4 w-4" /> : <MoonIcon className="h-4 w-4" />}
              onClick={() => setIsDarkMode(!isDarkMode)}
            >
              {isDarkMode ? "Light" : "Dark"}
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="glass"
              size="sm"
              leftIcon={<ArrowPathIcon className="h-4 w-4" />}
              onClick={handleRefresh}
            >
              Refresh
            </Button>
            <Button
              variant="glass"
              size="sm"
              leftIcon={<QrCodeIcon className="h-4 w-4" />}
              onClick={() => console.log("Show QR code")}
            >
              QR Code
            </Button>
            <Button
              variant="glass"
              size="sm"
              leftIcon={<ShareIcon className="h-4 w-4" />}
              onClick={() => console.log("Share preview")}
            >
              Share
            </Button>
            <Button
              variant="glass"
              size="sm"
              leftIcon={isFullscreen ? <ArrowsPointingInIcon className="h-4 w-4" /> : <ArrowsPointingOutIcon className="h-4 w-4" />}
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              {isFullscreen ? "Exit" : "Fullscreen"}
            </Button>
            <Button
              variant="primary"
              size="sm"
              leftIcon={<ExternalLinkIcon className="h-4 w-4" />}
              onClick={() => window.open("#", "_blank")}
            >
              Open Live
            </Button>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-hidden bg-zinc-900">
        {device === "all" ? (
          <div className="h-full p-8 overflow-auto">
            <div className="grid grid-cols-3 gap-8 min-h-full">
              {(["mobile", "tablet", "desktop"] as DeviceType[]).map((d) => (
                <div key={d} className="flex justify-center">
                  {renderDevice(d)}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="h-full flex items-start justify-center p-8 overflow-auto">
            {renderDevice(device)}
          </div>
        )}
      </div>

      {/* Debug Panel */}
      <AnimatePresence>
        {viewMode === "debug" && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 200, opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-t border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl overflow-hidden"
          >
            <div className="p-4 h-full overflow-auto">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium">Debug Console</h3>
                <Badge variant="secondary">
                  <CheckCircleIcon className="h-3 w-3 mr-1" />
                  No errors
                </Badge>
              </div>
              <div className="space-y-2 font-mono text-xs">
                <div className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  <span className="text-zinc-400">Page loaded successfully</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  <span className="text-zinc-400">Theme applied: {project.theme}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  <span className="text-zinc-400">Modules loaded: {project.modules?.join(", ")}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-blue-400">ℹ</span>
                  <span className="text-zinc-400">Device: {device} | Mode: {viewMode}</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Icons
function ExternalLinkIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
    </svg>
  )
}