"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Card from "@/components/ui/card"
import { AnimatePresence, motion } from "framer-motion"
import { 
  SparklesIcon,
  LightBulbIcon,
  PencilIcon,
  PhotoIcon,
  MegaphoneIcon,
  HeartIcon,
  NewspaperIcon,
  CalendarDaysIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  DocumentDuplicateIcon,
  AdjustmentsHorizontalIcon,
  LanguageIcon,
  MicrophoneIcon,
  ChatBubbleLeftRightIcon,
  BookOpenIcon,
  AcademicCapIcon,
  BellIcon,
  HandRaisedIcon,
  GlobeAltIcon
} from "@heroicons/react/24/outline"

interface AIContentAssistantProps {
  project: any
}

type ContentType = "hero" | "event" | "sermon" | "announcement" | "prayer" | "testimony" | "welcome" | "about"
type ToneType = "inspirational" | "welcoming" | "urgent" | "celebratory" | "reflective" | "educational"

interface ContentSuggestion {
  id: string
  type: ContentType
  title: string
  content: string
  tone: ToneType
  keywords: string[]
  cta?: string
  imagePrompt?: string
}

export default function AIContentAssistant({ project }: AIContentAssistantProps) {
  const [selectedType, setSelectedType] = useState<ContentType>("hero")
  const [selectedTone, setSelectedTone] = useState<ToneType>("welcoming")
  const [isGenerating, setIsGenerating] = useState(false)
  const [suggestions, setSuggestions] = useState<ContentSuggestion[]>([])
  const [customPrompt, setCustomPrompt] = useState("")
  const [selectedSuggestion, setSelectedSuggestion] = useState<ContentSuggestion | null>(null)

  const contentTypes = [
    { id: "hero", label: "Hero Headlines", icon: MegaphoneIcon, description: "Powerful welcome messages" },
    { id: "event", label: "Event Descriptions", icon: CalendarDaysIcon, description: "Engaging event copy" },
    { id: "sermon", label: "Sermon Series", icon: BookOpenIcon, description: "Series titles and descriptions" },
    { id: "announcement", label: "Announcements", icon: BellIcon, description: "Church news and updates" },
    { id: "prayer", label: "Prayer Focus", icon: HeartIcon, description: "Prayer prompts and devotionals" },
    { id: "testimony", label: "Testimonies", icon: ChatBubbleLeftRightIcon, description: "Story templates" },
    { id: "welcome", label: "Welcome Messages", icon: HandRaisedIcon, description: "First-time visitor content" },
    { id: "about", label: "About Content", icon: GlobeAltIcon, description: "Mission and vision statements" }
  ]

  const tones = [
    { id: "inspirational", label: "Inspirational", color: "text-purple-400" },
    { id: "welcoming", label: "Welcoming", color: "text-green-400" },
    { id: "urgent", label: "Urgent", color: "text-red-400" },
    { id: "celebratory", label: "Celebratory", color: "text-amber-400" },
    { id: "reflective", label: "Reflective", color: "text-blue-400" },
    { id: "educational", label: "Educational", color: "text-indigo-400" }
  ]

  const generateContent = async () => {
    setIsGenerating(true)
    
    // Simulate AI generation
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Sample AI-generated content
    const newSuggestions: ContentSuggestion[] = [
      {
        id: "1",
        type: selectedType,
        title: "Welcome Home - You Belong Here",
        content: "Experience the warmth of genuine community and discover your purpose. Join us this Sunday as we explore what it means to truly belong in God's family. Everyone has a seat at the table.",
        tone: selectedTone,
        keywords: ["belonging", "community", "purpose", "family"],
        cta: "Plan Your Visit",
        imagePrompt: "Diverse group of people warmly welcoming newcomers at modern church entrance"
      },
      {
        id: "2",
        type: selectedType,
        title: "Come As You Are - Find Grace Here",
        content: "No matter where you've been or what you've done, there's grace waiting for you. Join our family this Sunday and experience unconditional love and acceptance. Your story matters here.",
        tone: selectedTone,
        keywords: ["grace", "acceptance", "love", "welcome"],
        cta: "Join Us Sunday",
        imagePrompt: "Open church doors with warm light streaming out, people of all backgrounds entering"
      },
      {
        id: "3",
        type: selectedType,
        title: "New Here? Perfect Timing!",
        content: "We believe it's no accident you found us. This Sunday, discover a church that feels like home, where your questions are welcome and your journey is honored. Let's grow together.",
        tone: selectedTone,
        keywords: ["new", "journey", "growth", "questions"],
        cta: "Get Connected",
        imagePrompt: "Friendly church greeters with welcome signs and warm smiles"
      }
    ]
    
    setSuggestions(newSuggestions)
    setIsGenerating(false)
  }

  const enhanceContent = (content: string) => {
    // AI enhancement logic
    return content + " ✨ Enhanced with AI"
  }

  const translateContent = (content: string, language: string) => {
    // Translation logic
    console.log(`Translating to ${language}:`, content)
  }

  const copySuggestion = (suggestion: ContentSuggestion) => {
    const text = `${suggestion.title}\n\n${suggestion.content}\n\nCTA: ${suggestion.cta}`
    navigator.clipboard.writeText(text)
  }

  return (
    <Card variant="glass">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold">AI Content Assistant</h3>
            <p className="text-sm text-zinc-400">Generate compelling content with AI</p>
          </div>
          <Badge variant="secondary">
            <SparklesIcon className="h-3 w-3 mr-1" />
            AI Powered
          </Badge>
        </div>

        <div className="space-y-6">
          {/* Content Type Selection */}
          <div>
            <label className="text-sm font-medium mb-3 block">What content do you need?</label>
            <div className="grid grid-cols-4 gap-2">
              {contentTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setSelectedType(type.id as ContentType)}
                  className={cn(
                    "p-3 rounded-lg border text-center transition-all",
                    selectedType === type.id
                      ? "bg-[var(--color-primary)]/10 border-[var(--color-primary)]"
                      : "bg-white/5 border-white/10 hover:border-white/20"
                  )}
                >
                  <type.icon className="h-6 w-6 mx-auto mb-2" />
                  <p className="text-xs font-medium">{type.label}</p>
                </button>
              ))}
            </div>
          </div>

          {/* Tone Selection */}
          <div>
            <label className="text-sm font-medium mb-3 block">Select tone</label>
            <div className="flex flex-wrap gap-2">
              {tones.map((tone) => (
                <button
                  key={tone.id}
                  onClick={() => setSelectedTone(tone.id as ToneType)}
                  className={cn(
                    "px-4 py-2 rounded-lg border text-sm transition-all",
                    selectedTone === tone.id
                      ? "bg-white/10 border-white/30"
                      : "bg-white/5 border-white/10 hover:border-white/20"
                  )}
                >
                  <span className={tone.color}>{tone.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Custom Prompt */}
          <div>
            <label className="text-sm font-medium mb-2 block">Additional context (optional)</label>
            <textarea
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="E.g., 'We're launching a new youth program' or 'Easter service with baptisms'"
              className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-sm resize-none"
              rows={3}
            />
          </div>

          {/* Generate Button */}
          <Button
            variant="primary"
            className="w-full"
            leftIcon={isGenerating ? <ArrowPathIcon className="h-5 w-5 animate-spin" /> : <SparklesIcon className="h-5 w-5" />}
            onClick={generateContent}
            disabled={isGenerating}
          >
            {isGenerating ? "Generating Content..." : "Generate Content"}
          </Button>

          {/* Suggestions */}
          <AnimatePresence>
            {suggestions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">AI Suggestions</h4>
                  <Button
                    variant="glass"
                    size="sm"
                    leftIcon={<ArrowPathIcon className="h-4 w-4" />}
                    onClick={generateContent}
                  >
                    Regenerate
                  </Button>
                </div>

                {suggestions.map((suggestion, idx) => (
                  <motion.div
                    key={suggestion.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: idx * 0.1 }}
                    className={cn(
                      "p-4 bg-white/5 rounded-lg border cursor-pointer transition-all",
                      selectedSuggestion?.id === suggestion.id
                        ? "border-[var(--color-primary)]"
                        : "border-white/10 hover:border-white/20"
                    )}
                    onClick={() => setSelectedSuggestion(suggestion)}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h5 className="font-medium">{suggestion.title}</h5>
                      <Badge variant="secondary" size="sm">
                        {suggestion.tone}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-zinc-400 mb-3">{suggestion.content}</p>
                    
                    {suggestion.cta && (
                      <div className="flex items-center gap-2 mb-3">
                        <span className="text-xs text-zinc-400">CTA:</span>
                        <span className="text-sm font-medium text-blue-400">{suggestion.cta}</span>
                      </div>
                    )}

                    <div className="flex items-center gap-2">
                      <Button
                        variant="glass"
                        size="sm"
                        leftIcon={<DocumentDuplicateIcon className="h-4 w-4" />}
                        onClick={(e) => {
                          e.stopPropagation()
                          copySuggestion(suggestion)
                        }}
                      >
                        Copy
                      </Button>
                      <Button
                        variant="glass"
                        size="sm"
                        leftIcon={<PencilIcon className="h-4 w-4" />}
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log("Edit suggestion")
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="glass"
                        size="sm"
                        leftIcon={<PhotoIcon className="h-4 w-4" />}
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log("Generate image")
                        }}
                      >
                        Image
                      </Button>
                      <Button
                        variant="glass"
                        size="sm"
                        leftIcon={<LanguageIcon className="h-4 w-4" />}
                        onClick={(e) => {
                          e.stopPropagation()
                          translateContent(suggestion.content, "Spanish")
                        }}
                      >
                        Translate
                      </Button>
                    </div>

                    {suggestion.keywords && (
                      <div className="flex items-center gap-2 mt-3">
                        <span className="text-xs text-zinc-400">Keywords:</span>
                        <div className="flex flex-wrap gap-1">
                          {suggestion.keywords.map((keyword) => (
                            <span key={keyword} className="text-xs bg-white/10 px-2 py-1 rounded">
                              {keyword}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          {/* AI Tools */}
          <div className="grid grid-cols-2 gap-3">
            <Card variant="glass" className="p-4">
              <div className="flex items-start gap-3">
                <MicrophoneIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                <div>
                  <h5 className="text-sm font-medium mb-1">Voice to Text</h5>
                  <p className="text-xs text-zinc-400">Dictate your content</p>
                  <Button variant="glass" size="sm" className="mt-2">
                    Start Recording
                  </Button>
                </div>
              </div>
            </Card>

            <Card variant="glass" className="p-4">
              <div className="flex items-start gap-3">
                <AcademicCapIcon className="h-5 w-5 text-purple-400 mt-0.5" />
                <div>
                  <h5 className="text-sm font-medium mb-1">Scripture Finder</h5>
                  <p className="text-xs text-zinc-400">Find relevant verses</p>
                  <Button variant="glass" size="sm" className="mt-2">
                    Search Bible
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </Card>
  )
}

// Icons
function LanguageIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
    </svg>
  )
}