"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Card from "@/components/ui/card"
import DataCard from "@/components/ui/data-card"
import { motion } from "framer-motion"
import { 
  ChartBarIcon,
  EyeIcon,
  UserGroupIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  GlobeAltIcon,
  MapPinIcon,
  ArrowPathIcon,
  CalendarDaysIcon,
  FunnelIcon,
  HandRaisedIcon,
  BanknotesIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  ShareIcon,
  DocumentArrowDownIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from "@heroicons/react/24/outline"

interface AnalyticsDashboardProps {
  project: any
}

type DateRange = "today" | "week" | "month" | "year"
type MetricType = "overview" | "engagement" | "conversions" | "performance"

export default function AnalyticsDashboard({ project }: AnalyticsDashboardProps) {
  const [dateRange, setDateRange] = useState<DateRange>("week")
  const [metricType, setMetricType] = useState<MetricType>("overview")

  // Sample analytics data - using fixed values to avoid hydration mismatches
  const analytics = {
    overview: {
      totalViews: 45234,
      uniqueVisitors: 12453,
      avgDuration: "3:24",
      bounceRate: 32,
      conversions: 3421,
      conversionRate: 27.5
    },
    engagement: {
      pageViews: [
        { page: "Home", views: 15234, avgTime: "2:45" },
        { page: "Events", views: 8923, avgTime: "3:12" },
        { page: "Giving", views: 7234, avgTime: "4:56" },
        { page: "About", views: 5632, avgTime: "1:34" },
        { page: "Connect", views: 4521, avgTime: "2:23" }
      ],
      interactions: [
        { action: "Watch Sermon", count: 3421, rate: 75.2 },
        { action: "Give Online", count: 2134, rate: 46.8 },
        { action: "Event Registration", count: 1523, rate: 33.4 },
        { action: "Prayer Request", count: 892, rate: 19.6 },
        { action: "Connect Card", count: 623, rate: 13.7 }
      ]
    },
    devices: {
      mobile: { users: 8347, percentage: 67, avgTime: "2:54" },
      desktop: { users: 3482, percentage: 28, avgTime: "4:12" },
      tablet: { users: 624, percentage: 5, avgTime: "3:36" }
    },
    locations: [
      { city: "Austin", state: "TX", users: 3421, percentage: 27.5 },
      { city: "Dallas", state: "TX", users: 2134, percentage: 17.1 },
      { city: "Houston", state: "TX", users: 1892, percentage: 15.2 },
      { city: "San Antonio", state: "TX", users: 1234, percentage: 9.9 },
      { city: "Other", state: "", users: 3772, percentage: 30.3 }
    ],
    traffic: {
      direct: { users: 4523, percentage: 36.3 },
      social: { users: 3421, percentage: 27.5 },
      search: { users: 2892, percentage: 23.2 },
      referral: { users: 1617, percentage: 13.0 }
    }
  }

  // Chart data
  const getChartData = () => {
    const days = dateRange === "week" ? 7 : dateRange === "month" ? 30 : dateRange === "year" ? 12 : 24
    // Use deterministic values based on index to avoid hydration mismatches
    return Array.from({ length: days }, (_, i) => ({
      label: dateRange === "year" ? `Month ${i + 1}` : dateRange === "today" ? `${i}:00` : `Day ${i + 1}`,
      views: 500 + (i * 47 + 123) % 1000, // Deterministic values
      visitors: 200 + (i * 31 + 89) % 500,
      conversions: 20 + (i * 13 + 41) % 100
    }))
  }

  const chartData = getChartData()

  const renderMetricContent = () => {
    switch (metricType) {
      case "overview":
        return (
          <div className="space-y-6">
            {/* Main Stats */}
            <div className="grid grid-cols-4 gap-4">
              <DataCard
                title="Total Views"
                value={analytics.overview.totalViews.toLocaleString()}
                icon={EyeIcon}
                trend={{ value: 18, positive: true }}
                footer="vs last period"
                variant="glass"
              />
              <DataCard
                title="Unique Visitors"
                value={analytics.overview.uniqueVisitors.toLocaleString()}
                icon={UserGroupIcon}
                trend={{ value: 12, positive: true }}
                footer="vs last period"
                variant="glass"
              />
              <DataCard
                title="Avg. Duration"
                value={analytics.overview.avgDuration}
                icon={ClockIcon}
                trend={{ value: 5, positive: true }}
                footer="vs last period"
                variant="glass"
              />
              <DataCard
                title="Conversions"
                value={analytics.overview.conversions.toLocaleString()}
                icon={ArrowTrendingUpIcon}
                trend={{ value: 23, positive: true }}
                footer={`${analytics.overview.conversionRate}% rate`}
                variant="glass"
              />
            </div>

            {/* Chart */}
            <Card variant="glass">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium">Traffic Overview</h3>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                      <span className="text-sm">Views</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-purple-500" />
                      <span className="text-sm">Visitors</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500" />
                      <span className="text-sm">Conversions</span>
                    </div>
                  </div>
                </div>
                
                <div className="h-64 relative">
                  <div className="absolute inset-0 flex items-end justify-around">
                    {chartData.map((data, i) => (
                      <div key={i} className="flex-1 mx-0.5 flex flex-col items-center gap-1">
                        <div className="w-full flex flex-col gap-1">
                          <motion.div 
                            initial={{ height: 0 }}
                            animate={{ height: `${(data.conversions / 150) * 100}%` }}
                            transition={{ delay: i * 0.05 }}
                            className="w-full bg-green-500 rounded-t" 
                          />
                          <motion.div 
                            initial={{ height: 0 }}
                            animate={{ height: `${(data.visitors / 800) * 100}%` }}
                            transition={{ delay: i * 0.05 }}
                            className="w-full bg-purple-500" 
                          />
                          <motion.div 
                            initial={{ height: 0 }}
                            animate={{ height: `${(data.views / 1500) * 100}%` }}
                            transition={{ delay: i * 0.05 }}
                            className="w-full bg-blue-500 rounded-b" 
                          />
                        </div>
                        {(i === 0 || i === chartData.length - 1 || i % Math.max(1, Math.floor(chartData.length / 4)) === 0) && (
                          <p className="text-xs text-zinc-400 mt-2">{data.label}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Card>

            {/* Device & Location Stats */}
            <div className="grid grid-cols-2 gap-6">
              {/* Device Breakdown */}
              <Card variant="glass">
                <div className="p-6">
                  <h3 className="font-medium mb-4">Device Breakdown</h3>
                  <div className="space-y-4">
                    {Object.entries(analytics.devices).map(([device, data]) => (
                      <div key={device}>
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center gap-2">
                            {device === "mobile" ? <DevicePhoneMobileIcon className="h-4 w-4" /> :
                             device === "desktop" ? <ComputerDesktopIcon className="h-4 w-4" /> :
                             <DeviceTabletIcon className="h-4 w-4" />}
                            <span className="text-sm capitalize">{device}</span>
                          </div>
                          <span className="text-sm font-medium">{data.percentage}%</span>
                        </div>
                        <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
                          <motion.div 
                            initial={{ width: 0 }}
                            animate={{ width: `${data.percentage}%` }}
                            transition={{ duration: 0.5 }}
                            className={cn(
                              "h-full",
                              device === "mobile" ? "bg-blue-500" :
                              device === "desktop" ? "bg-purple-500" :
                              "bg-green-500"
                            )} 
                          />
                        </div>
                        <p className="text-xs text-zinc-400 mt-1">
                          {data.users.toLocaleString()} users • {data.avgTime} avg
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>

              {/* Top Locations */}
              <Card variant="glass">
                <div className="p-6">
                  <h3 className="font-medium mb-4">Top Locations</h3>
                  <div className="space-y-3">
                    {analytics.locations.map((location, idx) => (
                      <div key={idx} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <MapPinIcon className="h-4 w-4 text-zinc-400" />
                          <div>
                            <p className="text-sm font-medium">{location.city}</p>
                            {location.state && <p className="text-xs text-zinc-400">{location.state}</p>}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{location.users.toLocaleString()}</p>
                          <p className="text-xs text-zinc-400">{location.percentage}%</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )

      case "engagement":
        return (
          <div className="space-y-6">
            {/* Page Performance */}
            <Card variant="glass">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-4">Page Performance</h3>
                <div className="space-y-3">
                  {analytics.engagement.pageViews.map((page) => (
                    <div key={page.page} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <div className="flex items-center gap-3">
                        <GlobeAltIcon className="h-5 w-5 text-blue-400" />
                        <div>
                          <p className="font-medium">{page.page}</p>
                          <p className="text-sm text-zinc-400">Avg. time: {page.avgTime}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{page.views.toLocaleString()}</p>
                        <p className="text-sm text-zinc-400">views</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* User Interactions */}
            <Card variant="glass">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-4">User Interactions</h3>
                <div className="grid grid-cols-2 gap-4">
                  {analytics.engagement.interactions.map((interaction) => (
                    <div key={interaction.action} className="p-4 bg-white/5 rounded-lg">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <p className="font-medium">{interaction.action}</p>
                          <p className="text-2xl font-bold mt-1">{interaction.count.toLocaleString()}</p>
                        </div>
                        {interaction.rate > 50 ? 
                          <ArrowUpIcon className="h-5 w-5 text-green-400" /> :
                          <ArrowDownIcon className="h-5 w-5 text-red-400" />
                        }
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                          style={{ width: `${interaction.rate}%` }} 
                        />
                      </div>
                      <p className="text-xs text-zinc-400 mt-2">{interaction.rate}% engagement rate</p>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        )

      case "conversions":
        return (
          <div className="space-y-6">
            {/* Conversion Funnel */}
            <Card variant="glass">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-6">Conversion Funnel</h3>
                <div className="space-y-4">
                  {[
                    { step: "Page Views", value: 45234, icon: EyeIcon },
                    { step: "Engaged Users", value: 23421, icon: HandRaisedIcon },
                    { step: "Started Action", value: 8923, icon: ChatBubbleLeftRightIcon },
                    { step: "Completed", value: 3421, icon: CheckCircleIcon }
                  ].map((step, idx) => (
                    <div key={step.step}>
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <step.icon className="h-5 w-5 text-blue-400" />
                          <span className="font-medium">{step.step}</span>
                        </div>
                        <span className="font-bold">{step.value.toLocaleString()}</span>
                      </div>
                      {idx < 3 && (
                        <div className="ml-8 my-2 flex items-center gap-2">
                          <ArrowDownIcon className="h-4 w-4 text-zinc-400" />
                          <span className="text-sm text-zinc-400">
                            {Math.round((step.value / 45234) * 100)}% retention
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Goal Completions */}
            <div className="grid grid-cols-3 gap-4">
              <Card variant="glass">
                <div className="p-6 text-center">
                  <BanknotesIcon className="h-12 w-12 mx-auto mb-3 text-green-400" />
                  <p className="text-3xl font-bold">$45,234</p>
                  <p className="text-sm text-zinc-400 mt-1">Online Giving</p>
                  <Badge variant="success" className="mt-3">+23%</Badge>
                </div>
              </Card>
              <Card variant="glass">
                <div className="p-6 text-center">
                  <HandRaisedIcon className="h-12 w-12 mx-auto mb-3 text-blue-400" />
                  <p className="text-3xl font-bold">892</p>
                  <p className="text-sm text-zinc-400 mt-1">New Connections</p>
                  <Badge variant="success" className="mt-3">+18%</Badge>
                </div>
              </Card>
              <Card variant="glass">
                <div className="p-6 text-center">
                  <HeartIcon className="h-12 w-12 mx-auto mb-3 text-pink-400" />
                  <p className="text-3xl font-bold">234</p>
                  <p className="text-sm text-zinc-400 mt-1">Prayer Requests</p>
                  <Badge variant="success" className="mt-3">+12%</Badge>
                </div>
              </Card>
            </div>
          </div>
        )

      case "performance":
        return (
          <div className="space-y-6">
            {/* Traffic Sources */}
            <Card variant="glass">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-4">Traffic Sources</h3>
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    {Object.entries(analytics.traffic).map(([source, data]) => (
                      <div key={source} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{source}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{data.users.toLocaleString()}</span>
                          <Badge variant="secondary" size="sm">{data.percentage}%</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="relative h-48">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-40 h-40 relative">
                        {Object.entries(analytics.traffic).map(([source, data], idx) => {
                          const colors = ["bg-blue-500", "bg-purple-500", "bg-green-500", "bg-amber-500"]
                          const rotation = idx * 90
                          return (
                            <div
                              key={source}
                              className={cn(
                                "absolute inset-0 rounded-full",
                                colors[idx]
                              )}
                              style={{
                                clipPath: `polygon(50% 50%, ${50 + 50 * Math.cos((rotation - 90) * Math.PI / 180)}% ${50 + 50 * Math.sin((rotation - 90) * Math.PI / 180)}%, ${50 + 50 * Math.cos((rotation + data.percentage * 3.6 - 90) * Math.PI / 180)}% ${50 + 50 * Math.sin((rotation + data.percentage * 3.6 - 90) * Math.PI / 180)}%)`
                              }}
                            />
                          )
                        })}
                        <div className="absolute inset-4 bg-[var(--bg-card)] rounded-full" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Performance Metrics */}
            <div className="grid grid-cols-3 gap-4">
              <Card variant="glass">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm text-zinc-400">Load Time</p>
                    <Badge variant="success" size="sm">Good</Badge>
                  </div>
                  <p className="text-2xl font-bold">1.3s</p>
                  <p className="text-xs text-zinc-400 mt-1">Desktop average</p>
                </div>
              </Card>
              <Card variant="glass">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm text-zinc-400">Uptime</p>
                    <Badge variant="success" size="sm">Excellent</Badge>
                  </div>
                  <p className="text-2xl font-bold">99.9%</p>
                  <p className="text-xs text-zinc-400 mt-1">Last 30 days</p>
                </div>
              </Card>
              <Card variant="glass">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm text-zinc-400">Error Rate</p>
                    <Badge variant="success" size="sm">Low</Badge>
                  </div>
                  <p className="text-2xl font-bold">0.2%</p>
                  <p className="text-xs text-zinc-400 mt-1">4xx/5xx errors</p>
                </div>
              </Card>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Analytics Dashboard</h2>
          <p className="text-zinc-400">Track performance and engagement for {project.name}</p>
        </div>
        <div className="flex items-center gap-3">
          {/* Date Range Selector */}
          <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
            {(["today", "week", "month", "year"] as DateRange[]).map((range) => (
              <button
                key={range}
                onClick={() => setDateRange(range)}
                className={cn(
                  "px-3 py-1 rounded text-sm capitalize transition-colors",
                  dateRange === range
                    ? "bg-[var(--color-primary)] text-black"
                    : "hover:bg-white/10"
                )}
              >
                {range}
              </button>
            ))}
          </div>

          <Button
            variant="glass"
            size="sm"
            leftIcon={<ArrowPathIcon className="h-4 w-4" />}
            onClick={() => console.log("Refresh analytics")}
          >
            Refresh
          </Button>

          <Button
            variant="glass"
            size="sm"
            leftIcon={<DocumentArrowDownIcon className="h-4 w-4" />}
            onClick={() => console.log("Export report")}
          >
            Export
          </Button>
        </div>
      </div>

      {/* Metric Type Tabs */}
      <div className="flex items-center gap-4 border-b border-[var(--border-subtle)]">
        {[
          { id: "overview", label: "Overview", icon: ChartBarIcon },
          { id: "engagement", label: "Engagement", icon: HandRaisedIcon },
          { id: "conversions", label: "Conversions", icon: FunnelIcon },
          { id: "performance", label: "Performance", icon: ArrowTrendingUpIcon }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setMetricType(tab.id as MetricType)}
            className={cn(
              "flex items-center gap-2 px-4 py-3 border-b-2 transition-colors",
              metricType === tab.id
                ? "border-[var(--color-primary)] text-white"
                : "border-transparent text-zinc-400 hover:text-white"
            )}
          >
            <tab.icon className="h-4 w-4" />
            <span className="text-sm font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Metric Content */}
      {renderMetricContent()}
    </div>
  )
}

// Icons
function DeviceTabletIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
    </svg>
  )
}

function FunnelIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
    </svg>
  )
}

function DocumentArrowDownIcon(props: any) {
  return (
    <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m.75 12l3 3m0 0l3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
    </svg>
  )
}