import { ArrowUpIcon, ArrowDownIcon, CalendarIcon } from "@heroicons/react/24/outline"

export default function GivingOverview() {
  const givingStats = [
    {
      title: "Total Giving",
      amount: "$24,850",
      period: "This Month",
      change: "+12.4%",
      trend: "up",
    },
    {
      title: "Online Giving",
      amount: "$16,320",
      period: "This Month",
      change: "+18.7%",
      trend: "up",
    },
    {
      title: "In-Person Giving",
      amount: "$8,530",
      period: "This Month",
      change: "-3.2%",
      trend: "down",
    },
    {
      title: "Recurring Giving",
      amount: "$9,450",
      period: "This Month",
      change: "+5.8%",
      trend: "up",
    },
  ]

  return (
    <div className="grid grid-cols-2 gap-4">
      {givingStats.map((stat, index) => (
        <div
          key={index}
          className="bg-zinc-900/90 rounded-xl p-4 border border-zinc-800/20 hover:border-zinc-700/30 transition-all"
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-zinc-400">{stat.title}</h3>
            <div
              className={`flex items-center text-xs font-medium ${
                stat.trend === "up" ? "text-green-500" : "text-red-500"
              }`}
            >
              {stat.trend === "up" ? (
                <ArrowUpIcon className="h-3.5 w-3.5 mr-1" />
              ) : (
                <ArrowDownIcon className="h-3.5 w-3.5 mr-1" />
              )}
              {stat.change}
            </div>
          </div>
          <div className="flex items-end gap-2">
            <div className="text-2xl font-semibold">{stat.amount}</div>
            <div className="flex items-center text-xs text-zinc-500 mb-1">
              <CalendarIcon className="h-3 w-3 mr-1" />
              {stat.period}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
