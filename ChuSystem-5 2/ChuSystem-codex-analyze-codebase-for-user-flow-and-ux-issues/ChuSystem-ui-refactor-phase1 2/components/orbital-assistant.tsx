export default function OrbitalAssistant() {
  return (
    <div className="bg-[#141414] bg-gradient-to-b from-zinc-900/80 to-zinc-900/20 m-4 px-4 pt-4 pb-3 rounded-2xl shadow-ambient border border-zinc-800/30">
      <div className="flex items-center gap-3 mb-3">
        <div className="bg-gradient-to-br from-amber-500 to-red-500 w-8 h-8 rounded-full flex items-center justify-center text-white shadow-glow-amber">
          O
        </div>
        <div>
          <p className="text-sm font-medium">Hey, I'm <PERSON><PERSON>!</p>
          <p className="text-xs text-zinc-400">How can I help you?</p>
        </div>
      </div>

      <div className="h-16 w-full flex items-center justify-center mb-1 opacity-90">
        <svg width="160" height="40" viewBox="0 0 160 40" xmlns="http://www.w3.org/2000/svg">
          <path d="M0,20 Q40,5 80,20 T160,20" fill="none" stroke="url(#blueGradient)" strokeWidth="1.5" />
          <path d="M0,20 Q40,35 80,20 T160,20" fill="none" stroke="url(#lightBlueGradient)" strokeWidth="1.5" />
          <defs>
            <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.7" />
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="1" />
            </linearGradient>
            <linearGradient id="lightBlueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#60a5fa" stopOpacity="0.7" />
              <stop offset="100%" stopColor="#60a5fa" stopOpacity="1" />
            </linearGradient>
          </defs>
        </svg>
      </div>

      <p className="text-xs text-zinc-400">Hey Orlo! Please go to Team Projects.</p>
    </div>
  )
}
