import { Plus } from "lucide-react"
import { Button } from "./ui/button"

interface AddCardProps {
  text?: string
  className?: string
}

export default function AddCard({ text = "Add", className = "" }: AddCardProps) {
  return (
    <div
      className={`border border-dashed border-zinc-700/60 rounded-2xl flex items-center justify-center h-24 hover:border-zinc-600/60 transition-all group ${className}`}
    >
      <Button
        variant="ghost"
        className="flex flex-col items-center w-full h-full justify-center text-blue-400 hover:text-blue-300 hover:bg-transparent rounded-2xl"
      >
        <Plus className="h-5 w-5 mb-1 group-hover:scale-110 transition-transform" />
        <span className="text-xs">{text}</span>
      </Button>
    </div>
  )
}
