"use client"

import { useState } from "react"
import { X, Calendar, Clock, Users, MessageSquare, CheckSquare } from "lucide-react"
import { Button } from "./ui/button"
import TeamAvatars from "./team-avatars"

interface TaskDetailModalProps {
  isOpen: boolean
  onClose: () => void
  taskId: string
  title: string
  color: string
  avatars: string[]
}

export default function TaskDetailModal({ isOpen, onClose, taskId, title, color, avatars }: TaskDetailModalProps) {
  const [taskTitle, setTaskTitle] = useState(title)

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-zinc-900 border border-zinc-800 rounded-xl w-full max-w-lg shadow-xl">
        <div className="flex items-center justify-between p-4 border-b border-zinc-800">
          <h3 className="text-lg font-semibold">Task Details</h3>
          <Button variant="ghost" size="icon" onClick={onClose} className="rounded-full h-7 w-7">
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-5">
          <div className={`${color} h-2 w-16 rounded-full mb-4`}></div>

          <div className="mb-4">
            <input
              type="text"
              value={taskTitle}
              onChange={(e) => setTaskTitle(e.target.value)}
              className="w-full bg-transparent text-xl font-semibold border-none focus:outline-none focus:ring-1 focus:ring-blue-500/30 rounded-md p-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="flex items-center gap-2 text-zinc-400">
              <Calendar className="h-4 w-4" />
              <span className="text-sm">October 19, 2022</span>
            </div>

            <div className="flex items-center gap-2 text-zinc-400">
              <Clock className="h-4 w-4" />
              <span className="text-sm">1:00 PM - 2:00 PM</span>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <Users className="h-4 w-4" />
              Assigned to
            </h4>
            <div className="flex items-center justify-between">
              <TeamAvatars avatars={avatars.length ? avatars : ["/avatar1.png"]} size="md" />
              <Button variant="outline" size="sm" className="text-xs">
                Assign
              </Button>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2">Description</h4>
            <textarea
              className="w-full h-24 bg-zinc-800/50 border border-zinc-700/50 rounded-lg p-3 text-sm resize-none focus:outline-none focus:ring-1 focus:ring-blue-500/30"
              placeholder="Add a description..."
            ></textarea>
          </div>

          <div className="flex items-center gap-4 text-zinc-400 mb-6">
            <div className="flex items-center gap-1">
              <MessageSquare className="h-4 w-4" />
              <span className="text-xs">0 Comments</span>
            </div>

            <div className="flex items-center gap-1">
              <CheckSquare className="h-4 w-4" />
              <span className="text-xs">0/3 Subtasks</span>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-end gap-3 p-4 border-t border-zinc-800">
          <Button variant="outline" size="sm" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="primary" size="sm">
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  )
}
