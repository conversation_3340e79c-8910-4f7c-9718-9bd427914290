"use client"

import React, { useState } from "react"
import {
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  UserGroupIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from "@heroicons/react/24/outline"

interface Event {
  id: number
  title: string
  date: string
  time: string
  location: string
  attendees: number
  type: "Service" | "Youth" | "Outreach" | "Meeting" | "Other"
  status: "Upcoming" | "In Progress" | "Completed" | "Cancelled"
}

export default function EventsList() {
  const [sortField, setSortField] = useState<keyof Event>("date")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [selectedEvents, setSelectedEvents] = useState<number[]>([])

  const events: Event[] = [
    {
      id: 1,
      title: "Sunday Morning Service",
      date: "2023-05-19",
      time: "9:00 AM - 11:00 AM",
      location: "Main Sanctuary",
      attendees: 120,
      type: "Service",
      status: "Upcoming",
    },
    {
      id: 2,
      title: "Youth Group Meeting",
      date: "2023-05-22",
      time: "6:30 PM - 8:00 PM",
      location: "Youth Center",
      attendees: 35,
      type: "Youth",
      status: "Upcoming",
    },
    {
      id: 3,
      title: "Worship Team Rehearsal",
      date: "2023-05-23",
      time: "7:00 PM - 9:00 PM",
      location: "Main Sanctuary",
      attendees: 12,
      type: "Meeting",
      status: "Upcoming",
    },
    {
      id: 4,
      title: "Food Drive",
      date: "2023-05-20",
      time: "10:00 AM - 2:00 PM",
      location: "Community Center",
      attendees: 25,
      type: "Outreach",
      status: "Upcoming",
    },
    {
      id: 5,
      title: "Prayer Meeting",
      date: "2023-05-18",
      time: "7:00 PM - 8:00 PM",
      location: "Chapel",
      attendees: 15,
      type: "Meeting",
      status: "Completed",
    },
    {
      id: 6,
      title: "Bible Study",
      date: "2023-05-21",
      time: "6:00 PM - 7:30 PM",
      location: "Fellowship Hall",
      attendees: 20,
      type: "Meeting",
      status: "Upcoming",
    },
  ]

  const handleSort = (field: keyof Event) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  const sortedEvents = [...events].sort((a, b) => {
    if (a[sortField] < b[sortField]) return sortDirection === "asc" ? -1 : 1
    if (a[sortField] > b[sortField]) return sortDirection === "asc" ? 1 : -1
    return 0
  })

  const toggleSelectAll = () => {
    if (selectedEvents.length === events.length) {
      setSelectedEvents([])
    } else {
      setSelectedEvents(events.map((event) => event.id))
    }
  }

  const toggleSelectEvent = (id: number) => {
    if (selectedEvents.includes(id)) {
      setSelectedEvents(selectedEvents.filter((eventId) => eventId !== id))
    } else {
      setSelectedEvents([...selectedEvents, id])
    }
  }

  const getTypeClass = (type: string) => {
    switch (type) {
      case "Service":
        return "bg-blue-500/20 text-blue-400"
      case "Youth":
        return "bg-purple-500/20 text-purple-400"
      case "Outreach":
        return "bg-green-500/20 text-green-400"
      case "Meeting":
        return "bg-amber-500/20 text-amber-400"
      default:
        return "bg-zinc-500/20 text-zinc-400"
    }
  }

  const getStatusClass = (status: string) => {
    switch (status) {
      case "Upcoming":
        return "bg-blue-500/20 text-blue-400"
      case "In Progress":
        return "bg-green-500/20 text-green-400"
      case "Completed":
        return "bg-zinc-500/20 text-zinc-400"
      case "Cancelled":
        return "bg-red-500/20 text-red-400"
      default:
        return "bg-zinc-500/20 text-zinc-400"
    }
  }

  return (
    <div className="bg-zinc-900/90 rounded-xl border border-zinc-800/20 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-zinc-800/50 border-b border-zinc-700/30">
              <th className="px-4 py-3 text-left">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedEvents.length === events.length}
                    onChange={toggleSelectAll}
                    className="mr-3 h-4 w-4 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                  />
                  <button
                    className="flex items-center text-xs font-medium text-zinc-400 hover:text-white"
                    onClick={() => handleSort("title")}
                  >
                    Event
                    {sortField === "title" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? (
                          <ChevronUpIcon className="h-4 w-4" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4" />
                        )}
                      </span>
                    )}
                  </button>
                </div>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  className="flex items-center text-xs font-medium text-zinc-400 hover:text-white"
                  onClick={() => handleSort("date")}
                >
                  Date & Time
                  {sortField === "date" && (
                    <span className="ml-1">
                      {sortDirection === "asc" ? (
                        <ChevronUpIcon className="h-4 w-4" />
                      ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </span>
                  )}
                </button>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  className="flex items-center text-xs font-medium text-zinc-400 hover:text-white"
                  onClick={() => handleSort("location")}
                >
                  Location
                  {sortField === "location" && (
                    <span className="ml-1">
                      {sortDirection === "asc" ? (
                        <ChevronUpIcon className="h-4 w-4" />
                      ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </span>
                  )}
                </button>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  className="flex items-center text-xs font-medium text-zinc-400 hover:text-white"
                  onClick={() => handleSort("type")}
                >
                  Type
                  {sortField === "type" && (
                    <span className="ml-1">
                      {sortDirection === "asc" ? (
                        <ChevronUpIcon className="h-4 w-4" />
                      ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </span>
                  )}
                </button>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  className="flex items-center text-xs font-medium text-zinc-400 hover:text-white"
                  onClick={() => handleSort("status")}
                >
                  Status
                  {sortField === "status" && (
                    <span className="ml-1">
                      {sortDirection === "asc" ? (
                        <ChevronUpIcon className="h-4 w-4" />
                      ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </span>
                  )}
                </button>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  className="flex items-center text-xs font-medium text-zinc-400 hover:text-white"
                  onClick={() => handleSort("attendees")}
                >
                  Attendees
                  {sortField === "attendees" && (
                    <span className="ml-1">
                      {sortDirection === "asc" ? (
                        <ChevronUpIcon className="h-4 w-4" />
                      ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </span>
                  )}
                </button>
              </th>
              <th className="px-4 py-3 text-right">Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedEvents.map((event) => (
              <tr key={event.id} className="border-b border-zinc-800/30 hover:bg-zinc-800/20 transition-colors">
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedEvents.includes(event.id)}
                      onChange={() => toggleSelectEvent(event.id)}
                      className="mr-3 h-4 w-4 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                    />
                    <span className="font-medium">{event.title}</span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <CalendarIcon className="h-3.5 w-3.5 mr-1.5 text-zinc-500" />
                      <span>
                        {new Date(event.date).toLocaleDateString("en-US", {
                          weekday: "short",
                          month: "short",
                          day: "numeric",
                        })}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <ClockIcon className="h-3.5 w-3.5 mr-1.5 text-zinc-500" />
                      <span>{event.time}</span>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <MapPinIcon className="h-3.5 w-3.5 mr-1.5 text-zinc-500" />
                    <span>{event.location}</span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <span className={`px-2 py-1 rounded-full text-xs ${getTypeClass(event.type)}`}>{event.type}</span>
                </td>
                <td className="px-4 py-3">
                  <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(event.status)}`}>
                    {event.status}
                  </span>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <UserGroupIcon className="h-3.5 w-3.5 mr-1.5 text-zinc-500" />
                    <span>{event.attendees}</span>
                  </div>
                </td>
                <td className="px-4 py-3 text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <button className="p-1 text-zinc-400 hover:text-white transition-colors">
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button className="p-1 text-zinc-400 hover:text-white transition-colors">
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button className="p-1 text-zinc-400 hover:text-red-400 transition-colors">
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
