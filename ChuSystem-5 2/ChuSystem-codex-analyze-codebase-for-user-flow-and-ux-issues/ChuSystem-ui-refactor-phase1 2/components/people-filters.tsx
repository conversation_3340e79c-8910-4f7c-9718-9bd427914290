"use client"

import { useState } from "react"
import { ChevronDownIcon, ChevronRightIcon, XMarkIcon } from "@heroicons/react/16/solid"
import { Button } from "./ui/button"

interface FilterGroup {
  name: string
  expanded: boolean
  filters: {
    id: string
    label: string
    count: number
    selected: boolean
  }[]
}

export default function PeopleFilters() {
  const [filterGroups, setFilterGroups] = useState<FilterGroup[]>([
    {
      name: "Status",
      expanded: true,
      filters: [
        { id: "active", label: "Active", count: 42, selected: true },
        { id: "inactive", label: "Inactive", count: 15, selected: false },
        { id: "new", label: "New Member", count: 8, selected: false },
        { id: "visitor", label: "First-Time Visitor", count: 6, selected: false },
      ],
    },
    {
      name: "Ministry",
      expanded: true,
      filters: [
        { id: "worship", label: "Worship Team", count: 12, selected: false },
        { id: "youth", label: "Youth Ministry", count: 18, selected: false },
        { id: "children", label: "Children's Ministry", count: 14, selected: false },
        { id: "outreach", label: "Outreach", count: 9, selected: false },
      ],
    },
    {
      name: "Groups",
      expanded: false,
      filters: [
        { id: "small-groups", label: "Small Groups", count: 35, selected: false },
        { id: "bible-study", label: "Bible Study", count: 22, selected: false },
        { id: "prayer", label: "Prayer Team", count: 8, selected: false },
      ],
    },
  ])

  const toggleGroup = (groupName: string) => {
    setFilterGroups((groups) =>
      groups.map((group) => (group.name === groupName ? { ...group, expanded: !group.expanded } : group)),
    )
  }

  const toggleFilter = (groupName: string, filterId: string) => {
    setFilterGroups((groups) =>
      groups.map((group) =>
        group.name === groupName
          ? {
              ...group,
              filters: group.filters.map((filter) =>
                filter.id === filterId ? { ...filter, selected: !filter.selected } : filter,
              ),
            }
          : group,
      ),
    )
  }

  const clearAllFilters = () => {
    setFilterGroups((groups) =>
      groups.map((group) => ({
        ...group,
        filters: group.filters.map((filter) => ({ ...filter, selected: false })),
      })),
    )
  }

  // Count selected filters
  const selectedCount = filterGroups.reduce((count, group) => count + group.filters.filter((f) => f.selected).length, 0)

  return (
    <div className="bg-zinc-900/90 rounded-xl border border-zinc-800/20 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium">Filters</h3>
        {selectedCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            className="text-xs text-zinc-400 hover:text-white"
            onClick={clearAllFilters}
          >
            Clear All
          </Button>
        )}
      </div>

      {selectedCount > 0 && (
        <div className="mb-4">
          <div className="text-xs text-zinc-500 mb-2">Applied Filters</div>
          <div className="flex flex-wrap gap-2">
            {filterGroups.flatMap((group) =>
              group.filters
                .filter((filter) => filter.selected)
                .map((filter) => (
                  <div
                    key={filter.id}
                    className="flex items-center bg-blue-500/20 text-blue-400 rounded-full px-2 py-1 text-xs"
                  >
                    <span>{filter.label}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1 p-0 hover:bg-transparent"
                      onClick={() => toggleFilter(group.name, filter.id)}
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </Button>
                  </div>
                )),
            )}
          </div>
        </div>
      )}

      <div className="space-y-4">
        {filterGroups.map((group) => (
          <div key={group.name}>
            <div
              className="flex items-center justify-between cursor-pointer mb-2"
              onClick={() => toggleGroup(group.name)}
            >
              <div className="text-sm font-medium">{group.name}</div>
              {group.expanded ? (
                <ChevronDownIcon className="h-4 w-4 text-zinc-400" />
              ) : (
                <ChevronRightIcon className="h-4 w-4 text-zinc-400" />
              )}
            </div>

            {group.expanded && (
              <div className="space-y-2 pl-2">
                {group.filters.map((filter) => (
                  <div
                    key={filter.id}
                    className="flex items-center justify-between text-sm cursor-pointer hover:text-white"
                    onClick={() => toggleFilter(group.name, filter.id)}
                  >
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filter.selected}
                        className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                        onChange={() => toggleFilter(group.name, filter.id)}
                      />
                      <span className={filter.selected ? "text-white" : "text-zinc-400"}>{filter.label}</span>
                    </div>
                    <span className="text-xs text-zinc-500">{filter.count}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
