"use client"

import React, { useState } from "react"
import { ChevronDownIcon, ChevronRightIcon } from "@heroicons/react/16/solid"
import { Button } from "./ui/button"

export default function EventsFilters() {
  const [dateRange, setDateRange] = useState("upcoming")
  const [categories, setCategories] = useState({
    worship: false,
    youth: true,
    children: false,
    outreach: false,
    bible: false,
    prayer: false,
  })
  const [locations, setLocations] = useState({
    sanctuary: false,
    fellowship: false,
    youthCenter: true,
    classrooms: false,
    community: false,
  })
  const [expanded, setExpanded] = useState({
    date: true,
    category: true,
    location: true,
  })

  const toggleSection = (section: keyof typeof expanded) => {
    setExpanded((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  return (
    <div className="bg-zinc-900/90 rounded-xl border border-zinc-800/20 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium">Filters</h3>
        <Button variant="ghost" size="sm" className="text-xs text-zinc-400 hover:text-white">
          Clear All
        </Button>
      </div>

      <div className="space-y-4">
        {/* Date Range */}
        <div>
          <div className="flex items-center justify-between cursor-pointer mb-2" onClick={() => toggleSection("date")}>
            <div className="text-sm font-medium">Date Range</div>
            {expanded.date ? (
              <ChevronDownIcon className="h-4 w-4 text-zinc-400" />
            ) : (
              <ChevronRightIcon className="h-4 w-4 text-zinc-400" />
            )}
          </div>

          {expanded.date && (
            <div className="space-y-2 pl-2">
              <div className="flex items-center text-sm">
                <input
                  type="radio"
                  id="upcoming"
                  name="dateRange"
                  checked={dateRange === "upcoming"}
                  onChange={() => setDateRange("upcoming")}
                  className="mr-2 h-3.5 w-3.5 rounded-full border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="upcoming" className={dateRange === "upcoming" ? "text-white" : "text-zinc-400"}>
                  Upcoming
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="radio"
                  id="thisWeek"
                  name="dateRange"
                  checked={dateRange === "thisWeek"}
                  onChange={() => setDateRange("thisWeek")}
                  className="mr-2 h-3.5 w-3.5 rounded-full border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="thisWeek" className={dateRange === "thisWeek" ? "text-white" : "text-zinc-400"}>
                  This Week
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="radio"
                  id="thisMonth"
                  name="dateRange"
                  checked={dateRange === "thisMonth"}
                  onChange={() => setDateRange("thisMonth")}
                  className="mr-2 h-3.5 w-3.5 rounded-full border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="thisMonth" className={dateRange === "thisMonth" ? "text-white" : "text-zinc-400"}>
                  This Month
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="radio"
                  id="custom"
                  name="dateRange"
                  checked={dateRange === "custom"}
                  onChange={() => setDateRange("custom")}
                  className="mr-2 h-3.5 w-3.5 rounded-full border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="custom" className={dateRange === "custom" ? "text-white" : "text-zinc-400"}>
                  Custom Range
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Category */}
        <div>
          <div
            className="flex items-center justify-between cursor-pointer mb-2"
            onClick={() => toggleSection("category")}
          >
            <div className="text-sm font-medium">Category</div>
            {expanded.category ? (
              <ChevronDownIcon className="h-4 w-4 text-zinc-400" />
            ) : (
              <ChevronRightIcon className="h-4 w-4 text-zinc-400" />
            )}
          </div>

          {expanded.category && (
            <div className="space-y-2 pl-2">
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="worship"
                  checked={categories.worship}
                  onChange={() => setCategories({ ...categories, worship: !categories.worship })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="worship" className={categories.worship ? "text-white" : "text-zinc-400"}>
                  Worship Services
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="youth"
                  checked={categories.youth}
                  onChange={() => setCategories({ ...categories, youth: !categories.youth })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="youth" className={categories.youth ? "text-white" : "text-zinc-400"}>
                  Youth Events
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="children"
                  checked={categories.children}
                  onChange={() => setCategories({ ...categories, children: !categories.children })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="children" className={categories.children ? "text-white" : "text-zinc-400"}>
                  Children's Ministry
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="outreach"
                  checked={categories.outreach}
                  onChange={() => setCategories({ ...categories, outreach: !categories.outreach })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="outreach" className={categories.outreach ? "text-white" : "text-zinc-400"}>
                  Outreach & Missions
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="bible"
                  checked={categories.bible}
                  onChange={() => setCategories({ ...categories, bible: !categories.bible })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="bible" className={categories.bible ? "text-white" : "text-zinc-400"}>
                  Bible Studies
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="prayer"
                  checked={categories.prayer}
                  onChange={() => setCategories({ ...categories, prayer: !categories.prayer })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="prayer" className={categories.prayer ? "text-white" : "text-zinc-400"}>
                  Prayer Meetings
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Location */}
        <div>
          <div
            className="flex items-center justify-between cursor-pointer mb-2"
            onClick={() => toggleSection("location")}
          >
            <div className="text-sm font-medium">Location</div>
            {expanded.location ? (
              <ChevronDownIcon className="h-4 w-4 text-zinc-400" />
            ) : (
              <ChevronRightIcon className="h-4 w-4 text-zinc-400" />
            )}
          </div>

          {expanded.location && (
            <div className="space-y-2 pl-2">
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="sanctuary"
                  checked={locations.sanctuary}
                  onChange={() => setLocations({ ...locations, sanctuary: !locations.sanctuary })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="sanctuary" className={locations.sanctuary ? "text-white" : "text-zinc-400"}>
                  Main Sanctuary
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="fellowship"
                  checked={locations.fellowship}
                  onChange={() => setLocations({ ...locations, fellowship: !locations.fellowship })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="fellowship" className={locations.fellowship ? "text-white" : "text-zinc-400"}>
                  Fellowship Hall
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="youthCenter"
                  checked={locations.youthCenter}
                  onChange={() => setLocations({ ...locations, youthCenter: !locations.youthCenter })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="youthCenter" className={locations.youthCenter ? "text-white" : "text-zinc-400"}>
                  Youth Center
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="classrooms"
                  checked={locations.classrooms}
                  onChange={() => setLocations({ ...locations, classrooms: !locations.classrooms })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="classrooms" className={locations.classrooms ? "text-white" : "text-zinc-400"}>
                  Classrooms
                </label>
              </div>
              <div className="flex items-center text-sm">
                <input
                  type="checkbox"
                  id="community"
                  checked={locations.community}
                  onChange={() => setLocations({ ...locations, community: !locations.community })}
                  className="mr-2 h-3.5 w-3.5 rounded border-zinc-600 text-blue-500 focus:ring-blue-500/30"
                />
                <label htmlFor="community" className={locations.community ? "text-white" : "text-zinc-400"}>
                  Off-site Locations
                </label>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
