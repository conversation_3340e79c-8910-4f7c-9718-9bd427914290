"use client"

import { useState } from "react"
import { PaperClipIcon, PaperAirplaneIcon, CheckIcon, ClockIcon } from "@heroicons/react/24/outline"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

interface Note {
  id: string
  content: string
  timestamp: string
  type: "task" | "note"
  completed?: boolean
}

interface RightPanelProps {
  personId: string
  personName: string
}

export default function RightPanelModern({ personId, personName }: RightPanelProps) {
  const [activeTab, setActiveTab] = useState("noted")
  const [message, setMessage] = useState("")

  // TODO: This should likely come from props or a data store
  const notes: Note[] = [
    {
      id: "1",
      content: "Enroll in next estate courses to update certificates and skills",
      timestamp: "6:11 PM",
      type: "task",
      completed: true,
    },
    {
      id: "2",
      content: "Conduct a price analysis of real estate in the selected area",
      timestamp: "8:32 AM",
      type: "task",
      completed: true,
    },
    {
      id: "3",
      content: "Enhance the feedback and review system for clients",
      timestamp: "8:12 PM",
      type: "task",
      completed: false,
    },
  ]

  const documents = [
    { name: "Contract_Draft.pdf", size: "2.4 MB" },
    { name: "Property_Details.xlsx", size: "1.1 MB" },
  ]

  return (
    <div className={cn("right-panel-modern w-96 flex flex-col h-full")}>
      {/* Header */}
      <div className="p-4 border-b border-[var(--border-subtle)]">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="bg-[var(--bg-glass)] border border-[var(--border-subtle)] w-full rounded-xl">
            <TabsTrigger
              value="noted"
              className="flex-1 data-[state=active]:bg-[rgba(var(--color-primary),0.15)] data-[state=active]:text-yellow-400 rounded-lg text-zinc-400 hover:text-white"
            >
              Noted
            </TabsTrigger>
            <TabsTrigger
              value="chat"
              className="flex-1 data-[state=active]:bg-[rgba(var(--color-primary),0.15)] data-[state=active]:text-yellow-400 rounded-lg text-zinc-400 hover:text-white"
            >
              Chat
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {activeTab === "noted" && (
          <div className="p-4">
            <p className="text-sm text-zinc-500 mb-4">After our conversation, the following tasks remained:</p>

            <div className="space-y-3">
              {notes.map((note) => (
                <div key={note.id} className="card-modern p-4">
                  <div className="flex items-start gap-3">
                    <button
                      className={cn(
                        "mt-0.5 w-5 h-5 rounded-md border-2 flex items-center justify-center transition-colors",
                        note.completed
                          ? "bg-[rgba(var(--color-green),0.3)] border-[rgba(var(--color-green),0.5)]"
                          : "border-[var(--border-light)] hover:border-[var(--border-medium)]",
                      )}
                    >
                      {note.completed && <CheckIcon className="h-3 w-3 text-[var(--color-green)]" />}
                    </button>
                    <div className="flex-1">
                      <p className={cn("text-sm", note.completed ? "line-through text-zinc-500" : "text-white")}>
                        {note.content}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <ClockIcon className="h-3 w-3 text-zinc-500" />
                        <span className="text-xs text-zinc-500">{note.timestamp}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === "chat" && (
          <div className="flex flex-col h-full">
            <div className="flex-1 p-4">
              <div className="text-center text-zinc-500 text-sm">Start a conversation with {personName}</div>
            </div>

            {/* Documents */}
            {documents.length > 0 && (
              <div className="p-4 border-t border-[var(--border-subtle)]">
                <p className="text-xs text-zinc-500 mb-2">Attached Documents</p>
                <div className="space-y-2">
                  {documents.map((doc, index) => (
                    <div key={index} className="card-modern flex items-center gap-3 p-3">
                      <div className="p-2 bg-[var(--bg-glass)] rounded-lg">
                        <PaperClipIcon className="h-4 w-4 text-zinc-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm truncate text-white">{doc.name}</p>
                        <p className="text-xs text-zinc-500">{doc.size}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-[var(--border-subtle)]">
        <div className="flex items-center gap-2">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type something..."
            className="input-modern flex-1"
          />
          <Button
            size="icon"
            className="bg-[rgba(var(--color-primary),0.8)] hover:bg-[rgba(var(--color-primary),1)] text-black rounded-xl action-button-primary"
          >
            <PaperAirplaneIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
