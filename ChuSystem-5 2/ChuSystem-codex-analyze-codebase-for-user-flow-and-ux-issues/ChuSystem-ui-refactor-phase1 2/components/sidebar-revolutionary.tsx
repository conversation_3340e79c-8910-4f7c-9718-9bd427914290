"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { 
  ChartBarIcon,
  UserGroupIcon,
  CalendarDaysIcon,
  BanknotesIcon,
  TicketIcon,
  MusicalNoteIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon,
  BellIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  SparklesIcon,
  FireIcon,
  HomeIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon,
  BookOpenIcon,
  AcademicCapIcon,
  GlobeAltIcon,
  VideoCameraIcon,
  MicrophoneIcon,
  PhotoIcon,
  NewspaperIcon,
  FolderIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  CheckCircleIcon,
  XMarkIcon,
  PlusIcon
} from "@heroicons/react/24/outline"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import EnhancedProductSwitcher from "@/components/ui/enhanced-product-switcher"

interface NavItem {
  id: string
  label: string
  icon: React.ComponentType<any>
  href: string
  badge?: string | number
  badgeColor?: string
  subItems?: NavItem[]
  quickActions?: {
    icon: React.ComponentType<any>
    label: string
    action: () => void
  }[]
}

interface SidebarProps {
  className?: string
}

export default function SidebarRevolutionary({ className }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState("")
  const [showSearch, setShowSearch] = useState(false)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  // Main navigation items with enhanced structure
  const navItems: NavItem[] = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: HomeIcon,
      href: "/",
      badge: "Live",
      badgeColor: "bg-green-500"
    },
    {
      id: "services",
      label: "Services",
      icon: MusicalNoteIcon,
      href: "/services",
      badge: 3,
      quickActions: [
        { icon: PlusIcon, label: "New Service", action: () => console.log("New service") },
        { icon: CalendarDaysIcon, label: "Schedule", action: () => console.log("Schedule") }
      ],
      subItems: [
        { id: "planning", label: "Planning", icon: DocumentTextIcon, href: "/services" },
        { id: "songs", label: "Song Library", icon: MusicalNoteIcon, href: "/services/song-library" },
        { id: "teams", label: "Teams", icon: UserGroupIcon, href: "/services/teams" }
      ]
    },
    {
      id: "people",
      label: "People",
      icon: UserGroupIcon,
      href: "/people",
      badge: 847,
      subItems: [
        { id: "directory", label: "Directory", icon: BookOpenIcon, href: "/people" },
        { id: "visitors", label: "Visitors", icon: SparklesIcon, href: "/people/visitors", badge: "12" },
        { id: "households", label: "Households", icon: HomeIcon, href: "/people/households" }
      ]
    },
    {
      id: "giving",
      label: "Giving",
      icon: BanknotesIcon,
      href: "/giving",
      badge: "$12.8K",
      badgeColor: "bg-purple-500",
      quickActions: [
        { icon: PlusIcon, label: "Record Gift", action: () => console.log("Record gift") },
        { icon: DocumentTextIcon, label: "Statements", action: () => console.log("Statements") }
      ]
    },
    {
      id: "events",
      label: "Events",
      icon: CalendarDaysIcon,
      href: "/events",
      badge: 8,
      subItems: [
        { id: "calendar", label: "Calendar", icon: CalendarDaysIcon, href: "/events" },
        { id: "registrations", label: "Registrations", icon: TicketIcon, href: "/events/registrations" }
      ]
    },
    {
      id: "check-in",
      label: "Check-in",
      icon: TicketIcon,
      href: "/check-in",
      badge: "234",
      badgeColor: "bg-green-500"
    },
    {
      id: "groups",
      label: "Groups",
      icon: UserGroupIcon,
      href: "/groups",
      badge: 67,
      subItems: [
        { id: "small-groups", label: "Small Groups", icon: HomeIcon, href: "/groups/small-groups" },
        { id: "ministries", label: "Ministries", icon: HeartIcon, href: "/groups/ministries" },
        { id: "classes", label: "Classes", icon: AcademicCapIcon, href: "/groups/classes" }
      ]
    }
  ]

  // Additional tools/features
  const toolItems: NavItem[] = [
    {
      id: "media",
      label: "Media",
      icon: PhotoIcon,
      href: "/media",
      subItems: [
        { id: "library", label: "Library", icon: FolderIcon, href: "/media/library" },
        { id: "streaming", label: "Streaming", icon: VideoCameraIcon, href: "/media/streaming" }
      ]
    },
    {
      id: "communications",
      label: "Communications",
      icon: ChatBubbleLeftRightIcon,
      href: "/communications",
      badge: "3",
      badgeColor: "bg-blue-500"
    },
    {
      id: "reports",
      label: "Reports",
      icon: ChartBarIcon,
      href: "/reports"
    }
  ]

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedItems(newExpanded)
  }

  const isActive = (href: string) => pathname === href

  const filteredNavItems = navItems.filter(item => 
    item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.subItems?.some(sub => sub.label.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const filteredToolItems = toolItems.filter(item =>
    item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.subItems?.some(sub => sub.label.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  return (
    <div className={cn(
      "fixed left-0 top-0 h-screen bg-[var(--bg-glass)] backdrop-blur-xl border-r border-[var(--border-subtle)] z-40 flex flex-col transition-all duration-300",
      isCollapsed ? "w-20" : "w-80",
      className
    )}>
      {/* Header */}
      <div className="h-16 px-4 flex items-center justify-between border-b border-[var(--border-subtle)]">
        {!isCollapsed && (
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-[var(--color-primary)] to-purple-600 rounded-xl flex items-center justify-center">
              <SparklesIcon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="font-bold text-lg">ChuSystem</h1>
              <p className="text-xs text-zinc-400">Church OS</p>
            </div>
          </div>
        )}
        
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-2 hover:bg-white/10 rounded-lg transition-colors"
        >
          {isCollapsed ? <ChevronRightIcon className="h-5 w-5" /> : <ChevronLeftIcon className="h-5 w-5" />}
        </button>
      </div>

      {/* Search */}
      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: showSearch ? "auto" : 0, opacity: showSearch ? 1 : 0 }}
            exit={{ height: 0, opacity: 0 }}
            className="overflow-hidden"
          >
            <div className="p-4 border-b border-[var(--border-subtle)]">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-zinc-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search navigation..."
                  className="w-full pl-10 pr-8 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-1 hover:bg-white/10 rounded"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Search Toggle */}
      {!isCollapsed && (
        <button
          onClick={() => setShowSearch(!showSearch)}
          className="mx-4 mt-4 p-2 bg-white/5 hover:bg-white/10 rounded-lg transition-colors flex items-center justify-center gap-2"
        >
          <MagnifyingGlassIcon className="h-4 w-4" />
          <span className="text-sm">Quick Search</span>
          <kbd className="text-xs bg-white/10 px-1.5 py-0.5 rounded">⌘K</kbd>
        </button>
      )}

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto custom-scrollbar py-4">
        {/* Main Navigation */}
        <div className="px-4 mb-6">
          {!isCollapsed && <p className="text-xs text-zinc-400 uppercase tracking-wider mb-3">Main</p>}
          <nav className="space-y-1">
            {filteredNavItems.map((item) => (
              <div key={item.id}>
                <div
                  className="relative"
                  onMouseEnter={() => setHoveredItem(item.id)}
                  onMouseLeave={() => setHoveredItem(null)}
                >
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all group",
                      isActive(item.href)
                        ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]"
                        : "hover:bg-white/10"
                    )}
                  >
                    <item.icon className={cn(
                      "h-5 w-5 flex-shrink-0",
                      isActive(item.href) ? "text-[var(--color-primary)]" : "text-zinc-400 group-hover:text-white"
                    )} />
                    
                    {!isCollapsed && (
                      <>
                        <span className="flex-1 font-medium">{item.label}</span>
                        {item.badge && (
                          <Badge
                            variant="secondary"
                            size="sm"
                            className={cn(
                              "min-w-[2rem] justify-center",
                              item.badgeColor || "bg-white/10"
                            )}
                          >
                            {item.badge}
                          </Badge>
                        )}
                        {item.subItems && (
                          <button
                            onClick={(e) => {
                              e.preventDefault()
                              toggleExpanded(item.id)
                            }}
                            className="p-1 hover:bg-white/10 rounded"
                          >
                            <ChevronRightIcon
                              className={cn(
                                "h-4 w-4 transition-transform",
                                expandedItems.has(item.id) && "rotate-90"
                              )}
                            />
                          </button>
                        )}
                      </>
                    )}
                  </Link>

                  {/* Quick Actions on Hover */}
                  {!isCollapsed && item.quickActions && hoveredItem === item.id && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="absolute right-0 top-1/2 -translate-y-1/2 flex gap-1 bg-[var(--bg-glass)] p-1 rounded-lg shadow-glow"
                    >
                      {item.quickActions.map((action, idx) => (
                        <button
                          key={idx}
                          onClick={action.action}
                          className="p-1.5 hover:bg-white/10 rounded transition-colors"
                          title={action.label}
                        >
                          <action.icon className="h-4 w-4" />
                        </button>
                      ))}
                    </motion.div>
                  )}
                </div>

                {/* Sub Items */}
                {!isCollapsed && item.subItems && (
                  <AnimatePresence>
                    {expandedItems.has(item.id) && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="ml-4 mt-1 space-y-1 overflow-hidden"
                      >
                        {item.subItems.map((subItem) => (
                          <Link
                            key={subItem.id}
                            href={subItem.href}
                            className={cn(
                              "flex items-center gap-3 px-3 py-2 rounded-lg transition-all text-sm",
                              isActive(subItem.href)
                                ? "bg-[var(--color-primary)]/10 text-[var(--color-primary)]"
                                : "hover:bg-white/5 text-zinc-400 hover:text-white"
                            )}
                          >
                            <subItem.icon className="h-4 w-4" />
                            <span className="flex-1">{subItem.label}</span>
                            {subItem.badge && (
                              <Badge variant="secondary" size="sm">
                                {subItem.badge}
                              </Badge>
                            )}
                          </Link>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* Tools */}
        <div className="px-4 mb-6">
          {!isCollapsed && <p className="text-xs text-zinc-400 uppercase tracking-wider mb-3">Tools</p>}
          <nav className="space-y-1">
            {filteredToolItems.map((item) => (
              <div key={item.id}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all group",
                    isActive(item.href)
                      ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]"
                      : "hover:bg-white/10"
                  )}
                >
                  <item.icon className={cn(
                    "h-5 w-5 flex-shrink-0",
                    isActive(item.href) ? "text-[var(--color-primary)]" : "text-zinc-400 group-hover:text-white"
                  )} />
                  
                  {!isCollapsed && (
                    <>
                      <span className="flex-1 font-medium">{item.label}</span>
                      {item.badge && (
                        <Badge
                          variant="secondary"
                          size="sm"
                          className={cn(
                            "min-w-[2rem] justify-center",
                            item.badgeColor || "bg-white/10"
                          )}
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </>
                  )}
                </Link>
              </div>
            ))}
          </nav>
        </div>

        {/* Live Activity Indicator */}
        {!isCollapsed && (
          <div className="px-4 mb-6">
            <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-green-400">Live Activity</span>
              </div>
              <div className="space-y-1 text-xs">
                <div className="flex items-center justify-between">
                  <span className="text-zinc-400">Check-ins</span>
                  <span className="text-green-400">234 active</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-zinc-400">Service</span>
                  <span className="text-green-400">In progress</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Section */}
      <div className="border-t border-[var(--border-subtle)]">
        {/* Settings & Help */}
        <div className="p-4 space-y-1">
          <Link
            href="/settings"
            className="flex items-center gap-3 px-3 py-2.5 rounded-lg hover:bg-white/10 transition-all"
          >
            <Cog6ToothIcon className="h-5 w-5 text-zinc-400" />
            {!isCollapsed && <span>Settings</span>}
          </Link>
          <Link
            href="/help"
            className="flex items-center gap-3 px-3 py-2.5 rounded-lg hover:bg-white/10 transition-all"
          >
            <QuestionMarkCircleIcon className="h-5 w-5 text-zinc-400" />
            {!isCollapsed && <span>Help & Support</span>}
          </Link>
        </div>

        {/* Product Switcher */}
        {!isCollapsed && (
          <div className="p-4 pt-0">
            <EnhancedProductSwitcher />
          </div>
        )}

        {/* User Profile */}
        {!isCollapsed && (
          <div className="p-4 border-t border-[var(--border-subtle)]">
            <div className="flex items-center gap-3">
              <Image
                src="/placeholder-user.jpg"
                alt="User"
                width={40}
                height={40}
                className="rounded-full"
              />
              <div className="flex-1">
                <p className="text-sm font-medium">Admin User</p>
                <p className="text-xs text-zinc-400"><EMAIL></p>
              </div>
              <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
                <BellIcon className="h-5 w-5 text-zinc-400" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}