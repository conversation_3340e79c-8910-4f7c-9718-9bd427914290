import { Search } from "lucide-react"

interface SearchBarProps {
  placeholder?: string
  className?: string
}

export default function SearchBar({ placeholder = "Search...", className = "" }: SearchBarProps) {
  return (
    <div className={`relative ${className}`}>
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-zinc-500" />
      <input
        type="text"
        placeholder={placeholder}
        className="pl-10 pr-4 py-2 bg-zinc-900/80 border border-zinc-800/60 rounded-xl text-sm w-[300px] 
focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/30 focus:bg-zinc-900
transition-all placeholder:text-zinc-500"
      />
    </div>
  )
}
