"use client"

import type { ReactNode } from "react"
import { cn } from "@/lib/utils"
import { Squares2X2Icon, UserGroupIcon, CalendarIcon, ChartBarIcon, HomeIcon } from "@heroicons/react/16/solid"

type BadgeType = {
  type: "count" | "text" | "dot"
  value?: string
  color?: string
}

interface SidebarItemProps {
  icon: string | ReactNode
  label: string
  badge?: BadgeType
  active?: boolean
  onClick?: () => void
  className?: string
  textColor?: string
  iconColor?: string
}

export default function SidebarItem({
  icon,
  label,
  badge,
  active = false,
  onClick,
  className = "",
  textColor = "",
  iconColor = "text-zinc-400",
}: SidebarItemProps) {
  // Render the appropriate icon
  const renderIcon = () => {
    if (typeof icon === "string") {
      switch (icon) {
        case "grid":
          return <Squares2X2Icon className={`h-4 w-4 ${iconColor}`} />
        case "users":
          return <UserGroupIcon className={`h-4 w-4 ${iconColor}`} />
        case "calendar":
          return <CalendarIcon className={`h-4 w-4 ${iconColor}`} />
        case "chart":
          return <ChartBarIcon className={`h-4 w-4 ${iconColor}`} />
        case "home":
          return <HomeIcon className={`h-4 w-4 ${iconColor}`} />
        default:
          return <div className="w-4 h-4" />
      }
    } else {
      return icon
    }
  }

  // Render the appropriate badge
  const renderBadge = () => {
    if (!badge) return null

    switch (badge.type) {
      case "count":
        return <span className={`badge-circle ${badge.color || ""}`}>{badge.value}</span>
      case "text":
        const bgColor = badge.color || "bg-zinc-800/80"
        return <span className={`badge ${bgColor}`}>{badge.value}</span>
      case "dot":
        const dotColor = badge.color || "bg-red-500"
        return <span className={`${dotColor} w-2 h-2 rounded-full`}></span>
      default:
        return null
    }
  }

  return (
    <div
      className={cn(
        "flex items-center justify-between px-3 py-2 text-sm rounded-xl hover:bg-zinc-800/60 cursor-pointer transition-all data-hover:bg-zinc-800/80",
        active ? "bg-zinc-800/80" : "",
        className,
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-3">
        {renderIcon()}
        <span className={textColor}>{label}</span>
      </div>
      {renderBadge()}
    </div>
  )
}
