import { ChatBubbleLeftIcon, CheckIcon } from "@heroicons/react/16/solid"
import TeamAvatars from "./team-avatars"
import Card from "./design-system/card"
import { Badge } from "./design-system/badge"

interface ProjectCardProps {
  title: string
  status: string
  note: string
  avatars: string[]
  stats: {
    comments: number
    tasks: number
    activity: number
  }
  chartColor: "blue" | "white"
  badgeText: string
  badgeColor: "blue" | "orange" | "purple" | "green"
  className?: string
}

export default function ProjectCard({
  title,
  status,
  note,
  avatars,
  stats,
  chartColor,
  badgeText,
  badgeColor,
  className,
}: ProjectCardProps) {
  // Chart line color
  const lineColor = chartColor === "blue" ? "#3b82f6" : "#ffffff"
  const gradientId = `gradient-${title.replace(/\s+/g, "-").toLowerCase()}`

  return (
    <Card className={className}>
      <div className="mb-5">
        <div className="flex items-center justify-between mb-2">
          <Badge color={badgeColor as any}>{badgeText}</Badge>
          <div className="text-xs text-zinc-400">{status}</div>
        </div>

        <div className="h-20 flex items-center justify-center mb-2">
          {chartColor === "blue" ? (
            <svg width="140" height="40" viewBox="0 0 140 40" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.7" />
                  <stop offset="100%" stopColor="#3b82f6" stopOpacity="1" />
                </linearGradient>
              </defs>
              <path d="M0,30 Q30,10 70,25 T140,20" fill="none" stroke={`url(#${gradientId})`} strokeWidth="2" />
            </svg>
          ) : (
            <svg width="140" height="40" viewBox="0 0 140 40" xmlns="http://www.w3.org/2000/svg">
              <path d="M0,20 L30,25 L60,15 L90,30 L140,20" fill="none" stroke={lineColor} strokeWidth="2" />
            </svg>
          )}
        </div>
      </div>

      <h3 className="text-base font-semibold mb-1">{title}</h3>
      <p className="text-xs text-zinc-400 mb-3">{note}</p>

      <TeamAvatars avatars={avatars} size="sm" />

      <div className="flex items-center justify-between mt-auto pt-4 text-xs text-zinc-400">
        <div className="flex items-center gap-1 hover:text-zinc-300 transition-colors">
          <ChatBubbleLeftIcon className="h-3.5 w-3.5 text-zinc-400" />
          <span>{stats.comments}</span>
        </div>

        <div className="flex items-center gap-1 hover:text-zinc-300 transition-colors">
          <CheckIcon className="h-3.5 w-3.5 text-zinc-400" />
          <span>{stats.tasks}</span>
        </div>

        <div className="flex items-center gap-1 hover:text-zinc-300 transition-colors">
          <div
            className={`w-3 h-3 rounded-full ${chartColor === "blue" ? "bg-blue-500 shadow-glow-blue-sm" : "bg-white"}`}
          ></div>
          <span>{stats.activity}</span>
        </div>
      </div>
    </Card>
  )
}
