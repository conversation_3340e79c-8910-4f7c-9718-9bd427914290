"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarModern from "@/components/sidebar-modern"
import { 
  CalendarIcon,
  CalendarDaysIcon,
  ListBulletIcon,
  MapIcon,
  FunnelIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  UsersIcon,
  MapPinIcon,
  TicketIcon,
  BellIcon,
  ShareIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface EventsLayoutProps {
  children: React.ReactNode
  currentView?: "month" | "week" | "day" | "list" | "map"
  onViewChange?: (view: string) => void
  currentDate?: Date
  onDateChange?: (date: Date) => void
  upcomingEvents?: {
    today: number
    thisWeek: number
    thisMonth: number
  }
  eventCategories?: Array<{
    name: string
    color: string
    count: number
  }>
}

export default function EventsLayout({
  children,
  currentView = "month",
  onViewChange,
  currentDate = new Date(),
  onDateChange,
  upcomingEvents,
  eventCategories
}: EventsLayoutProps) {
  const [miniCalendarOpen, setMiniCalendarOpen] = useState(true)
  const [filterPanelOpen, setFilterPanelOpen] = useState(false)

  const views = [
    { id: "month", icon: CalendarDaysIcon, label: "Month" },
    { id: "week", icon: CalendarIcon, label: "Week" },
    { id: "day", icon: CalendarIcon, label: "Day" },
    { id: "list", icon: ListBulletIcon, label: "List" },
    { id: "map", icon: MapIcon, label: "Map" }
  ]

  const formatMonthYear = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
  }

  const goToPreviousMonth = () => {
    const newDate = new Date(currentDate)
    newDate.setMonth(newDate.getMonth() - 1)
    onDateChange?.(newDate)
  }

  const goToNextMonth = () => {
    const newDate = new Date(currentDate)
    newDate.setMonth(newDate.getMonth() + 1)
    onDateChange?.(newDate)
  }

  const goToToday = () => {
    onDateChange?.(new Date())
  }

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarModern />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Bar */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)]">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Date Navigation */}
              <div className="flex items-center gap-4">
                <h1 className="text-2xl font-bold">{formatMonthYear(currentDate)}</h1>
                <div className="flex items-center gap-1">
                  <button
                    onClick={goToPreviousMonth}
                    className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                  >
                    <ChevronLeftIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={goToToday}
                    className="px-3 py-1 hover:bg-white/10 rounded-lg transition-colors text-sm"
                  >
                    Today
                  </button>
                  <button
                    onClick={goToNextMonth}
                    className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                  >
                    <ChevronRightIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                {/* Search */}
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-zinc-400" />
                  <input
                    type="text"
                    placeholder="Search events..."
                    className="pl-10 pr-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm w-64 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                  />
                </div>

                {/* View Switcher */}
                <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                  {views.map((view) => (
                    <button
                      key={view.id}
                      onClick={() => onViewChange?.(view.id)}
                      className={cn(
                        "p-2 rounded transition-colors",
                        currentView === view.id
                          ? "bg-[var(--color-primary)] text-black"
                          : "hover:bg-white/10"
                      )}
                      title={view.label}
                    >
                      <view.icon className="h-4 w-4" />
                    </button>
                  ))}
                </div>

                {/* Actions */}
                <button
                  onClick={() => setFilterPanelOpen(!filterPanelOpen)}
                  className={cn(
                    "p-2 rounded-lg transition-colors",
                    filterPanelOpen ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]" : "hover:bg-white/10"
                  )}
                >
                  <FunnelIcon className="h-5 w-5" />
                </button>
                <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                  New Event
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Stats Bar */}
          {upcomingEvents && (
            <div className="px-6 pb-4">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                  <span className="text-sm">
                    <span className="font-medium">{upcomingEvents.today}</span>
                    <span className="text-zinc-400"> today</span>
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-amber-500 rounded-full" />
                  <span className="text-sm">
                    <span className="font-medium">{upcomingEvents.thisWeek}</span>
                    <span className="text-zinc-400"> this week</span>
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <span className="text-sm">
                    <span className="font-medium">{upcomingEvents.thisMonth}</span>
                    <span className="text-zinc-400"> this month</span>
                  </span>
                </div>
                <div className="h-4 w-px bg-[var(--border-subtle)]" />
                <div className="flex items-center gap-2">
                  {eventCategories?.map((category) => (
                    <Badge
                      key={category.name}
                      variant="secondary"
                      size="sm"
                      style={{ backgroundColor: `${category.color}20`, color: category.color }}
                    >
                      {category.name} ({category.count})
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Mini Calendar & Filters */}
          <AnimatePresence>
            {(miniCalendarOpen || filterPanelOpen) && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 320, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="border-r border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl overflow-hidden"
              >
                <div className="w-80 h-full overflow-y-auto custom-scrollbar">
                  {/* Mini Calendar */}
                  <div className="p-4 border-b border-[var(--border-subtle)]">
                    <h3 className="text-sm font-medium mb-3">Calendar</h3>
                    <div className="bg-white/5 rounded-lg p-3">
                      {/* Mini calendar implementation */}
                      <div className="grid grid-cols-7 gap-1 text-center">
                        {["S", "M", "T", "W", "T", "F", "S"].map((day) => (
                          <div key={day} className="text-xs text-zinc-400 py-1">
                            {day}
                          </div>
                        ))}
                        {/* Calendar days */}
                        {Array.from({ length: 35 }, (_, i) => {
                          const day = i - 2 // Adjust for start day
                          const isCurrentMonth = day > 0 && day <= 31
                          const isToday = day === new Date().getDate()
                          
                          return (
                            <button
                              key={i}
                              className={cn(
                                "aspect-square rounded flex items-center justify-center text-sm transition-colors",
                                isCurrentMonth ? "hover:bg-white/10" : "text-zinc-600",
                                isToday && "bg-[var(--color-primary)] text-black font-medium"
                              )}
                            >
                              {isCurrentMonth ? day : ""}
                            </button>
                          )
                        })}
                      </div>
                    </div>
                  </div>

                  {/* Filters */}
                  {filterPanelOpen && (
                    <div className="p-4">
                      <h3 className="text-sm font-medium mb-3">Filters</h3>
                      
                      {/* Event Type */}
                      <div className="mb-4">
                        <p className="text-xs text-zinc-400 mb-2">Event Type</p>
                        <div className="space-y-2">
                          {["Service", "Meeting", "Social", "Ministry", "Youth", "Training"].map((type) => (
                            <label key={type} className="flex items-center gap-2">
                              <input type="checkbox" className="rounded" defaultChecked />
                              <span className="text-sm">{type}</span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Location */}
                      <div className="mb-4">
                        <p className="text-xs text-zinc-400 mb-2">Location</p>
                        <div className="space-y-2">
                          {["Main Campus", "Youth Center", "Online", "Off-site"].map((location) => (
                            <label key={location} className="flex items-center gap-2">
                              <input type="checkbox" className="rounded" defaultChecked />
                              <span className="text-sm">{location}</span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Registration Status */}
                      <div className="mb-4">
                        <p className="text-xs text-zinc-400 mb-2">Registration</p>
                        <div className="space-y-2">
                          {["Open", "Closed", "Waitlist", "No Registration"].map((status) => (
                            <label key={status} className="flex items-center gap-2">
                              <input type="checkbox" className="rounded" defaultChecked />
                              <span className="text-sm">{status}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Upcoming Events List */}
                  <div className="p-4 border-t border-[var(--border-subtle)]">
                    <h3 className="text-sm font-medium mb-3">Upcoming Events</h3>
                    <div className="space-y-2">
                      {[
                        { title: "Sunday Service", time: "10:00 AM", type: "service", attendees: 450 },
                        { title: "Youth Group", time: "6:00 PM", type: "youth", attendees: 75 },
                        { title: "Bible Study", time: "Wednesday 7:00 PM", type: "study", attendees: 30 },
                        { title: "Men's Breakfast", time: "Saturday 8:00 AM", type: "social", attendees: 40 }
                      ].map((event, idx) => (
                        <div
                          key={idx}
                          className="p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                        >
                          <div className="flex items-start justify-between">
                            <div>
                              <p className="text-sm font-medium">{event.title}</p>
                              <p className="text-xs text-zinc-400">{event.time}</p>
                            </div>
                            <Badge variant="secondary" size="sm">
                              {event.attendees}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Calendar/Content Area */}
          <div className="flex-1 overflow-hidden bg-black/50">
            {children}
          </div>

          {/* Right Panel - Event Details (when selected) */}
          {/* This would be conditionally rendered based on selected event */}
        </div>
      </div>
    </div>
  )
}

export { EventsLayout }