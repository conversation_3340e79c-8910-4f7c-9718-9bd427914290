"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarModern from "@/components/sidebar-modern"
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  UserGroupIcon,
  MapPinIcon,
  CalendarIcon,
  ChartBarIcon,
  BellIcon,
  EnvelopeIcon,
  PhoneIcon,
  TagIcon,
  ViewColumnsIcon,
  Squares2X2Icon,
  ListBulletIcon,
  MapIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface PeopleLayoutProps {
  children: React.ReactNode
  viewMode?: "grid" | "list" | "map" | "timeline"
  onViewModeChange?: (mode: string) => void
  selectedCount?: number
  totalCount?: number
  filters?: {
    groups?: string[]
    tags?: string[]
    status?: string[]
    dateRange?: { start: Date; end: Date }
  }
  onFilterChange?: (filters: any) => void
  quickStats?: {
    newThisMonth?: number
    activeMembers?: number
    visitors?: number
    volunteers?: number
  }
}

export default function PeopleLayout({
  children,
  viewMode = "grid",
  onViewModeChange,
  selectedCount = 0,
  totalCount = 0,
  filters = {},
  onFilterChange,
  quickStats
}: PeopleLayoutProps) {
  const [filterPanelOpen, setFilterPanelOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  const viewModes = [
    { id: "grid", icon: Squares2X2Icon, label: "Grid View" },
    { id: "list", icon: ListBulletIcon, label: "List View" },
    { id: "map", icon: MapIcon, label: "Map View" },
    { id: "timeline", icon: CalendarIcon, label: "Timeline" }
  ]

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarModern />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Bar with Integrated Search and Actions */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)]">
          {/* Primary Bar */}
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Search Section */}
              <div className="flex-1 max-w-2xl">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-zinc-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search by name, email, phone, or tags..."
                    className="w-full pl-12 pr-4 py-3 bg-white/5 border border-[var(--border-subtle)] rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20 focus:border-transparent"
                  />
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
                    <button
                      onClick={() => setFilterPanelOpen(!filterPanelOpen)}
                      className={cn(
                        "p-2 rounded-lg transition-colors",
                        filterPanelOpen ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]" : "hover:bg-white/10"
                      )}
                    >
                      <FunnelIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* View Mode Switcher */}
              <div className="flex items-center gap-1 ml-4">
                {viewModes.map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => onViewModeChange?.(mode.id)}
                    className={cn(
                      "p-2 rounded-lg transition-colors",
                      viewMode === mode.id
                        ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]"
                        : "hover:bg-white/10"
                    )}
                    title={mode.label}
                  >
                    <mode.icon className="h-5 w-5" />
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Secondary Bar - Quick Stats and Actions */}
          <div className="px-6 pb-4">
            <div className="flex items-center justify-between">
              {/* Quick Stats */}
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <UserGroupIcon className="h-4 w-4 text-zinc-400" />
                  <span className="text-sm">
                    <span className="font-medium">{totalCount}</span>
                    <span className="text-zinc-400"> total</span>
                  </span>
                </div>
                {selectedCount > 0 && (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{selectedCount} selected</Badge>
                  </div>
                )}
                {quickStats && (
                  <>
                    <div className="h-4 w-px bg-[var(--border-subtle)]" />
                    <div className="flex items-center gap-4 text-sm">
                      <span>
                        <span className="font-medium text-green-400">+{quickStats.newThisMonth}</span>
                        <span className="text-zinc-400"> new</span>
                      </span>
                      <span>
                        <span className="font-medium">{quickStats.activeMembers}</span>
                        <span className="text-zinc-400"> active</span>
                      </span>
                      <span>
                        <span className="font-medium">{quickStats.visitors}</span>
                        <span className="text-zinc-400"> visitors</span>
                      </span>
                    </div>
                  </>
                )}
              </div>

              {/* Quick Actions */}
              <div className="flex items-center gap-2">
                {selectedCount > 0 ? (
                  <>
                    <Button variant="glass" size="sm" leftIcon={<EnvelopeIcon className="h-4 w-4" />}>
                      Email {selectedCount}
                    </Button>
                    <Button variant="glass" size="sm" leftIcon={<PhoneIcon className="h-4 w-4" />}>
                      SMS {selectedCount}
                    </Button>
                    <Button variant="glass" size="sm" leftIcon={<TagIcon className="h-4 w-4" />}>
                      Tag {selectedCount}
                    </Button>
                    <Button variant="glass" size="sm" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
                      Add to Group
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="glass" size="sm" leftIcon={<ChartBarIcon className="h-4 w-4" />}>
                      Reports
                    </Button>
                    <Button variant="glass" size="sm" leftIcon={<BellIcon className="h-4 w-4" />}>
                      Notifications
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Filter Tags */}
          {Object.keys(filters).length > 0 && (
            <div className="px-6 pb-3 flex items-center gap-2 border-t border-[var(--border-subtle)] pt-3">
              <span className="text-xs text-zinc-400">Active filters:</span>
              {filters.groups?.map((group) => (
                <Badge key={group} variant="secondary" size="sm">
                  {group}
                  <button className="ml-1 hover:text-red-400">×</button>
                </Badge>
              ))}
              {filters.tags?.map((tag) => (
                <Badge key={tag} variant="secondary" size="sm">
                  {tag}
                  <button className="ml-1 hover:text-red-400">×</button>
                </Badge>
              ))}
              <button className="text-xs text-zinc-400 hover:text-white ml-2">
                Clear all
              </button>
            </div>
          )}
        </div>

        {/* Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Filter Panel */}
          <AnimatePresence>
            {filterPanelOpen && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 280, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="border-r border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl overflow-hidden"
              >
                <div className="w-[280px] h-full overflow-y-auto custom-scrollbar p-4">
                  <h3 className="text-sm font-medium mb-4">Filters</h3>
                  
                  {/* Status Filter */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Status</p>
                    <div className="space-y-2">
                      {["Active", "Inactive", "Visitor", "New Member"].map((status) => (
                        <label key={status} className="flex items-center gap-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-sm">{status}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Groups Filter */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Groups</p>
                    <div className="space-y-2">
                      {["Youth", "Adults", "Seniors", "Small Groups", "Volunteers"].map((group) => (
                        <label key={group} className="flex items-center gap-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-sm">{group}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Tags Filter */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Tags</p>
                    <div className="flex flex-wrap gap-1">
                      {["baptized", "volunteer", "donor", "leader", "new", "visitor"].map((tag) => (
                        <button
                          key={tag}
                          className="px-2 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs transition-colors"
                        >
                          {tag}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Date Range */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Date Added</p>
                    <select className="w-full px-3 py-2 bg-white/10 border border-[var(--border-subtle)] rounded-lg text-sm">
                      <option>All time</option>
                      <option>Last 30 days</option>
                      <option>Last 90 days</option>
                      <option>Last year</option>
                      <option>Custom range</option>
                    </select>
                  </div>

                  {/* Location Filter */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Location</p>
                    <div className="relative">
                      <MapPinIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-zinc-400" />
                      <input
                        type="text"
                        placeholder="City or ZIP"
                        className="w-full pl-10 pr-3 py-2 bg-white/10 border border-[var(--border-subtle)] rounded-lg text-sm"
                      />
                    </div>
                  </div>

                  <Button variant="primary" size="sm" className="w-full">
                    Apply Filters
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto custom-scrollbar bg-black/50">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export { PeopleLayout }