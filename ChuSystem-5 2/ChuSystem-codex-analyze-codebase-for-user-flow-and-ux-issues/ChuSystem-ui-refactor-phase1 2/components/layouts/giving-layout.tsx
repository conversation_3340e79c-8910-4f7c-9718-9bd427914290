"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarModern from "@/components/sidebar-modern"
import { 
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  BanknotesIcon,
  CreditCardIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  EnvelopeIcon,
  PrinterIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface GivingLayoutProps {
  children: React.ReactNode
  currentPeriod?: "week" | "month" | "quarter" | "year"
  onPeriodChange?: (period: string) => void
  stats?: {
    totalGiving: number
    totalGivers: number
    averageGift: number
    recurringGivers: number
    trends: {
      giving: "up" | "down" | "stable"
      percentage: number
    }
  }
  recentTransactions?: Array<{
    id: string
    donor: string
    amount: number
    date: string
    method: string
    fund: string
  }>
}

export default function GivingLayout({
  children,
  currentPeriod = "month",
  onPeriodChange,
  stats,
  recentTransactions
}: GivingLayoutProps) {
  const [dashboardView, setDashboardView] = useState<"overview" | "donors" | "funds" | "reports">("overview")
  const [filterOpen, setFilterOpen] = useState(false)

  const periods = [
    { id: "week", label: "This Week" },
    { id: "month", label: "This Month" },
    { id: "quarter", label: "This Quarter" },
    { id: "year", label: "This Year" }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case "online":
        return <ComputerDesktopIcon className="h-4 w-4" />
      case "mobile":
        return <DevicePhoneMobileIcon className="h-4 w-4" />
      case "check":
        return <DocumentTextIcon className="h-4 w-4" />
      case "cash":
        return <BanknotesIcon className="h-4 w-4" />
      case "card":
        return <CreditCardIcon className="h-4 w-4" />
      default:
        return <CurrencyDollarIcon className="h-4 w-4" />
    }
  }

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarModern />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Bar */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)]">
          {/* Primary Bar */}
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <h1 className="text-2xl font-bold">Giving Dashboard</h1>
                
                {/* Period Selector */}
                <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                  {periods.map((period) => (
                    <button
                      key={period.id}
                      onClick={() => onPeriodChange?.(period.id)}
                      className={cn(
                        "px-3 py-1.5 rounded text-sm transition-colors",
                        currentPeriod === period.id
                          ? "bg-[var(--color-primary)] text-black font-medium"
                          : "hover:bg-white/10"
                      )}
                    >
                      {period.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-zinc-400" />
                  <input
                    type="text"
                    placeholder="Search donors or transactions..."
                    className="pl-10 pr-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm w-64 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                  />
                </div>
                <Button variant="glass" size="sm" leftIcon={<ArrowDownTrayIcon className="h-4 w-4" />}>
                  Export
                </Button>
                <Button variant="glass" size="sm" leftIcon={<PrinterIcon className="h-4 w-4" />}>
                  Statements
                </Button>
                <Button variant="primary" size="sm" leftIcon={<CurrencyDollarIcon className="h-4 w-4" />}>
                  Record Donation
                </Button>
              </div>
            </div>
          </div>

          {/* Stats Bar */}
          {stats && (
            <div className="px-6 pb-4 border-t border-[var(--border-subtle)] pt-4">
              <div className="grid grid-cols-4 gap-6">
                {/* Total Giving */}
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-zinc-400">Total Giving</p>
                    <p className="text-2xl font-bold">{formatCurrency(stats.totalGiving)}</p>
                    <div className="flex items-center gap-1 mt-1">
                      {stats.trends.giving === "up" ? (
                        <ArrowTrendingUpIcon className="h-4 w-4 text-green-400" />
                      ) : (
                        <ArrowTrendingDownIcon className="h-4 w-4 text-red-400" />
                      )}
                      <span className={cn(
                        "text-xs font-medium",
                        stats.trends.giving === "up" ? "text-green-400" : "text-red-400"
                      )}>
                        {stats.trends.percentage}%
                      </span>
                      <span className="text-xs text-zinc-400">vs last period</span>
                    </div>
                  </div>
                </div>

                {/* Total Givers */}
                <div>
                  <p className="text-xs text-zinc-400">Total Givers</p>
                  <p className="text-2xl font-bold">{stats.totalGivers}</p>
                  <p className="text-xs text-zinc-400 mt-1">unique donors</p>
                </div>

                {/* Average Gift */}
                <div>
                  <p className="text-xs text-zinc-400">Average Gift</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.averageGift)}</p>
                  <p className="text-xs text-zinc-400 mt-1">per donation</p>
                </div>

                {/* Recurring Givers */}
                <div>
                  <p className="text-xs text-zinc-400">Recurring Givers</p>
                  <p className="text-2xl font-bold">{stats.recurringGivers}</p>
                  <p className="text-xs text-green-400 mt-1">active subscriptions</p>
                </div>
              </div>
            </div>
          )}

          {/* View Tabs */}
          <div className="px-6">
            <div className="flex items-center gap-1">
              {[
                { id: "overview", label: "Overview", icon: ChartBarIcon },
                { id: "donors", label: "Donors", icon: UserGroupIcon },
                { id: "funds", label: "Funds", icon: CurrencyDollarIcon },
                { id: "reports", label: "Reports", icon: DocumentTextIcon }
              ].map((view) => (
                <button
                  key={view.id}
                  onClick={() => setDashboardView(view.id as any)}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 border-b-2 transition-colors",
                    dashboardView === view.id
                      ? "border-[var(--color-primary)] text-white"
                      : "border-transparent text-zinc-400 hover:text-white"
                  )}
                >
                  <view.icon className="h-4 w-4" />
                  <span className="text-sm font-medium">{view.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Recent Transactions */}
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 320, opacity: 1 }}
            className="border-r border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl"
          >
            <div className="w-80 h-full overflow-y-auto custom-scrollbar">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium">Recent Transactions</h3>
                  <button
                    onClick={() => setFilterOpen(!filterOpen)}
                    className="p-1 hover:bg-white/10 rounded transition-colors"
                  >
                    <FunnelIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Quick Filters */}
                {filterOpen && (
                  <div className="mb-4 p-3 bg-white/5 rounded-lg space-y-2">
                    <select className="w-full px-3 py-2 bg-white/10 border border-[var(--border-subtle)] rounded text-sm">
                      <option>All Methods</option>
                      <option>Online</option>
                      <option>Check</option>
                      <option>Cash</option>
                    </select>
                    <select className="w-full px-3 py-2 bg-white/10 border border-[var(--border-subtle)] rounded text-sm">
                      <option>All Funds</option>
                      <option>General</option>
                      <option>Missions</option>
                      <option>Building</option>
                    </select>
                  </div>
                )}

                {/* Transaction List */}
                <div className="space-y-2">
                  {recentTransactions?.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <p className="text-sm font-medium">{transaction.donor}</p>
                          <p className="text-xs text-zinc-400">{transaction.date}</p>
                        </div>
                        <p className="text-sm font-bold text-green-400">
                          {formatCurrency(transaction.amount)}
                        </p>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getMethodIcon(transaction.method)}
                          <span className="text-xs text-zinc-400">{transaction.method}</span>
                        </div>
                        <Badge variant="secondary" size="sm">
                          {transaction.fund}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Load More */}
                <button className="w-full mt-4 p-2 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-sm">
                  Load More Transactions
                </button>
              </div>

              {/* Quick Stats */}
              <div className="p-4 border-t border-[var(--border-subtle)]">
                <h3 className="text-sm font-medium mb-3">Today's Summary</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-zinc-400">Online Giving</span>
                    <span className="text-sm font-medium">$3,450</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-zinc-400">Check/Cash</span>
                    <span className="text-sm font-medium">$1,200</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-zinc-400">Total Donations</span>
                    <span className="text-sm font-medium">23</span>
                  </div>
                  <div className="pt-3 border-t border-[var(--border-subtle)]">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Today's Total</span>
                      <span className="text-lg font-bold text-green-400">$4,650</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto custom-scrollbar bg-black/50">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export { GivingLayout }