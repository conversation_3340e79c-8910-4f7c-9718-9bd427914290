"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarModern from "@/components/sidebar-modern"
import { 
  MusicalNoteIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  SpeakerWaveIcon,
  ViewColumnsIcon,
  AdjustmentsHorizontalIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  MagnifyingGlassIcon,
  Bars3Icon
} from "@heroicons/react/24/outline"

interface MusicLayoutProps {
  children: React.ReactNode
  currentSong?: {
    title: string
    artist: string
    duration: string
    currentTime: string
  }
  onPlayPause?: () => void
  isPlaying?: boolean
  showMusicControls?: boolean
  leftPanel?: React.ReactNode
  rightPanel?: React.ReactNode
}

export default function MusicLayout({
  children,
  currentSong,
  onPlayPause,
  isPlaying = false,
  showMusicControls = true,
  leftPanel,
  rightPanel
}: MusicLayoutProps) {
  const [leftPanelOpen, setLeftPanelOpen] = useState(true)
  const [rightPanelOpen, setRightPanelOpen] = useState(true)
  const [volume, setVolume] = useState(75)
  const [repeat, setRepeat] = useState(false)

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarModern />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Bar - Minimal and Elegant */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] flex items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setLeftPanelOpen(!leftPanelOpen)}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <Bars3Icon className={cn(
                "h-5 w-5 transition-transform",
                !leftPanelOpen && "rotate-180"
              )} />
            </button>
            
            {/* Search */}
            <div className="relative w-96">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-zinc-400" />
              <input
                type="text"
                placeholder="Search songs, artists, or lyrics..."
                className="w-full pl-10 pr-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20 focus:border-transparent"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
              <ViewColumnsIcon className="h-5 w-5" />
            </button>
            <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
              <AdjustmentsHorizontalIcon className="h-5 w-5" />
            </button>
            <button
              onClick={() => setRightPanelOpen(!rightPanelOpen)}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <Bars3Icon className={cn(
                "h-5 w-5 transition-transform",
                rightPanelOpen && "rotate-180"
              )} />
            </button>
          </div>
        </div>

        {/* Content Area with Panels */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel */}
          <AnimatePresence>
            {leftPanelOpen && leftPanel && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 320, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="border-r border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl overflow-hidden"
              >
                <div className="w-80 h-full overflow-y-auto custom-scrollbar p-4">
                  {leftPanel}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto custom-scrollbar bg-black/50">
            {children}
          </div>

          {/* Right Panel */}
          <AnimatePresence>
            {rightPanelOpen && rightPanel && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 320, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="border-l border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl overflow-hidden"
              >
                <div className="w-80 h-full overflow-y-auto custom-scrollbar p-4">
                  {rightPanel}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Music Controls Bar */}
        {showMusicControls && currentSong && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 80, opacity: 1 }}
            className="bg-[var(--bg-glass)] backdrop-blur-xl border-t border-[var(--border-subtle)] px-6"
          >
            <div className="h-full flex items-center justify-between">
              {/* Song Info */}
              <div className="flex items-center gap-4 flex-1">
                <div className="w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center">
                  <MusicalNoteIcon className="h-6 w-6" />
                </div>
                <div>
                  <p className="font-medium">{currentSong.title}</p>
                  <p className="text-sm text-zinc-400">{currentSong.artist}</p>
                </div>
              </div>

              {/* Playback Controls */}
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setRepeat(!repeat)}
                  className={cn(
                    "p-2 rounded-lg transition-colors",
                    repeat ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]" : "hover:bg-white/10"
                  )}
                >
                  <ArrowPathIcon className="h-4 w-4" />
                </button>
                
                <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
                  <ChevronUpIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={onPlayPause}
                  className="p-3 bg-[var(--color-primary)] hover:bg-[var(--color-primary-hover)] rounded-full transition-colors"
                >
                  {isPlaying ? (
                    <PauseIcon className="h-5 w-5 text-black" />
                  ) : (
                    <PlayIcon className="h-5 w-5 text-black" />
                  )}
                </button>
                
                <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
                  <ChevronDownIcon className="h-4 w-4" />
                </button>

                {/* Progress */}
                <div className="flex items-center gap-2 text-xs text-zinc-400">
                  <span>{currentSong.currentTime}</span>
                  <div className="w-48 h-1 bg-white/10 rounded-full overflow-hidden">
                    <div className="h-full w-1/3 bg-[var(--color-primary)] rounded-full" />
                  </div>
                  <span>{currentSong.duration}</span>
                </div>
              </div>

              {/* Volume */}
              <div className="flex items-center gap-2 flex-1 justify-end">
                <SpeakerWaveIcon className="h-4 w-4 text-zinc-400" />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={volume}
                  onChange={(e) => setVolume(Number(e.target.value))}
                  className="w-24"
                />
                <span className="text-xs text-zinc-400 w-8">{volume}%</span>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export { MusicLayout }