"use client"

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { 
  QrCodeIcon,
  UserPlusIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  CheckCircleIcon,
  XMarkIcon,
  PrinterIcon,
  CameraIcon,
  FingerPrintIcon,
  DevicePhoneMobileIcon,
  UserIcon,
  HomeIcon,
  ArrowLeftIcon,
  ExclamationTriangleIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

interface CheckinKioskLayoutProps {
  children?: React.ReactNode
  mode?: "welcome" | "search" | "selection" | "confirmation"
  onModeChange?: (mode: string) => void
  stats?: {
    checkedIn: number
    expected: number
    visitors: number
    volunteers: number
  }
  currentService?: {
    name: string
    time: string
    id: string
  }
}

export default function CheckinKioskLayout({
  children,
  mode = "welcome",
  onModeChange,
  stats,
  currentService
}: CheckinKioskLayoutProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [adminMode, setAdminMode] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    })
  }

  if (!adminMode) {
    // Kiosk Mode - Full Screen Experience
    return (
      <div className="fixed inset-0 bg-black overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-[var(--color-primary)] to-purple-600" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,transparent_0%,black_100%)]" />
        </div>

        {/* Top Bar */}
        <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-black/80 to-transparent z-10">
          <div className="flex items-center justify-between h-full px-8">
            <div className="flex items-center gap-6">
              <Image
                src="/orbital-logo.png"
                alt="Church Logo"
                width={48}
                height={48}
                className="rounded-lg"
              />
              <div>
                <h1 className="text-2xl font-bold">Welcome to Orbital Church</h1>
                <p className="text-sm text-zinc-400">{currentService?.name || "Check-in System"}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-3xl font-bold tabular-nums">{formatTime(currentTime)}</p>
              <p className="text-sm text-zinc-400">{formatDate(currentTime)}</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="h-full flex items-center justify-center p-8 pt-28">
          <AnimatePresence mode="wait">
            {mode === "welcome" && (
              <motion.div
                key="welcome"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="text-center max-w-4xl"
              >
                <motion.div
                  initial={{ y: 20 }}
                  animate={{ y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <h1 className="text-7xl font-bold mb-4">Welcome!</h1>
                  <p className="text-3xl text-zinc-300 mb-12">
                    Let's get you checked in for today's service
                  </p>
                </motion.div>

                {/* Check-in Options */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    onClick={() => onModeChange?.("search")}
                    className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] p-8 rounded-2xl hover:scale-105 transition-all hover:shadow-glow-xl group"
                  >
                    <div className="w-20 h-20 bg-[var(--color-primary)]/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-[var(--color-primary)]/30 transition-colors">
                      <MagnifyingGlassIcon className="h-10 w-10 text-[var(--color-primary)]" />
                    </div>
                    <h3 className="text-2xl font-semibold mb-2">Search by Name</h3>
                    <p className="text-zinc-400">Find your family by typing your name</p>
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] p-8 rounded-2xl hover:scale-105 transition-all hover:shadow-glow-xl group"
                  >
                    <div className="w-20 h-20 bg-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-500/30 transition-colors">
                      <DevicePhoneMobileIcon className="h-10 w-10 text-purple-400" />
                    </div>
                    <h3 className="text-2xl font-semibold mb-2">Phone Number</h3>
                    <p className="text-zinc-400">Quick check-in with your phone</p>
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] p-8 rounded-2xl hover:scale-105 transition-all hover:shadow-glow-xl group"
                  >
                    <div className="w-20 h-20 bg-green-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-green-500/30 transition-colors">
                      <UserPlusIcon className="h-10 w-10 text-green-400" />
                    </div>
                    <h3 className="text-2xl font-semibold mb-2">First Time?</h3>
                    <p className="text-zinc-400">We'd love to get to know you</p>
                  </motion.button>
                </div>

                {/* Alternative Options */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="mt-12 flex items-center justify-center gap-6"
                >
                  <button className="flex items-center gap-3 px-6 py-3 bg-white/10 hover:bg-white/20 rounded-xl transition-colors">
                    <QrCodeIcon className="h-6 w-6" />
                    <span className="text-lg">Scan QR Code</span>
                  </button>
                  <button className="flex items-center gap-3 px-6 py-3 bg-white/10 hover:bg-white/20 rounded-xl transition-colors">
                    <CameraIcon className="h-6 w-6" />
                    <span className="text-lg">Take Photo</span>
                  </button>
                </motion.div>
              </motion.div>
            )}

            {mode === "search" && (
              <motion.div
                key="search"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                className="w-full max-w-2xl"
              >
                <button
                  onClick={() => onModeChange?.("welcome")}
                  className="flex items-center gap-2 mb-8 text-zinc-400 hover:text-white transition-colors"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                  <span>Back</span>
                </button>

                <h2 className="text-4xl font-bold mb-8">Search for Your Family</h2>

                {/* Search Input */}
                <div className="relative mb-8">
                  <MagnifyingGlassIcon className="absolute left-6 top-1/2 -translate-y-1/2 h-8 w-8 text-zinc-400" />
                  <input
                    type="text"
                    placeholder="Enter your last name..."
                    className="w-full pl-20 pr-6 py-6 bg-white/10 border-2 border-[var(--border-subtle)] rounded-2xl text-2xl focus:outline-none focus:ring-4 focus:ring-[var(--color-primary)]/20 focus:border-[var(--color-primary)]"
                    autoFocus
                  />
                </div>

                {/* Alphabet Quick Jump */}
                <div className="flex flex-wrap gap-2 mb-8">
                  {Array.from("ABCDEFGHIJKLMNOPQRSTUVWXYZ").map((letter) => (
                    <button
                      key={letter}
                      className="w-12 h-12 bg-white/10 hover:bg-white/20 rounded-lg transition-colors text-lg font-medium"
                    >
                      {letter}
                    </button>
                  ))}
                </div>

                {/* Search Results */}
                <div className="space-y-3">
                  {[
                    { name: "Johnson Family", members: 4, lastCheckin: "Last Sunday" },
                    { name: "Johnson, Robert", members: 1, lastCheckin: "2 weeks ago" },
                    { name: "Johnston Family", members: 3, lastCheckin: "Last Sunday" }
                  ].map((family, idx) => (
                    <button
                      key={idx}
                      onClick={() => onModeChange?.("selection")}
                      className="w-full p-6 bg-white/5 hover:bg-white/10 border border-[var(--border-subtle)] rounded-xl transition-all hover:scale-[1.02] text-left"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-2xl font-semibold">{family.name}</h3>
                          <p className="text-zinc-400">
                            {family.members} {family.members === 1 ? "member" : "members"} • {family.lastCheckin}
                          </p>
                        </div>
                        <ArrowLeftIcon className="h-6 w-6 rotate-180 text-zinc-400" />
                      </div>
                    </button>
                  ))}
                </div>
              </motion.div>
            )}

            {mode === "selection" && (
              <motion.div
                key="selection"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                className="w-full max-w-4xl"
              >
                <button
                  onClick={() => onModeChange?.("search")}
                  className="flex items-center gap-2 mb-8 text-zinc-400 hover:text-white transition-colors"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                  <span>Back to Search</span>
                </button>

                <h2 className="text-4xl font-bold mb-2">Select Family Members</h2>
                <p className="text-xl text-zinc-400 mb-8">Choose who's checking in today</p>

                {/* Family Members Grid */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
                  {[
                    { name: "John Johnson", age: "Adult", photo: "/placeholder-user.jpg", selected: true },
                    { name: "Sarah Johnson", age: "Adult", photo: "/placeholder-user.jpg", selected: true },
                    { name: "Emma Johnson", age: "Youth", photo: "/placeholder-user.jpg", selected: false },
                    { name: "Liam Johnson", age: "Child", photo: "/placeholder-user.jpg", selected: false }
                  ].map((member, idx) => (
                    <motion.button
                      key={idx}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: idx * 0.1 }}
                      className={cn(
                        "p-6 rounded-xl border-2 transition-all",
                        member.selected
                          ? "bg-[var(--color-primary)]/20 border-[var(--color-primary)]"
                          : "bg-white/5 border-[var(--border-subtle)] hover:bg-white/10"
                      )}
                    >
                      <div className="relative mb-4">
                        <Image
                          src={member.photo}
                          alt={member.name}
                          width={80}
                          height={80}
                          className="rounded-full mx-auto"
                        />
                        {member.selected && (
                          <div className="absolute -top-2 -right-2 w-8 h-8 bg-[var(--color-primary)] rounded-full flex items-center justify-center">
                            <CheckCircleIcon className="h-5 w-5 text-black" />
                          </div>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold">{member.name}</h3>
                      <p className="text-sm text-zinc-400">{member.age}</p>
                    </motion.button>
                  ))}
                </div>

                {/* Service/Class Selection */}
                <div className="bg-white/5 border border-[var(--border-subtle)] rounded-xl p-6 mb-8">
                  <h3 className="text-xl font-semibold mb-4">Select Services & Classes</h3>
                  <div className="space-y-3">
                    <label className="flex items-center gap-4 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer">
                      <input type="checkbox" className="w-5 h-5" defaultChecked />
                      <div className="flex-1">
                        <p className="font-medium">Main Service</p>
                        <p className="text-sm text-zinc-400">10:00 AM - Main Auditorium</p>
                      </div>
                      <Badge variant="secondary">All Ages</Badge>
                    </label>
                    <label className="flex items-center gap-4 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer">
                      <input type="checkbox" className="w-5 h-5" />
                      <div className="flex-1">
                        <p className="font-medium">Children's Church</p>
                        <p className="text-sm text-zinc-400">10:15 AM - Kids Wing</p>
                      </div>
                      <Badge variant="secondary">Ages 5-12</Badge>
                    </label>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4">
                  <Button
                    variant="primary"
                    size="lg"
                    className="flex-1 h-16 text-xl"
                    onClick={() => onModeChange?.("confirmation")}
                  >
                    Check In Selected (2)
                  </Button>
                  <Button
                    variant="glass"
                    size="lg"
                    className="h-16"
                  >
                    Add Guest
                  </Button>
                </div>
              </motion.div>
            )}

            {mode === "confirmation" && (
              <motion.div
                key="confirmation"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="text-center max-w-2xl"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", delay: 0.2 }}
                  className="w-32 h-32 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-8"
                >
                  <CheckCircleIcon className="h-20 w-20 text-green-400" />
                </motion.div>

                <h1 className="text-5xl font-bold mb-4">Check-in Complete!</h1>
                <p className="text-2xl text-zinc-300 mb-8">
                  Your name tags are printing now
                </p>

                {/* Check-in Summary */}
                <div className="bg-white/5 border border-[var(--border-subtle)] rounded-xl p-6 mb-8 text-left">
                  <h3 className="text-xl font-semibold mb-4">Check-in Summary</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <UserIcon className="h-5 w-5 text-zinc-400" />
                        <span>John Johnson</span>
                      </div>
                      <Badge variant="secondary">Main Service</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <UserIcon className="h-5 w-5 text-zinc-400" />
                        <span>Sarah Johnson</span>
                      </div>
                      <Badge variant="secondary">Main Service</Badge>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4">
                  <Button
                    variant="primary"
                    size="lg"
                    className="flex-1 h-16 text-xl"
                    onClick={() => onModeChange?.("welcome")}
                    leftIcon={<HomeIcon className="h-6 w-6" />}
                  >
                    Start New Check-in
                  </Button>
                  <Button
                    variant="glass"
                    size="lg"
                    className="h-16"
                    leftIcon={<PrinterIcon className="h-6 w-6" />}
                  >
                    Reprint Tags
                  </Button>
                </div>

                {/* Auto Return Timer */}
                <p className="text-sm text-zinc-400 mt-8">
                  Returning to home screen in 10 seconds...
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Bottom Stats Bar */}
        {stats && (
          <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-black/80 to-transparent">
            <div className="flex items-center justify-center gap-8 h-full">
              <div className="text-center">
                <p className="text-3xl font-bold">{stats.checkedIn}</p>
                <p className="text-sm text-zinc-400">Checked In</p>
              </div>
              <div className="h-8 w-px bg-zinc-600" />
              <div className="text-center">
                <p className="text-3xl font-bold">{stats.expected}</p>
                <p className="text-sm text-zinc-400">Expected</p>
              </div>
              <div className="h-8 w-px bg-zinc-600" />
              <div className="text-center">
                <p className="text-3xl font-bold">{stats.visitors}</p>
                <p className="text-sm text-zinc-400">Visitors</p>
              </div>
              <div className="h-8 w-px bg-zinc-600" />
              <div className="text-center">
                <p className="text-3xl font-bold">{stats.volunteers}</p>
                <p className="text-sm text-zinc-400">Volunteers</p>
              </div>
            </div>
          </div>
        )}

        {/* Admin Access Button */}
        <button
          onClick={() => setAdminMode(true)}
          className="absolute bottom-4 right-4 p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-colors opacity-50 hover:opacity-100"
        >
          <CogIcon className="h-5 w-5" />
        </button>
      </div>
    )
  }

  // Admin Mode
  return (
    <div className="min-h-screen bg-black">
      <div className="p-8">
        <button
          onClick={() => setAdminMode(false)}
          className="flex items-center gap-2 mb-8 text-zinc-400 hover:text-white transition-colors"
        >
          <ArrowLeftIcon className="h-5 w-5" />
          <span>Back to Kiosk Mode</span>
        </button>
        {children}
      </div>
    </div>
  )
}

// Add missing import
import { CogIcon } from "@heroicons/react/24/outline"

export { CheckinKioskLayout }