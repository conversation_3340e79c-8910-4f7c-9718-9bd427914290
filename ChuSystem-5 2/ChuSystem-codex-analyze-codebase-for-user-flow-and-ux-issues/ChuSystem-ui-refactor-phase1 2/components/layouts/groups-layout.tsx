"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarModern from "@/components/sidebar-modern"
import { 
  UserGroupIcon,
  MapPinIcon,
  CalendarIcon,
  BookOpenIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon,
  Squares2X2Icon,
  MapIcon,
  ListBulletIcon,
  BellIcon,
  ClockIcon,
  FireIcon,
  SparklesIcon,
  AcademicCapIcon,
  HomeIcon,
  GlobeAltIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface GroupCategory {
  id: string
  name: string
  icon: React.ComponentType<any>
  color: string
  count: number
}

interface GroupsLayoutProps {
  children: React.ReactNode
  viewMode?: "grid" | "list" | "map"
  onViewModeChange?: (mode: string) => void
  selectedCategory?: string
  onCategoryChange?: (category: string) => void
  stats?: {
    totalGroups: number
    activeGroups: number
    totalMembers: number
    meetingsThisWeek: number
  }
  categories?: GroupCategory[]
}

export default function GroupsLayout({
  children,
  viewMode = "grid",
  onViewModeChange,
  selectedCategory = "all",
  onCategoryChange,
  stats,
  categories = [
    { id: "small-groups", name: "Small Groups", icon: HomeIcon, color: "blue", count: 23 },
    { id: "bible-study", name: "Bible Study", icon: BookOpenIcon, color: "purple", count: 12 },
    { id: "ministry", name: "Ministry Teams", icon: HeartIcon, color: "pink", count: 18 },
    { id: "classes", name: "Classes", icon: AcademicCapIcon, color: "green", count: 8 },
    { id: "support", name: "Support Groups", icon: UserGroupIcon, color: "amber", count: 6 },
    { id: "online", name: "Online Groups", icon: GlobeAltIcon, color: "cyan", count: 4 }
  ]
}: GroupsLayoutProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [filterOpen, setFilterOpen] = useState(false)
  const [activityFeedOpen, setActivityFeedOpen] = useState(true)

  const views = [
    { id: "grid", icon: Squares2X2Icon, label: "Grid View" },
    { id: "list", icon: ListBulletIcon, label: "List View" },
    { id: "map", icon: MapIcon, label: "Map View" }
  ]

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarModern />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Bar */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)]">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Title and Search */}
              <div className="flex items-center gap-6 flex-1">
                <h1 className="text-2xl font-bold">Groups</h1>
                
                {/* Search */}
                <div className="relative max-w-md flex-1">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-zinc-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search groups by name, leader, or topic..."
                    className="w-full pl-10 pr-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                  />
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                {/* View Mode */}
                <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                  {views.map((view) => (
                    <button
                      key={view.id}
                      onClick={() => onViewModeChange?.(view.id)}
                      className={cn(
                        "p-2 rounded transition-colors",
                        viewMode === view.id
                          ? "bg-[var(--color-primary)] text-black"
                          : "hover:bg-white/10"
                      )}
                      title={view.label}
                    >
                      <view.icon className="h-4 w-4" />
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => setFilterOpen(!filterOpen)}
                  className={cn(
                    "p-2 rounded-lg transition-colors",
                    filterOpen ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]" : "hover:bg-white/10"
                  )}
                >
                  <FunnelIcon className="h-5 w-5" />
                </button>

                <button
                  onClick={() => setActivityFeedOpen(!activityFeedOpen)}
                  className={cn(
                    "p-2 rounded-lg transition-colors",
                    activityFeedOpen ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]" : "hover:bg-white/10"
                  )}
                >
                  <BellIcon className="h-5 w-5" />
                </button>

                <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                  Create Group
                </Button>
              </div>
            </div>
          </div>

          {/* Category Tabs */}
          <div className="px-6 overflow-x-auto">
            <div className="flex items-center gap-1 pb-px">
              <button
                onClick={() => onCategoryChange?.("all")}
                className={cn(
                  "px-4 py-3 border-b-2 transition-colors whitespace-nowrap",
                  selectedCategory === "all"
                    ? "border-[var(--color-primary)] text-white"
                    : "border-transparent text-zinc-400 hover:text-white"
                )}
              >
                <span className="text-sm font-medium">All Groups</span>
                {stats && (
                  <Badge variant="secondary" size="sm" className="ml-2">
                    {stats.totalGroups}
                  </Badge>
                )}
              </button>
              
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => onCategoryChange?.(category.id)}
                  className={cn(
                    "flex items-center gap-2 px-4 py-3 border-b-2 transition-colors whitespace-nowrap",
                    selectedCategory === category.id
                      ? "border-[var(--color-primary)] text-white"
                      : "border-transparent text-zinc-400 hover:text-white"
                  )}
                >
                  <category.icon className="h-4 w-4" />
                  <span className="text-sm font-medium">{category.name}</span>
                  <Badge variant="secondary" size="sm">
                    {category.count}
                  </Badge>
                </button>
              ))}
            </div>
          </div>

          {/* Stats Bar */}
          {stats && (
            <div className="px-6 py-3 border-t border-[var(--border-subtle)] bg-white/[0.02]">
              <div className="flex items-center gap-8">
                <div className="flex items-center gap-2">
                  <FireIcon className="h-4 w-4 text-orange-400" />
                  <span className="text-sm">
                    <span className="font-medium">{stats.activeGroups}</span>
                    <span className="text-zinc-400"> active groups</span>
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <UserGroupIcon className="h-4 w-4 text-blue-400" />
                  <span className="text-sm">
                    <span className="font-medium">{stats.totalMembers}</span>
                    <span className="text-zinc-400"> total members</span>
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-green-400" />
                  <span className="text-sm">
                    <span className="font-medium">{stats.meetingsThisWeek}</span>
                    <span className="text-zinc-400"> meetings this week</span>
                  </span>
                </div>
                <div className="flex-1" />
                <div className="flex items-center gap-2">
                  <SparklesIcon className="h-4 w-4 text-purple-400" />
                  <span className="text-sm text-purple-400">3 new groups this month</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Filter Panel */}
          <AnimatePresence>
            {filterOpen && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 280, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="border-r border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl overflow-hidden"
              >
                <div className="w-[280px] h-full overflow-y-auto custom-scrollbar p-4">
                  <h3 className="text-sm font-medium mb-4">Filters</h3>
                  
                  {/* Meeting Day */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Meeting Day</p>
                    <div className="space-y-2">
                      {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].map((day) => (
                        <label key={day} className="flex items-center gap-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-sm">{day}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Meeting Time */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Meeting Time</p>
                    <div className="space-y-2">
                      {["Morning (6AM-12PM)", "Afternoon (12PM-5PM)", "Evening (5PM-9PM)", "Night (9PM+)"].map((time) => (
                        <label key={time} className="flex items-center gap-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-sm">{time}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Group Size */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Group Size</p>
                    <div className="space-y-2">
                      {["Small (2-6)", "Medium (7-12)", "Large (13-20)", "Very Large (20+)"].map((size) => (
                        <label key={size} className="flex items-center gap-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-sm">{size}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Location Type */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Location</p>
                    <div className="space-y-2">
                      {["On Campus", "Homes", "Online", "Hybrid"].map((location) => (
                        <label key={location} className="flex items-center gap-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-sm">{location}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Childcare */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Other Options</p>
                    <div className="space-y-2">
                      <label className="flex items-center gap-2">
                        <input type="checkbox" className="rounded" />
                        <span className="text-sm">Childcare Provided</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" className="rounded" />
                        <span className="text-sm">Open to New Members</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" className="rounded" />
                        <span className="text-sm">Active in Last 30 Days</span>
                      </label>
                    </div>
                  </div>

                  <Button variant="primary" size="sm" className="w-full">
                    Apply Filters
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto custom-scrollbar bg-black/50">
            {children}
          </div>

          {/* Activity Feed */}
          <AnimatePresence>
            {activityFeedOpen && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 320, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="border-l border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl overflow-hidden"
              >
                <div className="w-80 h-full overflow-y-auto custom-scrollbar">
                  <div className="p-4">
                    <h3 className="text-sm font-medium mb-4">Group Activity</h3>
                    
                    {/* Activity Items */}
                    <div className="space-y-3">
                      {[
                        {
                          type: "new_member",
                          group: "Young Adults",
                          user: "Sarah Johnson",
                          time: "2 hours ago",
                          icon: UserGroupIcon,
                          color: "text-green-400"
                        },
                        {
                          type: "meeting_scheduled",
                          group: "Men's Bible Study",
                          detail: "Thursday 7:00 PM",
                          time: "4 hours ago",
                          icon: CalendarIcon,
                          color: "text-blue-400"
                        },
                        {
                          type: "new_discussion",
                          group: "Women's Ministry",
                          user: "Emily Davis",
                          detail: "Posted: 'Prayer Request...'",
                          time: "6 hours ago",
                          icon: ChatBubbleLeftRightIcon,
                          color: "text-purple-400"
                        },
                        {
                          type: "video_uploaded",
                          group: "Youth Group",
                          detail: "Summer Camp Highlights",
                          time: "Yesterday",
                          icon: VideoCameraIcon,
                          color: "text-pink-400"
                        },
                        {
                          type: "milestone",
                          group: "Marriage Enrichment",
                          detail: "Reached 50 members!",
                          time: "2 days ago",
                          icon: SparklesIcon,
                          color: "text-amber-400"
                        }
                      ].map((activity, idx) => (
                        <div
                          key={idx}
                          className="p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                        >
                          <div className="flex items-start gap-3">
                            <div className={cn(
                              "w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center flex-shrink-0",
                              activity.color
                            )}>
                              <activity.icon className="h-4 w-4" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{activity.group}</p>
                              {activity.user && (
                                <p className="text-xs text-zinc-400">{activity.user}</p>
                              )}
                              {activity.detail && (
                                <p className="text-xs text-zinc-300 mt-1">{activity.detail}</p>
                              )}
                              <p className="text-xs text-zinc-500 mt-1">
                                <ClockIcon className="h-3 w-3 inline mr-1" />
                                {activity.time}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Trending Groups */}
                  <div className="p-4 border-t border-[var(--border-subtle)]">
                    <h3 className="text-sm font-medium mb-3">Trending Groups</h3>
                    <div className="space-y-2">
                      {[
                        { name: "Young Professionals", members: 45, growth: "+12" },
                        { name: "New Parents Support", members: 28, growth: "+8" },
                        { name: "Financial Peace", members: 34, growth: "+6" }
                      ].map((group, idx) => (
                        <div
                          key={idx}
                          className="flex items-center justify-between p-2 hover:bg-white/5 rounded-lg transition-colors cursor-pointer"
                        >
                          <div>
                            <p className="text-sm font-medium">{group.name}</p>
                            <p className="text-xs text-zinc-400">{group.members} members</p>
                          </div>
                          <Badge variant="secondary" size="sm" className="bg-green-500/20 text-green-400">
                            {group.growth}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}

export { GroupsLayout }