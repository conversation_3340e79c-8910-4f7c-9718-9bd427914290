# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- **Development Server**: `npm run dev` - Start Next.js development server
- **Build**: `npm run build` - Build the application for production
- **Production Server**: `npm run start` - Start production server
- **Linting**: `npm run lint` - Run ESLint (configured to ignore errors during builds)
- **Testing**: `npm run test` - Run Vitest tests (Vitest configured with jsdom environment)

### Package Management
- Uses `pnpm` as the package manager (pnpm-lock.yaml present)
- Dependencies managed through package.json
- **Testing Framework**: Vitest with React Testing Library and jsdom environment

## Architecture Overview

### Application Structure
This is a **Next.js 15 church management system** built with:
- **App Router** (app/ directory structure)
- **React 19** with TypeScript
- **Tailwind CSS** with shadcn/ui components
- **Dark-first design system** with glassmorphism UI patterns

### Key Architectural Patterns

#### Module Layout System
- **ModuleLayout** (`/components/ui/module-layout.tsx`) is the primary layout wrapper
- Provides consistent sidebar, top bar with search/title, and right panel structure
- Each page should wrap content with ModuleLayout for consistency
- Support for multiple header modes: default, compact, search-focused, title-focused
- Fixed sidebar offset (ml-80) with overflow handling for content areas

#### Context-Driven Actions
- **ActionsProvider** (`/lib/actions-context.tsx`) manages page-specific quick actions
- Actions automatically update based on current route (`usePathname`)
- Each module (/services, /people, /events, etc.) has predefined action sets with 6-8 actions each
- Actions include keyboard shortcuts, color coding, and Heroicons integration
- Module actions cover core workflows like New Service, Add Song, Live Mode, etc.

#### Design System Components
Core UI components located in `/components/ui/` and design system in `/components/design-system/`:
- **ModuleLayout**: Main page wrapper with sidebar and panels (`/components/ui/module-layout.tsx`)
- **Glass Components**: Glassmorphic variants (glass-card, glass-input, glass-modal, etc.)
- **Enhanced Components**: Enhanced versions with additional features (enhanced-quick-actions, enhanced-product-switcher)
- **Layout Components**: Specialized layouts for different modules (layouts/, service-planning/)
- **shadcn/ui Integration**: Full shadcn/ui component library with custom glass variants

### Styling Architecture
- **Glassmorphism Design**: CSS variables for glass effects (--bg-glass, --bg-glass-hover, --border-subtle)
- **Custom CSS Variables**: Comprehensive color system in globals.css with dark theme support
- **Tailwind Extensions**: Custom border radius (pill, 2xl), enhanced shadows, gradient backgrounds
- **Animation System**: Fade-in, fade-in-up, float animations with staggered delay utilities (100ms-1000ms)
- **Custom Scrollbars**: Styled webkit scrollbars with glass appearance

### Component Patterns
- **Barrel Exports**: Components export both named and default exports for flexibility
- **Variant System**: Extensive use of class-variance-authority for component variants
- **Glass UI**: Primary variant system across all components for consistent glassmorphic look
- **Icon Integration**: Heroicons 16/solid and 24/outline for consistent icon usage
- **shadcn/ui Base**: Built on Radix UI primitives with custom styling extensions

### Data Flow
- **Provider Architecture**: Multiple context providers in root layout (ThemeProvider, ActionsProvider, CommandPaletteProvider)
- **Theme Provider**: Dark mode enforced, no system theme switching (enableSystem: false)
- **Actions Context**: Global context for page-specific actions with route-based updates
- **Command Palette**: Global command palette integration for keyboard navigation
- **Static Data**: Currently uses placeholder data (no external API integration)

## Development Guidelines

### Component Creation
- Follow existing patterns in `/components/ui/` and `/components/design-system/`
- Use glassmorphic styling with backdrop-blur effects and CSS variables
- Include both named and default exports for maximum compatibility
- Implement class-variance-authority for variant systems
- Prefer glass variants (glass-card, glass-input) for consistency

### Page Development
- Wrap all pages with ModuleLayout for consistency
- Define page-specific actions in actions-context.tsx
- Use existing DataCard and Card components for content
- Follow the module-based routing structure

### Styling Standards
- Use Tailwind utility classes with custom extensions
- Apply glassmorphic design patterns consistently
- Implement fade-in animations with staggered delays
- Maintain dark theme throughout the application

### Import Patterns
- Use absolute imports with @ alias configured in components.json
- Import paths: @/components, @/lib, @/hooks, @/utils
- Import ModuleLayout from @/components/ui/module-layout
- Use barrel exports from shadcn/ui components
- Heroicons imports: use 16/solid for small icons, 24/outline for larger ones

## Build Configuration
- **TypeScript**: Ignores build errors (configured in next.config.mjs)
- **ESLint**: Ignores errors during builds for faster development
- **Images**: Unoptimized for deployment flexibility
- **Vitest**: Configured with jsdom environment and React plugin
- **shadcn/ui**: Configured with neutral base color and CSS variables
- **Deployment**: Configured for Vercel deployment with v0.dev integration

## Key Dependencies
- **UI Framework**: Radix UI primitives with shadcn/ui patterns and class-variance-authority
- **Styling**: Tailwind CSS with custom glassmorphism extensions and tailwindcss-animate
- **Icons**: Heroicons (both 16/solid and 24/outline) and Lucide React
- **Animation**: Framer Motion for complex animations
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for data visualization
- **Drag & Drop**: React DnD with HTML5 backend for interactive interfaces
- **Command Palette**: cmdk for command palette functionality
- **Testing**: Vitest, React Testing Library, jsdom
- **Carousel**: Embla Carousel React for carousel components