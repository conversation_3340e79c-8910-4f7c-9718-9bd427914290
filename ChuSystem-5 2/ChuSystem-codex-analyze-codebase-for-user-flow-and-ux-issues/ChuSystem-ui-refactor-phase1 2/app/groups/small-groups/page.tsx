"use client"

import {
  ArrowLeftIcon,
  UserIcon as UserGroupIcon,
  PlusIcon,
  MapPinIcon,
  CalendarIcon,
  Users,
  Clock,
  TrendingUp,
} from "lucide-react"
import ModuleLayout from "@/components/ui/module-layout"
import ActionButton from "@/components/ui/action-button"
import DataCard from "@/components/ui/data-card"
import { Button } from "@/components/ui/button"

export default function SmallGroupsPage() {
  const smallGroupsHelpContent = (
    <div>
      <p>
        Small Groups are the heart of community life, providing intimate settings for fellowship, study, and growth.
      </p>
      <ul className="mt-2 space-y-1">
        <li>• Create and manage small group communities</li>
        <li>• Track attendance and member engagement</li>
        <li>• Facilitate group discussions and activities</li>
        <li>• Share resources and study materials</li>
        <li>• Coordinate group meetings and events</li>
        <li>• Support group leaders with training and tools</li>
      </ul>
      <p className="mt-2">Foster deeper relationships and spiritual growth through small group ministry.</p>
    </div>
  )

  const moduleActions = (
    <div className="flex items-center gap-3">
      <ActionButton
        icon={<ArrowLeftIcon className="h-4 w-4" />}
        label="Back to Groups"
        onClick={() => console.log("Back clicked")}
        variant="secondary"
        size="sm"
        tooltip="Return to groups overview"
      />

      <ActionButton
        icon={<MapPinIcon className="h-4 w-4" />}
        label="Group Map"
        onClick={() => console.log("Group map clicked")}
        variant="secondary"
        size="sm"
        tooltip="View groups by location"
      />

      <ActionButton
        icon={<PlusIcon className="h-4 w-4" />}
        label="New Small Group"
        onClick={() => console.log("New small group clicked")}
        variant="primary"
        size="sm"
        tooltip="Create a new small group"
      />
    </div>
  )

  return (
    <ModuleLayout
      title="Small Groups"
      icon={<UserGroupIcon className="h-5 w-5" />}
      helpContent={smallGroupsHelpContent}
      actions={moduleActions}
      searchPlaceholder="Search small groups by name, leader, or location..."
    >
      {/* Small Groups Overview */}
      <div className="content-section">
        <h2 className="section-title">
          <UserGroupIcon className="h-5 w-5 text-blue-400" />
          Small Groups Overview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-white/70">Active Groups</p>
                <p className="text-2xl font-bold">24</p>
              </div>
              <UserGroupIcon className="h-8 w-8 text-blue-400 opacity-80" />
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-white/70">Total Members</p>
                <p className="text-2xl font-bold">186</p>
              </div>
              <Users className="h-8 w-8 text-green-400 opacity-80" />
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-white/70">Weekly Meetings</p>
                <p className="text-2xl font-bold">18</p>
              </div>
              <CalendarIcon className="h-8 w-8 text-purple-400 opacity-80" />
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-white/70">Avg Attendance</p>
                <p className="text-2xl font-bold">82%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-amber-400 opacity-80" />
            </div>
          </div>
        </div>
      </div>

      {/* Active Small Groups */}
      <div className="content-section">
        <DataCard
          title="Active Small Groups"
          tooltip="All currently active small groups"
          actions={
            <div className="flex gap-2">
              <ActionButton
                icon={<MapPinIcon className="h-4 w-4" />}
                label="Map View"
                onClick={() => console.log("Map view clicked")}
                variant="secondary"
                size="sm"
              />
              <ActionButton
                icon={<PlusIcon className="h-4 w-4" />}
                label="Add Group"
                onClick={() => console.log("Add group clicked")}
                variant="primary"
                size="sm"
              />
            </div>
          }
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                name: "Young Adults Fellowship",
                leader: "Sarah Johnson",
                members: 12,
                location: "Community Center",
                meetingDay: "Wednesday",
                meetingTime: "7:00 PM",
                status: "active",
                growth: "+2",
              },
              {
                name: "Couples Connection",
                leader: "Mike & Lisa Chen",
                members: 8,
                location: "Johnson Home",
                meetingDay: "Friday",
                meetingTime: "7:30 PM",
                status: "active",
                growth: "+1",
              },
              {
                name: "Men's Brotherhood",
                leader: "David Wilson",
                members: 15,
                location: "Church Building",
                meetingDay: "Saturday",
                meetingTime: "8:00 AM",
                status: "active",
                growth: "0",
              },
              {
                name: "Women's Study Circle",
                leader: "Jennifer Martinez",
                members: 10,
                location: "Martinez Home",
                meetingDay: "Tuesday",
                meetingTime: "10:00 AM",
                status: "active",
                growth: "+3",
              },
              {
                name: "College & Career",
                leader: "Alex Thompson",
                members: 18,
                location: "Youth Center",
                meetingDay: "Thursday",
                meetingTime: "7:00 PM",
                status: "active",
                growth: "+5",
              },
              {
                name: "Empty Nesters",
                leader: "Robert & Mary Davis",
                members: 6,
                location: "Davis Home",
                meetingDay: "Sunday",
                meetingTime: "6:00 PM",
                status: "active",
                growth: "+1",
              },
            ].map((group, index) => (
              <div key={index} className="card p-4 hover-lift cursor-pointer">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold">{group.name}</h3>
                  <span className="badge badge-primary">{group.status}</span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <UserGroupIcon className="h-4 w-4 text-white/50" />
                    <span>{group.leader}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-white/50" />
                    <span>{group.members} members</span>
                    {group.growth !== "0" && <span className="text-green-400 text-xs">({group.growth})</span>}
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPinIcon className="h-4 w-4 text-white/50" />
                    <span>{group.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-white/50" />
                    <span>
                      {group.meetingDay}s at {group.meetingTime}
                    </span>
                  </div>
                </div>

                <div className="flex gap-2 mt-4">
                  <Button variant="outline" size="sm" className="flex-1">
                    View Details
                  </Button>
                  <Button variant="ghost" size="sm">
                    Edit
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </DataCard>
      </div>
    </ModuleLayout>
  )
}
