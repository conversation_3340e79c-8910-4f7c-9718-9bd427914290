"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommand<PERSON>enter from "@/components/ui/floating-command-center"
import { 
  UserGroupIcon,
  MapPinIcon,
  CalendarIcon,
  BookOpenIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon,
  Squares2X2Icon,
  MapIcon,
  ListBulletIcon,
  BellIcon,
  ClockIcon,
  FireIcon,
  SparklesIcon,
  AcademicCapIcon,
  HomeIcon,
  GlobeAltIcon,
  ChartBarIcon,
  HashtagIcon,
  ArrowTrendingUpIcon,
  UserPlusIcon,
  CalendarDaysIcon,
  PhotoIcon,
  DocumentTextIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  ShareIcon,
  ChevronRightIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

type ViewMode = "discover" | "mygroups" | "activity" | "network"
type DisplayMode = "grid" | "list" | "map"

interface GroupPanel {
  id: string
  group: any
  position: { x: number; y: number }
}

interface Group {
  id: string
  name: string
  category: string
  description: string
  members: number
  capacity: number
  meetingDay: string
  meetingTime: string
  location: string
  leader: string
  image?: string
  tags: string[]
  isJoined?: boolean
  activity: number // activity score 0-100
}

interface Activity {
  id: string
  type: "post" | "event" | "member_joined" | "photo" | "announcement"
  groupId: string
  groupName: string
  user: string
  content: string
  time: string
  likes?: number
  comments?: number
  image?: string
}

export default function RevolutionaryGroupsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("discover")
  const [displayMode, setDisplayMode] = useState<DisplayMode>("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [floatingPanels, setFloatingPanels] = useState<GroupPanel[]>([])
  const [activityFilter, setActivityFilter] = useState<"all" | "mygroups">("all")

  // Sample data
  const groupStats = {
    totalGroups: 67,
    myGroups: 5,
    suggestedGroups: 8,
    totalMembers: 892,
    activeThisWeek: 45,
    newGroups: 3
  }

  const categories = [
    { id: "small-groups", name: "Small Groups", icon: HomeIcon, color: "bg-blue-500", count: 23 },
    { id: "bible-study", name: "Bible Study", icon: BookOpenIcon, color: "bg-purple-500", count: 12 },
    { id: "ministry", name: "Ministry Teams", icon: HeartIcon, color: "bg-pink-500", count: 18 },
    { id: "classes", name: "Classes", icon: AcademicCapIcon, color: "bg-green-500", count: 8 },
    { id: "support", name: "Support Groups", icon: UserGroupIcon, color: "bg-amber-500", count: 6 }
  ]

  const sampleGroups: Group[] = [
    {
      id: "1",
      name: "Young Professionals",
      category: "small-groups",
      description: "Connect with other young professionals navigating faith and career",
      members: 24,
      capacity: 30,
      meetingDay: "Thursday",
      meetingTime: "7:00 PM",
      location: "Room 201",
      leader: "John Smith",
      tags: ["20s-30s", "career", "networking"],
      isJoined: true,
      activity: 95
    },
    {
      id: "2",
      name: "Women's Bible Study",
      category: "bible-study",
      description: "Dive deep into Scripture with other women seeking spiritual growth",
      members: 18,
      capacity: 20,
      meetingDay: "Tuesday",
      meetingTime: "10:00 AM",
      location: "Chapel",
      leader: "Sarah Johnson",
      tags: ["women", "morning", "bible"],
      isJoined: false,
      activity: 88
    },
    {
      id: "3",
      name: "Marriage Enrichment",
      category: "support",
      description: "Strengthen your marriage through biblical principles and community",
      members: 16,
      capacity: 24,
      meetingDay: "Sunday",
      meetingTime: "6:00 PM",
      location: "Fellowship Hall",
      leader: "Mike & Lisa Davis",
      tags: ["couples", "marriage", "relationships"],
      isJoined: false,
      activity: 72
    }
  ]

  const recentActivities: Activity[] = [
    {
      id: "1",
      type: "post",
      groupId: "1",
      groupName: "Young Professionals",
      user: "Emily Chen",
      content: "Great discussion tonight about balancing faith and work! Thanks everyone for sharing.",
      time: "2 hours ago",
      likes: 12,
      comments: 4
    },
    {
      id: "2",
      type: "event",
      groupId: "2",
      groupName: "Women's Bible Study",
      user: "Sarah Johnson",
      content: "Special guest speaker next Tuesday! Don't miss it.",
      time: "4 hours ago",
      likes: 8,
      comments: 2
    },
    {
      id: "3",
      type: "member_joined",
      groupId: "1",
      groupName: "Young Professionals",
      user: "David Kim",
      content: "just joined the group",
      time: "6 hours ago"
    },
    {
      id: "4",
      type: "photo",
      groupId: "3",
      groupName: "Marriage Enrichment",
      user: "Mike Davis",
      content: "Photos from our couples retreat last weekend!",
      time: "1 day ago",
      likes: 24,
      comments: 7,
      image: "/placeholder.jpg"
    }
  ]

  const addFloatingPanel = (group: any) => {
    const newPanel: GroupPanel = {
      id: group.id,
      group,
      position: { x: window.innerWidth / 2, y: window.innerHeight / 2 }
    }
    setFloatingPanels([...floatingPanels, newPanel])
  }

  const removeFloatingPanel = (id: string) => {
    setFloatingPanels(floatingPanels.filter(panel => panel.id !== id))
  }

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarRevolutionary />

      {/* Main Content - NO SCROLLING */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Primary Navigation Bar - Sticky */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] sticky top-0 z-30">
          <div className="h-full px-6 flex items-center justify-between">
            {/* View Mode Switcher */}
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">Groups</h1>
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "discover", label: "Discover", icon: SparklesIcon },
                  { id: "mygroups", label: "My Groups", icon: UserGroupIcon, badge: groupStats.myGroups },
                  { id: "activity", label: "Activity", icon: FireIcon },
                  { id: "network", label: "Network", icon: ShareIcon }
                ].map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => setViewMode(mode.id as ViewMode)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded transition-all",
                      viewMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <mode.icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{mode.label}</span>
                    {mode.badge && (
                      <Badge variant="secondary" size="sm">
                        {mode.badge}
                      </Badge>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              {/* Display Mode */}
              {(viewMode === "discover" || viewMode === "mygroups") && (
                <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                  {[
                    { id: "grid", icon: Squares2X2Icon },
                    { id: "list", icon: ListBulletIcon },
                    { id: "map", icon: MapIcon }
                  ].map((mode) => (
                    <button
                      key={mode.id}
                      onClick={() => setDisplayMode(mode.id as DisplayMode)}
                      className={cn(
                        "p-2 rounded transition-colors",
                        displayMode === mode.id
                          ? "bg-[var(--color-primary)] text-black"
                          : "hover:bg-white/10"
                      )}
                    >
                      <mode.icon className="h-4 w-4" />
                    </button>
                  ))}
                </div>
              )}

              <Button variant="glass" size="sm" leftIcon={<FunnelIcon className="h-4 w-4" />}>
                Filter
              </Button>

              <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Start a Group
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Category Bar */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)]">
          {/* Search */}
          <div className="p-4 border-b border-[var(--border-subtle)]">
            <div className="relative max-w-2xl">
              <MagnifyingGlassIcon className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-zinc-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search groups by name, topic, or leader..."
                className="w-full pl-12 pr-4 py-3 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
              />
            </div>
          </div>

          {/* Categories */}
          {viewMode === "discover" && (
            <div className="px-6 py-3 flex items-center gap-2 overflow-x-auto">
              <button
                onClick={() => setSelectedCategory(null)}
                className={cn(
                  "px-4 py-1.5 rounded-lg text-sm font-medium whitespace-nowrap transition-colors",
                  selectedCategory === null
                    ? "bg-[var(--color-primary)] text-black"
                    : "bg-white/5 hover:bg-white/10"
                )}
              >
                All Groups
              </button>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={cn(
                    "flex items-center gap-2 px-4 py-1.5 rounded-lg text-sm font-medium whitespace-nowrap transition-colors",
                    selectedCategory === category.id
                      ? "bg-[var(--color-primary)] text-black"
                      : "bg-white/5 hover:bg-white/10"
                  )}
                >
                  <category.icon className="h-4 w-4" />
                  <span>{category.name}</span>
                  <Badge variant="secondary" size="sm">{category.count}</Badge>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Stats Bar - Always Visible */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center">
          <div className="grid grid-cols-6 gap-6 w-full">
            <div className="text-center">
              <p className="text-2xl font-bold">{groupStats.totalGroups}</p>
              <p className="text-xs text-zinc-400">Total Groups</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">{groupStats.myGroups}</p>
              <p className="text-xs text-zinc-400">My Groups</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{groupStats.totalMembers}</p>
              <p className="text-xs text-zinc-400">Total Members</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{groupStats.activeThisWeek}</p>
              <p className="text-xs text-zinc-400">Active This Week</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-400">+{groupStats.newGroups}</p>
              <p className="text-xs text-zinc-400">New This Month</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-amber-400">{groupStats.suggestedGroups}</p>
              <p className="text-xs text-zinc-400">Suggested</p>
            </div>
          </div>
        </div>

        {/* Main Content Area - Dynamic Based on View Mode */}
        <div className="flex-1 relative overflow-hidden">
          {viewMode === "discover" && displayMode === "grid" && (
            <div className="h-full p-6 overflow-y-auto custom-scrollbar">
              {/* Suggested Groups */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-medium">Suggested for You</h2>
                  <Button variant="glass" size="sm">See All</Button>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  {sampleGroups.filter(g => !g.isJoined).slice(0, 3).map((group) => (
                    <motion.div
                      key={group.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 hover:shadow-glow-lg transition-all cursor-pointer group"
                      onClick={() => addFloatingPanel(group)}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center", categories.find(c => c.id === group.category)?.color)}>
                          {React.createElement(categories.find(c => c.id === group.category)?.icon || UserGroupIcon, { className: "h-6 w-6 text-white" })}
                        </div>
                        <Badge variant="secondary" size="sm" className="bg-amber-500/20 text-amber-400">
                          Suggested
                        </Badge>
                      </div>
                      
                      <h3 className="font-medium mb-2">{group.name}</h3>
                      <p className="text-sm text-zinc-400 mb-4 line-clamp-2">{group.description}</p>
                      
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center gap-2 text-sm">
                          <CalendarIcon className="h-4 w-4 text-zinc-400" />
                          <span>{group.meetingDay}s at {group.meetingTime}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <MapPinIcon className="h-4 w-4 text-zinc-400" />
                          <span>{group.location}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <UserGroupIcon className="h-4 w-4 text-zinc-400" />
                          <span>{group.members}/{group.capacity} members</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex -space-x-2">
                          {[1, 2, 3].map((i) => (
                            <Image
                              key={i}
                              src="/placeholder-user.jpg"
                              alt="Member"
                              width={24}
                              height={24}
                              className="rounded-full border-2 border-[var(--bg-glass)]"
                            />
                          ))}
                          {group.members > 3 && (
                            <div className="w-6 h-6 rounded-full bg-white/10 border-2 border-[var(--bg-glass)] flex items-center justify-center">
                              <span className="text-xs">+{group.members - 3}</span>
                            </div>
                          )}
                        </div>
                        <ChevronRightIcon className="h-5 w-5 text-zinc-400 group-hover:text-white transition-colors" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* All Groups */}
              <div>
                <h2 className="text-lg font-medium mb-4">All Groups</h2>
                <div className="grid grid-cols-3 gap-4">
                  {sampleGroups.map((group, idx) => (
                    <motion.div
                      key={group.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: idx * 0.05 }}
                      className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 hover:shadow-glow-lg transition-all cursor-pointer"
                      onClick={() => addFloatingPanel(group)}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center", categories.find(c => c.id === group.category)?.color)}>
                          {React.createElement(categories.find(c => c.id === group.category)?.icon || UserGroupIcon, { className: "h-6 w-6 text-white" })}
                        </div>
                        {group.isJoined && (
                          <Badge variant="secondary" size="sm" className="bg-green-500/20 text-green-400">
                            Joined
                          </Badge>
                        )}
                      </div>
                      
                      <h3 className="font-medium mb-2">{group.name}</h3>
                      <p className="text-sm text-zinc-400 mb-4 line-clamp-2">{group.description}</p>
                      
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2 text-sm">
                          <UserGroupIcon className="h-4 w-4 text-zinc-400" />
                          <span>{group.members} members</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <FireIcon className="h-4 w-4 text-orange-400" />
                          <span className="text-sm text-orange-400">{group.activity}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1">
                        {group.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" size="sm">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {viewMode === "mygroups" && (
            <div className="h-full p-6">
              <div className="grid grid-cols-12 gap-6 h-full">
                {/* My Groups List */}
                <div className="col-span-8 space-y-4">
                  <h2 className="text-lg font-medium">My Groups ({groupStats.myGroups})</h2>
                  
                  {sampleGroups.filter(g => g.isJoined).map((group) => (
                    <div
                      key={group.id}
                      className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 hover:shadow-glow-lg transition-all cursor-pointer"
                      onClick={() => addFloatingPanel(group)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex gap-4">
                          <div className={cn("w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0", categories.find(c => c.id === group.category)?.color)}>
                            {React.createElement(categories.find(c => c.id === group.category)?.icon || UserGroupIcon, { className: "h-8 w-8 text-white" })}
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-2">
                              <h3 className="text-lg font-medium">{group.name}</h3>
                              <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                                Active Member
                              </Badge>
                            </div>
                            
                            <p className="text-sm text-zinc-400 mb-3">{group.description}</p>
                            
                            <div className="grid grid-cols-3 gap-4 mb-4">
                              <div className="text-center p-2 bg-white/5 rounded-lg">
                                <p className="text-lg font-bold">{group.members}</p>
                                <p className="text-xs text-zinc-400">Members</p>
                              </div>
                              <div className="text-center p-2 bg-white/5 rounded-lg">
                                <p className="text-lg font-bold">12</p>
                                <p className="text-xs text-zinc-400">Posts/Week</p>
                              </div>
                              <div className="text-center p-2 bg-white/5 rounded-lg">
                                <p className="text-lg font-bold">3</p>
                                <p className="text-xs text-zinc-400">Upcoming</p>
                              </div>
                            </div>
                            
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4 text-sm">
                                <div className="flex items-center gap-2">
                                  <CalendarIcon className="h-4 w-4 text-zinc-400" />
                                  <span>{group.meetingDay}s at {group.meetingTime}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <MapPinIcon className="h-4 w-4 text-zinc-400" />
                                  <span>{group.location}</span>
                                </div>
                              </div>
                              <Button variant="glass" size="sm">
                                View Group
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Quick Actions & Upcoming */}
                <div className="col-span-4 space-y-4">
                  {/* Quick Actions */}
                  <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                    <h3 className="text-sm font-medium mb-3">Quick Actions</h3>
                    <div className="space-y-2">
                      <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<ChatBubbleLeftRightIcon className="h-4 w-4" />}>
                        Post to All Groups
                      </Button>
                      <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<CalendarDaysIcon className="h-4 w-4" />}>
                        View Group Calendar
                      </Button>
                      <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
                        Invite Friends
                      </Button>
                    </div>
                  </div>

                  {/* Upcoming Events */}
                  <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                    <h3 className="text-sm font-medium mb-3">Upcoming in Your Groups</h3>
                    <div className="space-y-3">
                      {[
                        { group: "Young Professionals", event: "Monthly Dinner", date: "Tomorrow, 7PM" },
                        { group: "Men's Bible Study", event: "Chapter 5 Discussion", date: "Wed, 7AM" },
                        { group: "Volunteer Team", event: "Service Project", date: "Sat, 9AM" }
                      ].map((item, idx) => (
                        <div key={idx} className="p-3 bg-white/5 rounded-lg">
                          <p className="text-sm font-medium">{item.event}</p>
                          <p className="text-xs text-zinc-400">{item.group}</p>
                          <p className="text-xs text-blue-400 mt-1">{item.date}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "activity" && (
            <div className="h-full flex">
              {/* Activity Feed */}
              <div className="flex-1 p-6 overflow-y-auto custom-scrollbar">
                {/* Filter */}
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-medium">Group Activity</h2>
                  <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                    <button
                      onClick={() => setActivityFilter("all")}
                      className={cn(
                        "px-3 py-1.5 rounded text-sm transition-colors",
                        activityFilter === "all" ? "bg-[var(--color-primary)] text-black" : "hover:bg-white/10"
                      )}
                    >
                      All Groups
                    </button>
                    <button
                      onClick={() => setActivityFilter("mygroups")}
                      className={cn(
                        "px-3 py-1.5 rounded text-sm transition-colors",
                        activityFilter === "mygroups" ? "bg-[var(--color-primary)] text-black" : "hover:bg-white/10"
                      )}
                    >
                      My Groups
                    </button>
                  </div>
                </div>

                {/* Activity Items */}
                <div className="space-y-4">
                  {recentActivities.map((activity, idx) => (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: idx * 0.05 }}
                      className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6"
                    >
                      <div className="flex items-start gap-4">
                        <Image
                          src="/placeholder-user.jpg"
                          alt="User"
                          width={48}
                          height={48}
                          className="rounded-full"
                        />
                        
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <p className="font-medium">
                                {activity.user}
                                {activity.type === "member_joined" && (
                                  <span className="font-normal text-zinc-400"> {activity.content}</span>
                                )}
                              </p>
                              <p className="text-sm text-zinc-400">
                                {activity.groupName} • {activity.time}
                              </p>
                            </div>
                            
                            {activity.type === "event" && (
                              <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                                Event
                              </Badge>
                            )}
                          </div>

                          {activity.type !== "member_joined" && (
                            <p className="mb-3">{activity.content}</p>
                          )}

                          {activity.image && (
                            <div className="mb-3 rounded-lg overflow-hidden">
                              <Image
                                src={activity.image}
                                alt="Post image"
                                width={400}
                                height={300}
                                className="w-full"
                              />
                            </div>
                          )}

                          {(activity.likes || activity.comments) && (
                            <div className="flex items-center gap-4">
                              {activity.likes && (
                                <button className="flex items-center gap-2 text-sm text-zinc-400 hover:text-white transition-colors">
                                  <HeartIcon className="h-5 w-5" />
                                  <span>{activity.likes}</span>
                                </button>
                              )}
                              {activity.comments && (
                                <button className="flex items-center gap-2 text-sm text-zinc-400 hover:text-white transition-colors">
                                  <ChatBubbleLeftRightIcon className="h-5 w-5" />
                                  <span>{activity.comments}</span>
                                </button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Trending Sidebar */}
              <div className="w-80 border-l border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl p-4 overflow-y-auto custom-scrollbar">
                {/* Trending Topics */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium mb-3">Trending Topics</h3>
                  <div className="space-y-2">
                    {["#prayer", "#testimony", "#service", "#fellowship", "#biblestudy"].map((tag, idx) => (
                      <div key={tag} className="flex items-center justify-between p-2 hover:bg-white/5 rounded-lg transition-colors cursor-pointer">
                        <span className="text-sm">{tag}</span>
                        <Badge variant="secondary" size="sm">{45 - idx * 8}</Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Active Groups */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium mb-3">Most Active Groups</h3>
                  <div className="space-y-2">
                    {sampleGroups.map((group) => (
                      <div
                        key={group.id}
                        className="flex items-center justify-between p-2 hover:bg-white/5 rounded-lg transition-colors cursor-pointer"
                        onClick={() => addFloatingPanel(group)}
                      >
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center">
                            <FireIcon className="h-4 w-4 text-orange-400" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">{group.name}</p>
                            <p className="text-xs text-zinc-400">{group.members} members</p>
                          </div>
                        </div>
                        <ArrowRightIcon className="h-4 w-4 text-zinc-400" />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Suggested Connections */}
                <div>
                  <h3 className="text-sm font-medium mb-3">Suggested Connections</h3>
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex items-center gap-3">
                        <Image
                          src="/placeholder-user.jpg"
                          alt="User"
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                        <div className="flex-1">
                          <p className="text-sm font-medium">User {i}</p>
                          <p className="text-xs text-zinc-400">3 mutual groups</p>
                        </div>
                        <Button variant="glass" size="sm">
                          <UserPlusIcon className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "network" && (
            <div className="h-full p-6">
              <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 relative">
                <h3 className="text-lg font-medium mb-4">Group Network Visualization</h3>
                
                {/* Network Graph */}
                <svg className="w-full h-full">
                  {/* Connections */}
                  <line x1="50%" y1="50%" x2="25%" y2="25%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="75%" y2="25%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="25%" y2="75%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="75%" y2="75%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="25%" y1="25%" x2="15%" y2="10%" stroke="var(--border-subtle)" strokeWidth="1" opacity="0.5" />
                  <line x1="25%" y1="25%" x2="10%" y2="30%" stroke="var(--border-subtle)" strokeWidth="1" opacity="0.5" />
                  
                  {/* Center - You */}
                  <g transform="translate(50%, 50%)">
                    <circle cx="0" cy="0" r="60" fill="var(--color-primary)" fillOpacity="0.2" stroke="var(--color-primary)" strokeWidth="2" />
                    <text x="0" y="0" textAnchor="middle" dominantBaseline="middle" fill="white" className="text-sm font-medium">You</text>
                    <text x="0" y="20" textAnchor="middle" fill="white" className="text-xs opacity-60">{groupStats.myGroups} groups</text>
                  </g>
                  
                  {/* Connected Groups */}
                  <g transform="translate(25%, 25%)">
                    <circle cx="0" cy="0" r="40" fill="rgba(59, 130, 246, 0.2)" stroke="rgb(59, 130, 246)" strokeWidth="2" className="cursor-pointer hover:fill-opacity-30" />
                    <text x="0" y="0" textAnchor="middle" dominantBaseline="middle" fill="white" className="text-xs">Young</text>
                    <text x="0" y="15" textAnchor="middle" fill="white" className="text-xs">Professionals</text>
                  </g>
                  
                  <g transform="translate(75%, 25%)">
                    <circle cx="0" cy="0" r="40" fill="rgba(168, 85, 247, 0.2)" stroke="rgb(168, 85, 247)" strokeWidth="2" className="cursor-pointer hover:fill-opacity-30" />
                    <text x="0" y="0" textAnchor="middle" dominantBaseline="middle" fill="white" className="text-xs">Bible</text>
                    <text x="0" y="15" textAnchor="middle" fill="white" className="text-xs">Study</text>
                  </g>
                  
                  <g transform="translate(25%, 75%)">
                    <circle cx="0" cy="0" r="40" fill="rgba(34, 197, 94, 0.2)" stroke="rgb(34, 197, 94)" strokeWidth="2" className="cursor-pointer hover:fill-opacity-30" />
                    <text x="0" y="0" textAnchor="middle" dominantBaseline="middle" fill="white" className="text-xs">Volunteer</text>
                    <text x="0" y="15" textAnchor="middle" fill="white" className="text-xs">Team</text>
                  </g>
                  
                  <g transform="translate(75%, 75%)">
                    <circle cx="0" cy="0" r="40" fill="rgba(251, 146, 60, 0.2)" stroke="rgb(251, 146, 60)" strokeWidth="2" className="cursor-pointer hover:fill-opacity-30" />
                    <text x="0" y="0" textAnchor="middle" dominantBaseline="middle" fill="white" className="text-xs">Men's</text>
                    <text x="0" y="15" textAnchor="middle" fill="white" className="text-xs">Group</text>
                  </g>
                  
                  {/* Secondary Connections */}
                  <g transform="translate(15%, 10%)">
                    <circle cx="0" cy="0" r="25" fill="rgba(59, 130, 246, 0.1)" stroke="rgb(59, 130, 246)" strokeWidth="1" opacity="0.7" />
                    <text x="0" y="5" textAnchor="middle" fill="white" className="text-xs opacity-70">12 mutual</text>
                  </g>
                  
                  <g transform="translate(10%, 30%)">
                    <circle cx="0" cy="0" r="25" fill="rgba(59, 130, 246, 0.1)" stroke="rgb(59, 130, 246)" strokeWidth="1" opacity="0.7" />
                    <text x="0" y="5" textAnchor="middle" fill="white" className="text-xs opacity-70">8 mutual</text>
                  </g>
                </svg>

                {/* Legend */}
                <div className="absolute top-4 right-4 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-lg p-4">
                  <h4 className="text-sm font-medium mb-2">Network Overview</h4>
                  <div className="space-y-2 text-xs">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-[var(--color-primary)] rounded-full" />
                      <span>Your Groups ({groupStats.myGroups})</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full" />
                      <span>Connected Members (45)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-white/20 rounded-full" />
                      <span>Suggested Groups (8)</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bottom Action Bar */}
        <div className="h-12 bg-[var(--bg-glass)] backdrop-blur-xl border-t border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <FireIcon className="h-4 w-4 text-orange-400" />
              <span>{groupStats.activeThisWeek} groups active this week</span>
            </div>
            <div className="flex items-center gap-2">
              <SparklesIcon className="h-4 w-4 text-purple-400" />
              <span>{groupStats.suggestedGroups} suggested groups</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="glass" size="sm" leftIcon={<CalendarDaysIcon className="h-4 w-4" />}>
              Group Calendar
            </Button>
            <Button variant="glass" size="sm" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
              Find Members
            </Button>
          </div>
        </div>
      </div>

      {/* Floating Group Panels */}
      {floatingPanels.map((panel) => (
        <motion.div
          key={panel.id}
          drag
          dragMomentum={false}
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="fixed z-40 w-[480px] bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-xl"
          style={{ left: panel.position.x - 240, top: panel.position.y - 200 }}
        >
          <div className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-4">
                <div className={cn("w-16 h-16 rounded-lg flex items-center justify-center", categories.find(c => c.id === panel.group.category)?.color)}>
                  {React.createElement(categories.find(c => c.id === panel.group.category)?.icon || UserGroupIcon, { className: "h-8 w-8 text-white" })}
                </div>
                <div>
                  <h3 className="text-lg font-medium">{panel.group.name}</h3>
                  <p className="text-sm text-zinc-400">{panel.group.leader} • Leader</p>
                </div>
              </div>
              <button
                onClick={() => removeFloatingPanel(panel.id)}
                className="p-1 hover:bg-white/10 rounded transition-colors"
              >
                <XCircleIcon className="h-5 w-5" />
              </button>
            </div>

            <p className="text-sm mb-4">{panel.group.description}</p>

            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="bg-white/5 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <CalendarIcon className="h-4 w-4 text-zinc-400" />
                  <p className="text-xs text-zinc-400">Meeting Time</p>
                </div>
                <p className="text-sm font-medium">{panel.group.meetingDay}s at {panel.group.meetingTime}</p>
              </div>
              <div className="bg-white/5 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <MapPinIcon className="h-4 w-4 text-zinc-400" />
                  <p className="text-xs text-zinc-400">Location</p>
                </div>
                <p className="text-sm font-medium">{panel.group.location}</p>
              </div>
            </div>

            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <Image
                      key={i}
                      src="/placeholder-user.jpg"
                      alt="Member"
                      width={32}
                      height={32}
                      className="rounded-full border-2 border-[var(--bg-glass)]"
                    />
                  ))}
                  <div className="w-8 h-8 rounded-full bg-white/10 border-2 border-[var(--bg-glass)] flex items-center justify-center">
                    <span className="text-xs">+{panel.group.members - 4}</span>
                  </div>
                </div>
                <span className="text-sm text-zinc-400">{panel.group.members}/{panel.group.capacity} members</span>
              </div>
              <div className="flex items-center gap-1">
                <FireIcon className="h-4 w-4 text-orange-400" />
                <span className="text-sm text-orange-400">{panel.group.activity}% active</span>
              </div>
            </div>

            <div className="flex gap-2">
              {panel.group.isJoined ? (
                <>
                  <Button variant="glass" size="sm" className="flex-1">
                    <ChatBubbleLeftRightIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="glass" size="sm" className="flex-1">
                    <CalendarDaysIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="primary" size="sm" className="flex-1">
                    View Group
                  </Button>
                </>
              ) : (
                <>
                  <Button variant="glass" size="sm" className="flex-1">
                    Learn More
                  </Button>
                  <Button variant="primary" size="sm" className="flex-1">
                    Join Group
                  </Button>
                </>
              )}
            </div>
          </div>
        </motion.div>
      ))}

      {/* Floating Command Center */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}