"use client"

import { ArrowLeftIcon, PlusIcon, UserGroupIcon, MapPinIcon } from "@heroicons/react/16/solid"
import { <PERSON>, Clock, BookOpen } from "lucide-react"
import ModuleLayout from "@/components/ui/module-layout"
import ActionButton from "@/components/ui/action-button"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"

export default function NewGroupPage() {
  const newGroupHelpContent = (
    <div>
      <p>Create a new group to foster community and spiritual growth within your church.</p>
      <ul className="mt-2 space-y-1">
        <li>• Choose the appropriate group type for your purpose</li>
        <li>• Set meeting schedules and location preferences</li>
        <li>• Define group capacity and membership criteria</li>
        <li>• Assign group leaders and co-leaders</li>
        <li>• Configure group privacy and communication settings</li>
      </ul>
      <p className="mt-2">All fields marked with * are required to create the group.</p>
    </div>
  )

  const moduleActions = (
    <div className="flex items-center gap-3">
      <ActionButton
        icon={<ArrowLeftIcon className="h-4 w-4" />}
        label="Back to Groups"
        onClick={() => console.log("Back clicked")}
        variant="secondary"
        size="sm"
        tooltip="Return to groups overview"
      />

      <ActionButton
        icon={<BookOpen className="h-4 w-4" />}
        label="Group Templates"
        onClick={() => console.log("Templates clicked")}
        variant="secondary"
        size="sm"
        tooltip="Use a group template"
      />

      <ActionButton
        icon={<PlusIcon className="h-4 w-4" />}
        label="Create Group"
        onClick={() => console.log("Create group clicked")}
        variant="primary"
        size="sm"
        tooltip="Create the new group"
      />
    </div>
  )

  return (
    <ModuleLayout
      title="Create New Group"
      icon={<UserGroupIcon className="h-5 w-5" />}
      helpContent={newGroupHelpContent}
      actions={moduleActions}
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-6">Group Information</h2>

            <div className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Basic Information</h3>

                <div>
                  <Label htmlFor="group-name">Group Name *</Label>
                  <Input id="group-name" placeholder="Enter group name" className="mt-1 bg-black/20 border-white/5" />
                </div>

                <div>
                  <Label htmlFor="group-type">Group Type *</Label>
                  <Select>
                    <SelectTrigger className="mt-1 bg-black/20 border-white/5">
                      <SelectValue placeholder="Select group type" />
                    </SelectTrigger>
                    <SelectContent className="bg-black/90 border-white/10">
                      <SelectItem value="small-group">Small Group</SelectItem>
                      <SelectItem value="bible-study">Bible Study</SelectItem>
                      <SelectItem value="ministry-team">Ministry Team</SelectItem>
                      <SelectItem value="support-group">Support Group</SelectItem>
                      <SelectItem value="fellowship">Fellowship Group</SelectItem>
                      <SelectItem value="prayer-group">Prayer Group</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="group-description">Description</Label>
                  <textarea
                    id="group-description"
                    placeholder="Describe the purpose and focus of this group"
                    className="mt-1 w-full bg-black/20 border border-white/5 rounded-lg p-3 text-sm resize-none"
                    rows={3}
                  />
                </div>
              </div>

              {/* Meeting Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Meeting Details</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="meeting-day">Meeting Day</Label>
                    <Select>
                      <SelectTrigger className="mt-1 bg-black/20 border-white/5">
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/90 border-white/10">
                        <SelectItem value="sunday">Sunday</SelectItem>
                        <SelectItem value="monday">Monday</SelectItem>
                        <SelectItem value="tuesday">Tuesday</SelectItem>
                        <SelectItem value="wednesday">Wednesday</SelectItem>
                        <SelectItem value="thursday">Thursday</SelectItem>
                        <SelectItem value="friday">Friday</SelectItem>
                        <SelectItem value="saturday">Saturday</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="meeting-time">Meeting Time</Label>
                    <div className="relative mt-1">
                      <Input id="meeting-time" type="time" className="pl-10 bg-black/20 border-white/5" />
                      <Clock className="absolute left-3 top-2.5 h-5 w-5 text-zinc-400" />
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="meeting-location">Meeting Location</Label>
                  <div className="relative mt-1">
                    <Select>
                      <SelectTrigger className="pl-10 bg-black/20 border-white/5">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/90 border-white/10">
                        <SelectItem value="church-building">Church Building</SelectItem>
                        <SelectItem value="community-center">Community Center</SelectItem>
                        <SelectItem value="member-home">Member's Home</SelectItem>
                        <SelectItem value="online">Online/Virtual</SelectItem>
                        <SelectItem value="rotating">Rotating Locations</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <MapPinIcon className="absolute left-3 top-2.5 h-5 w-5 text-zinc-400" />
                  </div>
                </div>
              </div>

              {/* Group Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Group Settings</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="max-members">Maximum Members</Label>
                    <div className="relative mt-1">
                      <Input
                        id="max-members"
                        type="number"
                        placeholder="12"
                        className="pl-10 bg-black/20 border-white/5"
                      />
                      <Users className="absolute left-3 top-2.5 h-5 w-5 text-zinc-400" />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="group-leader">Group Leader</Label>
                    <Select>
                      <SelectTrigger className="mt-1 bg-black/20 border-white/5">
                        <SelectValue placeholder="Select leader" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/90 border-white/10">
                        <SelectItem value="sarah-johnson">Sarah Johnson</SelectItem>
                        <SelectItem value="mike-chen">Mike Chen</SelectItem>
                        <SelectItem value="jennifer-martinez">Jennifer Martinez</SelectItem>
                        <SelectItem value="david-wilson">David Wilson</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="privacy-level">Privacy Level</Label>
                  <Select>
                    <SelectTrigger className="mt-1 bg-black/20 border-white/5">
                      <SelectValue placeholder="Select privacy level" />
                    </SelectTrigger>
                    <SelectContent className="bg-black/90 border-white/10">
                      <SelectItem value="open">Open - Anyone can join</SelectItem>
                      <SelectItem value="approval">Approval Required - Leader approval needed</SelectItem>
                      <SelectItem value="invite-only">Invite Only - Members must be invited</SelectItem>
                      <SelectItem value="closed">Closed - No new members</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button variant="outline" size="md" asChild>
                  <Link href="/groups">Cancel</Link>
                </Button>
                <Button variant="secondary" size="md">
                  Save as Draft
                </Button>
                <Button variant="glass" size="md">
                  Create Group
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Group Templates */}
          <div className="card p-4">
            <h3 className="text-lg font-semibold mb-4">Quick Templates</h3>
            <div className="space-y-3">
              {[
                { name: "Small Group", description: "8-12 people, weekly meetings", type: "small-group" },
                { name: "Bible Study", description: "Study-focused group", type: "bible-study" },
                { name: "Prayer Group", description: "Prayer and intercession", type: "prayer-group" },
                { name: "Fellowship", description: "Social and community", type: "fellowship" },
              ].map((template, index) => (
                <div
                  key={index}
                  className="p-3 bg-black/20 rounded-xl hover:bg-black/30 cursor-pointer transition-colors"
                >
                  <h4 className="font-medium">{template.name}</h4>
                  <p className="text-xs text-white/70">{template.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Tips */}
          <div className="card p-4">
            <h3 className="text-lg font-semibold mb-4">Group Creation Tips</h3>
            <div className="space-y-3 text-sm">
              <div className="flex gap-3">
                <div className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-400 text-xs">1</span>
                </div>
                <p>Choose a clear, descriptive name that reflects the group's purpose</p>
              </div>
              <div className="flex gap-3">
                <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-green-400 text-xs">2</span>
                </div>
                <p>Set realistic meeting times that work for your target audience</p>
              </div>
              <div className="flex gap-3">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-400 text-xs">3</span>
                </div>
                <p>Consider the optimal group size for meaningful relationships</p>
              </div>
              <div className="flex gap-3">
                <div className="w-6 h-6 rounded-full bg-amber-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-amber-400 text-xs">4</span>
                </div>
                <p>Select experienced leaders who can guide and nurture the group</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModuleLayout>
  )
}
