"use client"

import { ModuleLayout } from "@/components/ui/module-layout"
import ProfileViewModern from "@/components/profile-view-modern"
// RightPanelModern is typically handled by ModuleLayout, but can be explicitly included if needed for prop passing.
// For this example, we'll assume ModuleLayout's default handling of RightPanelModern is sufficient,
// or that ModuleLayout is enhanced to accept personId/personName for its RightPanelModern instance.
import { Button } from "@/components/ui/button"
import { DropdownMenu } from "@/components/ui/dropdown-menu"
import { Edit3, Printer, Archive, ChevronDown, UserCog } from "lucide-react" // Example icons

// Placeholder data structure, similar to what ProfileViewModern expects
interface Person {
  id: string
  name: string
  email: string
  phone: string
  location: string
  role: string
  avatar: string
  joinedDate: string
  ministries: string[]
  familyMembers: Array<{ name: string; relationship: string }>
  attendance: { percentage: number; lastAttended: string }
  giving: { totalThisYear: number; lastGift: string }
}

export default function PersonDetailPage({ params }: { params: { id: string } }) {
  // Simulate fetching person data based on params.id
  // In a real application, this would involve an API call:
  // const [person, setPerson] = useState<Person | null>(null);
  // useEffect(() => {
  //   fetchPersonData(params.id).then(setPerson);
  // }, [params.id]);
  // if (!person) return <div>Loading...</div>;

  const samplePerson: Person = {
    id: params.id,
    name: `Person ${params.id} (Details View)`, // Modified to show it's a detail view
    email: `person${params.id}@example.com`,
    phone: "555-0102",
    location: "Cityville, ST",
    role: "Lead Volunteer",
    avatar: `/avatar${(parseInt(params.id, 10) % 4) + 1}.png`, // Cycle through available avatars
    joinedDate: "2022-08-15",
    ministries: ["Worship Team", "Youth Group"],
    familyMembers: [{ name: "John Doe (Spouse)", relationship: "Spouse" }],
    attendance: { percentage: 85, lastAttended: "Last Sunday" },
    giving: { totalThisYear: 1200, lastGift: "Two weeks ago" },
  }

  return (
    <ModuleLayout
      title={samplePerson.name}
      showSearch={false}
      showRightPanel={true}
      actions={
        <div className="flex items-center gap-3">
          <Button variant="primary" size="default" leftIcon={<Edit3 className="h-4 w-4"/>}>
            Edit Profile
          </Button>
          <DropdownMenu
            trigger={
              <Button variant="glass" size="default" rightIcon={<ChevronDown className="h-4 w-4" />}>
                More Actions
              </Button>
            }
            items={[
              { id: "assign-role", label: "Assign Role", icon: <UserCog className="h-4 w-4" />, onClick: () => console.log("Assign Role clicked") },
              { id: "print-profile", label: "Print Profile", icon: <Printer className="h-4 w-4" />, onClick: () => console.log("Print Profile clicked") },
              { id: "divider", isDivider: true, label:"" },
              { id: "archive-person", label: "Archive Person", icon: <Archive className="h-4 w-4" />, onClick: () => console.log("Archive Person clicked") },
            ]}
            align="end"
          />
        </div>
      }
      // bottomBarContent should be null or omitted to not show the table selection bar
    >
      {/* 
        The main content for this page is the ProfileViewModern.
        ModuleLayout will render SidebarModern and RightPanelModern.
        If RightPanelModern needs to be context-aware of `samplePerson`,
        ModuleLayout would need to be adapted to pass these props, or
        RightPanelModern would need to use a global state/context.
        For this task, we pass person data directly to ProfileViewModern.
      */}
      <ProfileViewModern person={samplePerson} />
    </ModuleLayout>
  )
}
