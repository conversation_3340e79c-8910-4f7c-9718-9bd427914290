"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommand<PERSON>enter from "@/components/ui/floating-command-center"
import { 
  UserGroupIcon,
  ChartBarIcon,
  HeartIcon,
  Squares2X2Icon,
  ListBulletIcon,
  MapIcon,
  ShareIcon,
  EnvelopeIcon,
  PhoneIcon,
  TagIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  UserIcon,
  CalendarIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  BellIcon,
  HomeIcon,
  CheckCircleIcon,
  XCircleIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

type ViewMode = "network" | "directory" | "analytics" | "engagement"
type DisplayMode = "cards" | "table" | "map" | "timeline"

interface FloatingPersonPanel {
  id: string
  person: any
  position: { x: number; y: number }
}

export default function RevolutionaryPeoplePage() {
  const [viewMode, setViewMode] = useState<ViewMode>("network")
  const [displayMode, setDisplayMode] = useState<DisplayMode>("cards")
  const [selectedPeople, setSelectedPeople] = useState<Set<string>>(new Set())
  const [floatingPanels, setFloatingPanels] = useState<FloatingPersonPanel[]>([])
  const [filterBarExpanded, setFilterBarExpanded] = useState(false)

  // Sample data
  const networkStats = {
    totalMembers: 1234,
    activeMembers: 892,
    newThisMonth: 47,
    averageAttendance: 78,
    volunteerRate: 34,
    givingRate: 67
  }

  const engagementMetrics = [
    { category: "Highly Engaged", count: 234, percentage: 19, color: "bg-green-500" },
    { category: "Moderately Engaged", count: 567, percentage: 46, color: "bg-blue-500" },
    { category: "Low Engagement", count: 289, percentage: 23, color: "bg-amber-500" },
    { category: "Inactive", count: 144, percentage: 12, color: "bg-red-500" }
  ]

  const addFloatingPanel = (person: any) => {
    const newPanel: FloatingPersonPanel = {
      id: person.id,
      person,
      position: { x: window.innerWidth / 2, y: window.innerHeight / 2 }
    }
    setFloatingPanels([...floatingPanels, newPanel])
  }

  const removeFloatingPanel = (id: string) => {
    setFloatingPanels(floatingPanels.filter(panel => panel.id !== id))
  }

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarRevolutionary />

      {/* Main Content - NO SCROLLING */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Primary Navigation Bar - Sticky */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] sticky top-0 z-30">
          <div className="h-full px-6 flex items-center justify-between">
            {/* View Mode Switcher */}
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">People</h1>
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "network", label: "Network", icon: ShareIcon },
                  { id: "directory", label: "Directory", icon: UserGroupIcon },
                  { id: "analytics", label: "Analytics", icon: ChartBarIcon },
                  { id: "engagement", label: "Engagement", icon: HeartIcon }
                ].map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => setViewMode(mode.id as ViewMode)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded transition-all",
                      viewMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <mode.icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{mode.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              {/* Display Mode */}
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "cards", icon: Squares2X2Icon },
                  { id: "table", icon: ListBulletIcon },
                  { id: "map", icon: MapIcon },
                  { id: "timeline", icon: CalendarIcon }
                ].map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => setDisplayMode(mode.id as DisplayMode)}
                    className={cn(
                      "p-2 rounded transition-colors",
                      displayMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <mode.icon className="h-4 w-4" />
                  </button>
                ))}
              </div>

              <button
                onClick={() => setFilterBarExpanded(!filterBarExpanded)}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  filterBarExpanded ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]" : "hover:bg-white/10"
                )}
              >
                <FunnelIcon className="h-5 w-5" />
              </button>

              <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Add Person
              </Button>
            </div>
          </div>
        </div>

        {/* Smart Filter Bar - Collapsible */}
        <AnimatePresence>
          {filterBarExpanded && (
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: "auto" }}
              exit={{ height: 0 }}
              className="bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-center gap-4">
                  {/* Search */}
                  <div className="relative flex-1 max-w-md">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-zinc-400" />
                    <input
                      type="text"
                      placeholder="Search by name, email, phone, tags..."
                      className="w-full pl-10 pr-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                    />
                  </div>

                  {/* Quick Filters */}
                  <div className="flex items-center gap-2">
                    {["Active", "New", "Visitors", "Volunteers", "Donors"].map((filter) => (
                      <button
                        key={filter}
                        className="px-3 py-1.5 bg-white/5 hover:bg-white/10 rounded-lg text-sm transition-colors"
                      >
                        {filter}
                      </button>
                    ))}
                  </div>

                  {/* Date Range */}
                  <select className="px-3 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm">
                    <option>Last 30 days</option>
                    <option>Last 90 days</option>
                    <option>This year</option>
                    <option>All time</option>
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Stats Bar - Always Visible */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center">
          <div className="grid grid-cols-6 gap-6 w-full">
            <div className="text-center">
              <p className="text-2xl font-bold">{networkStats.totalMembers}</p>
              <p className="text-xs text-zinc-400">Total Members</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{networkStats.activeMembers}</p>
              <p className="text-xs text-zinc-400">Active</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">+{networkStats.newThisMonth}</p>
              <p className="text-xs text-zinc-400">New This Month</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{networkStats.averageAttendance}%</p>
              <p className="text-xs text-zinc-400">Avg Attendance</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-400">{networkStats.volunteerRate}%</p>
              <p className="text-xs text-zinc-400">Volunteer Rate</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-amber-400">{networkStats.givingRate}%</p>
              <p className="text-xs text-zinc-400">Giving Rate</p>
            </div>
          </div>
        </div>

        {/* Main Content Area - Dynamic Based on View Mode */}
        <div className="flex-1 relative">
          {viewMode === "network" && (
            <div className="h-full p-6">
              {/* Network Visualization */}
              <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 relative">
                <svg className="w-full h-full">
                  {/* Network connections */}
                  <line x1="50%" y1="50%" x2="30%" y2="30%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="70%" y2="30%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="30%" y2="70%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="70%" y2="70%" stroke="var(--border-subtle)" strokeWidth="2" />
                  
                  {/* Center node */}
                  <circle cx="50%" cy="50%" r="60" fill="var(--color-primary)" fillOpacity="0.2" stroke="var(--color-primary)" strokeWidth="2" />
                  <text x="50%" y="50%" textAnchor="middle" fill="white" className="text-sm font-medium">Core Members</text>
                  
                  {/* Surrounding nodes */}
                  <circle cx="30%" cy="30%" r="40" fill="rgba(59, 130, 246, 0.2)" stroke="rgb(59, 130, 246)" strokeWidth="2" />
                  <text x="30%" y="30%" textAnchor="middle" fill="white" className="text-xs">Small Groups</text>
                  
                  <circle cx="70%" cy="30%" r="40" fill="rgba(168, 85, 247, 0.2)" stroke="rgb(168, 85, 247)" strokeWidth="2" />
                  <text x="70%" y="30%" textAnchor="middle" fill="white" className="text-xs">Volunteers</text>
                  
                  <circle cx="30%" cy="70%" r="40" fill="rgba(34, 197, 94, 0.2)" stroke="rgb(34, 197, 94)" strokeWidth="2" />
                  <text x="30%" y="70%" textAnchor="middle" fill="white" className="text-xs">New Members</text>
                  
                  <circle cx="70%" cy="70%" r="40" fill="rgba(251, 146, 60, 0.2)" stroke="rgb(251, 146, 60)" strokeWidth="2" />
                  <text x="70%" y="70%" textAnchor="middle" fill="white" className="text-xs">Youth</text>
                </svg>

                {/* Floating Legend */}
                <div className="absolute top-4 right-4 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-lg p-4">
                  <h3 className="text-sm font-medium mb-2">Network Overview</h3>
                  <div className="space-y-2 text-xs">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-[var(--color-primary)] rounded-full" />
                      <span>Core Members (234)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full" />
                      <span>Small Groups (156)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-purple-500 rounded-full" />
                      <span>Volunteers (89)</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "directory" && displayMode === "cards" && (
            <div className="h-full p-6 grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 auto-rows-min overflow-y-auto custom-scrollbar">
              {Array.from({ length: 20 }, (_, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.02 }}
                  className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4 hover:shadow-glow-lg transition-all cursor-pointer group"
                  onClick={() => addFloatingPanel({ id: `person-${i}`, name: `Person ${i + 1}` })}
                >
                  <div className="flex items-start justify-between mb-3">
                    <Image
                      src="/placeholder-user.jpg"
                      alt="Person"
                      width={48}
                      height={48}
                      className="rounded-full"
                    />
                    <div className="flex gap-1">
                      <button className="p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-white/10 rounded">
                        <EnvelopeIcon className="h-4 w-4" />
                      </button>
                      <button className="p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-white/10 rounded">
                        <PhoneIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <h3 className="font-medium mb-1">John Doe {i + 1}</h3>
                  <p className="text-xs text-zinc-400 mb-3">Member since 2020</p>
                  
                  <div className="grid grid-cols-3 gap-2 text-center mb-3">
                    <div className="bg-white/5 rounded-lg p-2">
                      <p className="text-sm font-bold text-green-400">92%</p>
                      <p className="text-[10px] text-zinc-400">Attendance</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-2">
                      <p className="text-sm font-bold">3</p>
                      <p className="text-[10px] text-zinc-400">Groups</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-2">
                      <CheckCircleIcon className="h-4 w-4 text-green-400 mx-auto" />
                      <p className="text-[10px] text-zinc-400">Active</p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" size="sm">volunteer</Badge>
                    <Badge variant="secondary" size="sm">youth</Badge>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {viewMode === "analytics" && (
            <div className="h-full p-6 grid grid-cols-12 gap-4">
              {/* Engagement Distribution */}
              <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <h3 className="text-lg font-medium mb-4">Engagement Distribution</h3>
                <div className="h-64 relative">
                  {/* Bar Chart */}
                  <div className="absolute inset-0 flex items-end justify-around">
                    {engagementMetrics.map((metric, idx) => (
                      <div key={idx} className="flex-1 mx-2">
                        <div className="text-center mb-2">
                          <p className="text-2xl font-bold">{metric.count}</p>
                          <p className="text-xs text-zinc-400">{metric.percentage}%</p>
                        </div>
                        <div
                          className={cn("rounded-t-lg transition-all", metric.color)}
                          style={{ height: `${metric.percentage * 2}px` }}
                        />
                        <p className="text-xs text-center mt-2">{metric.category}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Quick Insights */}
              <div className="col-span-4 space-y-4">
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium">Growth Trend</h3>
                    <ArrowTrendingUpIcon className="h-5 w-5 text-green-400" />
                  </div>
                  <p className="text-2xl font-bold">+12.3%</p>
                  <p className="text-xs text-zinc-400">vs last quarter</p>
                </div>

                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Top Groups</h3>
                  <div className="space-y-2">
                    {["Young Adults", "Women's Ministry", "Men's Group"].map((group, idx) => (
                      <div key={idx} className="flex items-center justify-between">
                        <span className="text-sm">{group}</span>
                        <Badge variant="secondary" size="sm">{45 - idx * 5}</Badge>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Alerts</h3>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <BellIcon className="h-4 w-4 text-amber-400 flex-shrink-0 mt-0.5" />
                      <p className="text-xs">15 members haven't attended in 30+ days</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <BellIcon className="h-4 w-4 text-blue-400 flex-shrink-0 mt-0.5" />
                      <p className="text-xs">8 new visitors this week</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "engagement" && (
            <div className="h-full p-6">
              <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <h3 className="text-lg font-medium mb-4">Engagement Matrix</h3>
                
                {/* 2x2 Matrix */}
                <div className="h-[calc(100%-3rem)] relative">
                  <div className="absolute inset-0 grid grid-cols-2 grid-rows-2 gap-4">
                    {/* High Frequency, High Recency */}
                    <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-4">
                      <h4 className="text-sm font-medium text-green-400 mb-2">Champions</h4>
                      <p className="text-3xl font-bold mb-1">234</p>
                      <p className="text-xs text-zinc-400">High engagement</p>
                      <div className="mt-4 flex -space-x-2">
                        {[1,2,3,4,5].map(i => (
                          <Image
                            key={i}
                            src="/placeholder-user.jpg"
                            alt="User"
                            width={32}
                            height={32}
                            className="rounded-full border-2 border-[var(--bg-glass)]"
                          />
                        ))}
                        <div className="w-8 h-8 rounded-full bg-white/10 border-2 border-[var(--bg-glass)] flex items-center justify-center">
                          <span className="text-xs">+229</span>
                        </div>
                      </div>
                    </div>

                    {/* Low Frequency, High Recency */}
                    <div className="bg-blue-500/10 border border-blue-500/30 rounded-xl p-4">
                      <h4 className="text-sm font-medium text-blue-400 mb-2">Potential</h4>
                      <p className="text-3xl font-bold mb-1">156</p>
                      <p className="text-xs text-zinc-400">Recently engaged</p>
                    </div>

                    {/* High Frequency, Low Recency */}
                    <div className="bg-amber-500/10 border border-amber-500/30 rounded-xl p-4">
                      <h4 className="text-sm font-medium text-amber-400 mb-2">At Risk</h4>
                      <p className="text-3xl font-bold mb-1">89</p>
                      <p className="text-xs text-zinc-400">Declining engagement</p>
                    </div>

                    {/* Low Frequency, Low Recency */}
                    <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4">
                      <h4 className="text-sm font-medium text-red-400 mb-2">Inactive</h4>
                      <p className="text-3xl font-bold mb-1">67</p>
                      <p className="text-xs text-zinc-400">Need attention</p>
                    </div>
                  </div>

                  {/* Axis Labels */}
                  <div className="absolute left-1/2 bottom-0 -translate-x-1/2 text-xs text-zinc-400">
                    Frequency →
                  </div>
                  <div className="absolute left-0 top-1/2 -translate-y-1/2 -rotate-90 text-xs text-zinc-400">
                    Recency →
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Action Bar - Always Visible */}
        {selectedPeople.size > 0 && (
          <motion.div
            initial={{ y: 100 }}
            animate={{ y: 0 }}
            className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-t border-[var(--border-subtle)] px-6 flex items-center justify-between"
          >
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{selectedPeople.size} selected</Badge>
              <button className="text-sm text-zinc-400 hover:text-white">Clear selection</button>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="glass" size="sm" leftIcon={<EnvelopeIcon className="h-4 w-4" />}>
                Email All
              </Button>
              <Button variant="glass" size="sm" leftIcon={<PhoneIcon className="h-4 w-4" />}>
                SMS All
              </Button>
              <Button variant="glass" size="sm" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
                Add to Group
              </Button>
              <Button variant="glass" size="sm" leftIcon={<TagIcon className="h-4 w-4" />}>
                Tag All
              </Button>
            </div>
          </motion.div>
        )}
      </div>

      {/* Floating Person Panels */}
      {floatingPanels.map((panel) => (
        <motion.div
          key={panel.id}
          drag
          dragMomentum={false}
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="fixed z-40 w-80 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-xl"
          style={{ left: panel.position.x, top: panel.position.y }}
        >
          <div className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <Image
                  src="/placeholder-user.jpg"
                  alt="Person"
                  width={48}
                  height={48}
                  className="rounded-full"
                />
                <div>
                  <h3 className="font-medium">{panel.person.name}</h3>
                  <p className="text-xs text-zinc-400">ID: {panel.person.id}</p>
                </div>
              </div>
              <button
                onClick={() => removeFloatingPanel(panel.id)}
                className="p-1 hover:bg-white/10 rounded transition-colors"
              >
                <XCircleIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="bg-white/5 rounded-lg p-2">
                  <p className="text-lg font-bold text-green-400">92%</p>
                  <p className="text-xs text-zinc-400">Attendance</p>
                </div>
                <div className="bg-white/5 rounded-lg p-2">
                  <p className="text-lg font-bold">$150</p>
                  <p className="text-xs text-zinc-400">Avg Gift</p>
                </div>
                <div className="bg-white/5 rounded-lg p-2">
                  <p className="text-lg font-bold text-purple-400">24h</p>
                  <p className="text-xs text-zinc-400">Volunteer</p>
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="glass" size="sm" className="flex-1">
                  <EnvelopeIcon className="h-4 w-4" />
                </Button>
                <Button variant="glass" size="sm" className="flex-1">
                  <PhoneIcon className="h-4 w-4" />
                </Button>
                <Button variant="primary" size="sm" className="flex-1">
                  View Profile
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      ))}

      {/* Floating Command Center */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}