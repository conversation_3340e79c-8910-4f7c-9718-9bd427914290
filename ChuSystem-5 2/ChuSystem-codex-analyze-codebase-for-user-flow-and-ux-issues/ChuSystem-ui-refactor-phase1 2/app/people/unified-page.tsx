"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import UnifiedLayout from "@/components/ui/unified-layout"
import FloatingPanel from "@/components/ui/floating-panel"
import { AnimatePresence, motion } from "framer-motion"
import { 
  UserGroupIcon,
  Squares2X2Icon,
  MapIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  CalendarDaysIcon,
  HomeIcon,
  BriefcaseIcon,
  HeartIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ClockIcon,
  FireIcon,
  SparklesIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  CameraIcon,
  TagIcon,
  GiftIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  MapPinIcon,
  CakeIcon,
  LinkIcon,
  PencilIcon,
  ShareIcon,
  PrinterIcon,
  ArrowDownTrayIcon,
  ViewColumnsIcon,
  TableCellsIcon,
  CircleStackIcon,
  ArrowsPointingOutIcon
} from "@heroicons/react/24/outline"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

type PeopleMode = "directory" | "network" | "analytics" | "engagement"
type DirectoryView = "grid" | "list" | "compact"

interface Person {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  photo?: string
  status: "active" | "inactive" | "visitor"
  memberSince: string
  lastAttended?: string
  groups: string[]
  tags: string[]
  familyId?: string
  engagementScore: number
}

interface FloatingPersonPanel {
  id: string
  person: Person
  position: { x: number; y: number }
  expanded?: boolean
}

export default function UnifiedPeoplePage() {
  const [peopleMode, setPeopleMode] = useState<PeopleMode>("directory")
  const [directoryView, setDirectoryView] = useState<DirectoryView>("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFilters, setSelectedFilters] = useState<string[]>([])
  const [floatingPanels, setFloatingPanels] = useState<FloatingPersonPanel[]>([])
  const [bulkSelectionMode, setBulkSelectionMode] = useState(false)
  const [selectedPeople, setSelectedPeople] = useState<Set<string>>(new Set())

  // Layout modes
  const layoutModes = [
    { id: "directory", label: "Directory", icon: UserGroupIcon },
    { id: "network", label: "Network", icon: CircleStackIcon },
    { id: "analytics", label: "Analytics", icon: ChartBarIcon },
    { id: "engagement", label: "Engagement", icon: FireIcon, badge: "Live" }
  ]

  // Quick stats
  const quickStats = [
    { label: "Total People", value: 1234, icon: UserGroupIcon, color: "text-blue-400", trend: { value: 5.2, positive: true } },
    { label: "Active Members", value: 847, icon: CheckCircleIcon, color: "text-green-400" },
    { label: "First-Time Visitors", value: 23, icon: SparklesIcon, color: "text-purple-400" },
    { label: "Avg Engagement", value: "78%", icon: FireIcon, color: "text-orange-400", trend: { value: 12, positive: true } },
    { label: "In Groups", value: 567, icon: UserGroupIcon, color: "text-pink-400" },
    { label: "Volunteers", value: 234, icon: HeartIcon, color: "text-red-400" }
  ]

  // Sample people data
  const samplePeople: Person[] = [
    {
      id: "1",
      firstName: "John",
      lastName: "Smith",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      memberSince: "2019-03-15",
      lastAttended: "2024-01-14",
      groups: ["Young Adults", "Volunteer Team"],
      tags: ["volunteer", "worship-team", "small-group-leader"],
      engagementScore: 92
    },
    {
      id: "2",
      firstName: "Sarah",
      lastName: "Johnson",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      memberSince: "2020-06-22",
      lastAttended: "2024-01-14",
      groups: ["Women's Ministry", "Prayer Team"],
      tags: ["member", "donor", "event-volunteer"],
      engagementScore: 85
    },
    {
      id: "3",
      firstName: "Mike",
      lastName: "Davis",
      email: "<EMAIL>",
      phone: "(*************",
      status: "visitor",
      memberSince: "2024-01-07",
      lastAttended: "2024-01-14",
      groups: [],
      tags: ["first-time-visitor", "follow-up-needed"],
      engagementScore: 25
    }
  ]

  const addFloatingPanel = (person: Person) => {
    const existingPanel = floatingPanels.find(p => p.person.id === person.id)
    if (existingPanel) return

    const newPanel: FloatingPersonPanel = {
      id: `person-${person.id}`,
      person,
      position: { 
        x: window.innerWidth / 2 - 200 + floatingPanels.length * 20, 
        y: 200 + floatingPanels.length * 20 
      },
      expanded: false
    }
    setFloatingPanels([...floatingPanels, newPanel])
  }

  const removeFloatingPanel = (id: string) => {
    setFloatingPanels(floatingPanels.filter(p => p.id !== id))
  }

  const togglePanelExpanded = (id: string) => {
    setFloatingPanels(floatingPanels.map(p => 
      p.id === id ? { ...p, expanded: !p.expanded } : p
    ))
  }

  // Top bar actions
  const topBarActions = (
    <>
      {/* Directory View Switcher (only in directory mode) */}
      {peopleMode === "directory" && (
        <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
          {[
            { id: "grid", icon: Squares2X2Icon },
            { id: "list", icon: ViewColumnsIcon },
            { id: "compact", icon: TableCellsIcon }
          ].map((view) => (
            <button
              key={view.id}
              onClick={() => setDirectoryView(view.id as DirectoryView)}
              className={cn(
                "p-2 rounded transition-colors",
                directoryView === view.id
                  ? "bg-[var(--color-primary)] text-black"
                  : "hover:bg-white/10"
              )}
            >
              <view.icon className="h-4 w-4" />
            </button>
          ))}
        </div>
      )}

      {/* Bulk Selection Toggle */}
      <Button 
        variant={bulkSelectionMode ? "primary" : "glass"}
        size="sm" 
        onClick={() => {
          setBulkSelectionMode(!bulkSelectionMode)
          setSelectedPeople(new Set())
        }}
      >
        {bulkSelectionMode ? `${selectedPeople.size} Selected` : "Select"}
      </Button>

      <Button variant="glass" size="sm" leftIcon={<FunnelIcon className="h-4 w-4" />}>
        Filter
      </Button>

      <Button variant="glass" size="sm" leftIcon={<ArrowDownTrayIcon className="h-4 w-4" />}>
        Export
      </Button>

      <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
        Add Person
      </Button>
    </>
  )

  // Dock items
  const dockItems = [
    { id: "profile", label: "Profile", icon: UserIcon, color: "bg-blue-500" },
    { id: "family", label: "Family", icon: HomeIcon, color: "bg-green-500" },
    { id: "groups", label: "Groups", icon: UserGroupIcon, color: "bg-purple-500" },
    { id: "giving", label: "Giving", icon: GiftIcon, color: "bg-amber-500" },
    { id: "attendance", label: "Attendance", icon: CalendarDaysIcon, color: "bg-pink-500" },
    { id: "notes", label: "Notes", icon: DocumentTextIcon, color: "bg-indigo-500" },
    { id: "communicate", label: "Communicate", icon: ChatBubbleLeftRightIcon, color: "bg-cyan-500" },
    { id: "tags", label: "Tags", icon: TagIcon, color: "bg-zinc-600" }
  ]

  // Main content based on mode
  const renderContent = () => {
    switch (peopleMode) {
      case "directory":
        return <DirectoryMode 
          view={directoryView}
          people={samplePeople}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          onPersonClick={addFloatingPanel}
          bulkSelectionMode={bulkSelectionMode}
          selectedPeople={selectedPeople}
          onSelectionChange={setSelectedPeople}
        />
      case "network":
        return <NetworkMode 
          people={samplePeople}
          onPersonClick={addFloatingPanel}
        />
      case "analytics":
        return <AnalyticsMode />
      case "engagement":
        return <EngagementMode 
          people={samplePeople}
          onPersonClick={addFloatingPanel}
        />
      default:
        return null
    }
  }

  // Floating panels
  const floatingPanels_ = (
    <AnimatePresence>
      {floatingPanels.map((panel) => (
        <FloatingPanel
          key={panel.id}
          id={panel.id}
          title={`${panel.person.firstName} ${panel.person.lastName}`}
          subtitle={panel.person.email}
          icon={UserIcon}
          iconColor="bg-blue-500"
          position={panel.position}
          onClose={() => removeFloatingPanel(panel.id)}
          onExpand={() => togglePanelExpanded(panel.id)}
          expanded={panel.expanded}
          width={panel.expanded ? 800 : 400}
          height={panel.expanded ? 600 : undefined}
        >
          <PersonDetails person={panel.person} expanded={panel.expanded} />
        </FloatingPanel>
      ))}
    </AnimatePresence>
  )

  // Right panel - Quick Actions when people are selected
  const rightPanelContent = bulkSelectionMode && selectedPeople.size > 0 ? (
    <BulkActionsPanel 
      selectedCount={selectedPeople.size}
      onClose={() => {
        setBulkSelectionMode(false)
        setSelectedPeople(new Set())
      }}
    />
  ) : null

  return (
    <UnifiedLayout
      productName="People"
      layoutModes={layoutModes}
      currentMode={peopleMode}
      onModeChange={(mode) => setPeopleMode(mode as PeopleMode)}
      topBarActions={topBarActions}
      showStatsBar={true}
      quickStats={quickStats}
      floatingPanels={floatingPanels_}
      showBottomDock={true}
      dockItems={dockItems}
      onDockItemClick={(item) => console.log("Dock item clicked:", item)}
      rightPanel={rightPanelContent}
      rightPanelOpen={bulkSelectionMode && selectedPeople.size > 0}
    >
      {renderContent()}
    </UnifiedLayout>
  )
}

// Directory Mode Component
function DirectoryMode({ 
  view, 
  people, 
  searchQuery, 
  onSearchChange, 
  onPersonClick,
  bulkSelectionMode,
  selectedPeople,
  onSelectionChange
}: any) {
  // Filter people based on search
  const filteredPeople = people.filter((person: Person) => {
    const searchLower = searchQuery.toLowerCase()
    return (
      person.firstName.toLowerCase().includes(searchLower) ||
      person.lastName.toLowerCase().includes(searchLower) ||
      person.email.toLowerCase().includes(searchLower)
    )
  })

  const toggleSelection = (personId: string) => {
    const newSelection = new Set(selectedPeople)
    if (newSelection.has(personId)) {
      newSelection.delete(personId)
    } else {
      newSelection.add(personId)
    }
    onSelectionChange(newSelection)
  }

  if (view === "grid") {
    return (
      <div className="h-full p-6 overflow-hidden">
        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative max-w-2xl">
            <MagnifyingGlassIcon className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-zinc-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder="Search by name, email, phone, or tags..."
              className="w-full pl-12 pr-4 py-3 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
            />
          </div>
        </div>

        {/* People Grid */}
        <div className="h-[calc(100%-100px)] overflow-y-auto custom-scrollbar">
          <div className="grid grid-cols-4 gap-4">
            {filteredPeople.map((person: Person) => (
              <motion.div
                key={person.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className={cn(
                  "relative bg-[var(--bg-glass)] backdrop-blur-xl border rounded-xl p-6 cursor-pointer transition-all",
                  selectedPeople.has(person.id) 
                    ? "border-[var(--color-primary)] shadow-glow" 
                    : "border-[var(--border-subtle)] hover:shadow-glow-lg"
                )}
                onClick={() => !bulkSelectionMode && onPersonClick(person)}
              >
                {/* Selection Checkbox */}
                {bulkSelectionMode && (
                  <div className="absolute top-4 right-4">
                    <input
                      type="checkbox"
                      checked={selectedPeople.has(person.id)}
                      onChange={(e) => {
                        e.stopPropagation()
                        toggleSelection(person.id)
                      }}
                      className="w-5 h-5 rounded border-2 border-white/20 bg-white/10 checked:bg-[var(--color-primary)] checked:border-[var(--color-primary)]"
                    />
                  </div>
                )}

                {/* Profile Picture */}
                <div className="w-20 h-20 rounded-full bg-white/10 mx-auto mb-4 flex items-center justify-center">
                  {person.photo ? (
                    <Image src={person.photo} alt={person.firstName} width={80} height={80} className="rounded-full" />
                  ) : (
                    <UserIcon className="h-10 w-10 text-zinc-400" />
                  )}
                </div>

                {/* Person Info */}
                <h3 className="font-medium text-center mb-1">{person.firstName} {person.lastName}</h3>
                <p className="text-sm text-zinc-400 text-center mb-3">{person.email}</p>

                {/* Status Badge */}
                <div className="flex justify-center mb-3">
                  <Badge 
                    variant="secondary" 
                    size="sm"
                    className={cn(
                      person.status === "active" ? "bg-green-500/20 text-green-400" :
                      person.status === "visitor" ? "bg-amber-500/20 text-amber-400" :
                      "bg-zinc-500/20 text-zinc-400"
                    )}
                  >
                    {person.status}
                  </Badge>
                </div>

                {/* Engagement Score */}
                <div className="flex items-center justify-center gap-2">
                  <FireIcon className="h-4 w-4 text-orange-400" />
                  <span className="text-sm">{person.engagementScore}%</span>
                </div>

                {/* Tags */}
                {person.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-3 justify-center">
                    {person.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" size="sm">
                        {tag}
                      </Badge>
                    ))}
                    {person.tags.length > 2 && (
                      <Badge variant="secondary" size="sm">
                        +{person.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // List view
  return (
    <div className="h-full p-6 overflow-hidden">
      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative max-w-2xl">
          <MagnifyingGlassIcon className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-zinc-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="Search by name, email, phone, or tags..."
            className="w-full pl-12 pr-4 py-3 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
          />
        </div>
      </div>

      {/* People List */}
      <div className="h-[calc(100%-100px)] bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl overflow-hidden">
        <div className="overflow-y-auto h-full custom-scrollbar">
          {filteredPeople.map((person: Person) => (
            <div
              key={person.id}
              className={cn(
                "flex items-center justify-between p-4 border-b border-[var(--border-subtle)] hover:bg-white/5 transition-colors cursor-pointer",
                selectedPeople.has(person.id) && "bg-[var(--color-primary)]/10"
              )}
              onClick={() => !bulkSelectionMode && onPersonClick(person)}
            >
              <div className="flex items-center gap-4">
                {bulkSelectionMode && (
                  <input
                    type="checkbox"
                    checked={selectedPeople.has(person.id)}
                    onChange={(e) => {
                      e.stopPropagation()
                      toggleSelection(person.id)
                    }}
                    className="w-5 h-5 rounded"
                  />
                )}
                
                <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-zinc-400" />
                </div>

                <div>
                  <h3 className="font-medium">{person.firstName} {person.lastName}</h3>
                  <p className="text-sm text-zinc-400">{person.email} • {person.phone}</p>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="text-right">
                  <p className="text-sm">Member since {new Date(person.memberSince).getFullYear()}</p>
                  <p className="text-xs text-zinc-400">Last attended: {person.lastAttended || "Never"}</p>
                </div>

                <Badge 
                  variant="secondary"
                  className={cn(
                    person.status === "active" ? "bg-green-500/20 text-green-400" :
                    person.status === "visitor" ? "bg-amber-500/20 text-amber-400" :
                    "bg-zinc-500/20 text-zinc-400"
                  )}
                >
                  {person.status}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Network Mode Component
function NetworkMode({ people, onPersonClick }: any) {
  return (
    <div className="h-full p-6">
      <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 relative">
        <h3 className="text-lg font-medium mb-4">Relationship Network</h3>
        
        {/* Network Visualization */}
        <svg className="w-full h-full">
          {/* Connection lines */}
          <line x1="50%" y1="50%" x2="30%" y2="30%" stroke="var(--border-subtle)" strokeWidth="2" />
          <line x1="50%" y1="50%" x2="70%" y2="30%" stroke="var(--border-subtle)" strokeWidth="2" />
          <line x1="50%" y1="50%" x2="30%" y2="70%" stroke="var(--border-subtle)" strokeWidth="2" />
          <line x1="50%" y1="50%" x2="70%" y2="70%" stroke="var(--border-subtle)" strokeWidth="2" />
          
          {/* Center Hub */}
          <g transform="translate(50%, 50%)">
            <circle cx="0" cy="0" r="80" fill="var(--color-primary)" fillOpacity="0.2" stroke="var(--color-primary)" strokeWidth="2" />
            <BuildingOfficeIcon className="h-10 w-10 text-white" x="-20" y="-20" />
            <text x="0" y="35" textAnchor="middle" fill="white" className="text-sm font-medium">Church Community</text>
          </g>
          
          {/* Family Clusters */}
          <g transform="translate(30%, 30%)">
            <circle cx="0" cy="0" r="60" fill="rgba(59, 130, 246, 0.2)" stroke="rgb(59, 130, 246)" strokeWidth="2" className="cursor-pointer hover:fill-opacity-30" />
            <HomeIcon className="h-8 w-8 text-white" x="-16" y="-16" />
            <text x="0" y="30" textAnchor="middle" fill="white" className="text-xs">Smith Family</text>
          </g>
          
          <g transform="translate(70%, 30%)">
            <circle cx="0" cy="0" r="60" fill="rgba(34, 197, 94, 0.2)" stroke="rgb(34, 197, 94)" strokeWidth="2" className="cursor-pointer hover:fill-opacity-30" />
            <HomeIcon className="h-8 w-8 text-white" x="-16" y="-16" />
            <text x="0" y="30" textAnchor="middle" fill="white" className="text-xs">Johnson Family</text>
          </g>
          
          {/* Individual nodes */}
          {people.slice(0, 8).map((person: Person, idx: number) => {
            const angle = (idx / 8) * 2 * Math.PI
            const x = 50 + 35 * Math.cos(angle)
            const y = 50 + 35 * Math.sin(angle)
            
            return (
              <g 
                key={person.id} 
                transform={`translate(${x}%, ${y}%)`}
                className="cursor-pointer"
                onClick={() => onPersonClick(person)}
              >
                <circle 
                  cx="0" 
                  cy="0" 
                  r="30" 
                  fill={person.status === "active" ? "rgba(34, 197, 94, 0.2)" : "rgba(251, 146, 60, 0.2)"} 
                  stroke={person.status === "active" ? "rgb(34, 197, 94)" : "rgb(251, 146, 60)"} 
                  strokeWidth="2" 
                  className="hover:fill-opacity-30 transition-all"
                />
                <UserIcon className="h-4 w-4 text-white" x="-8" y="-8" />
                <text x="0" y="20" textAnchor="middle" fill="white" className="text-xs">
                  {person.firstName}
                </text>
              </g>
            )
          })}
        </svg>

        {/* Legend */}
        <div className="absolute top-4 right-4 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-lg p-4">
          <h4 className="text-sm font-medium mb-2">Network Key</h4>
          <div className="space-y-2 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full" />
              <span>Active Members</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-amber-500 rounded-full" />
              <span>Visitors</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full" />
              <span>Family Groups</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Analytics Mode Component
function AnalyticsMode() {
  return (
    <div className="h-full p-6 overflow-y-auto custom-scrollbar">
      <div className="grid grid-cols-12 gap-4">
        {/* Engagement Overview */}
        <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
          <h3 className="text-lg font-medium mb-4">Engagement Analytics</h3>
          
          {/* Engagement Chart */}
          <div className="h-64 relative mb-6">
            <div className="absolute inset-0 flex items-end justify-around">
              {Array.from({ length: 12 }, (_, i) => (
                <div key={i} className="flex-1 mx-1 flex flex-col items-center">
                  <div 
                    className="w-full bg-[var(--color-primary)] rounded-t-lg transition-all hover:opacity-80" 
                    style={{ height: `${Math.random() * 80 + 20}%` }} 
                  />
                  <p className="text-xs mt-2">W{i + 1}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center p-3 bg-white/5 rounded-lg">
              <ArrowTrendingUpIcon className="h-8 w-8 mx-auto mb-2 text-green-400" />
              <p className="text-2xl font-bold">87%</p>
              <p className="text-xs text-zinc-400">Avg Engagement</p>
            </div>
            <div className="text-center p-3 bg-white/5 rounded-lg">
              <UserGroupIcon className="h-8 w-8 mx-auto mb-2 text-blue-400" />
              <p className="text-2xl font-bold">67%</p>
              <p className="text-xs text-zinc-400">In Small Groups</p>
            </div>
            <div className="text-center p-3 bg-white/5 rounded-lg">
              <HeartIcon className="h-8 w-8 mx-auto mb-2 text-pink-400" />
              <p className="text-2xl font-bold">34%</p>
              <p className="text-xs text-zinc-400">Volunteers</p>
            </div>
            <div className="text-center p-3 bg-white/5 rounded-lg">
              <GiftIcon className="h-8 w-8 mx-auto mb-2 text-amber-400" />
              <p className="text-2xl font-bold">56%</p>
              <p className="text-xs text-zinc-400">Regular Givers</p>
            </div>
          </div>
        </div>

        {/* Insights */}
        <div className="col-span-4 space-y-4">
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Key Insights</h3>
            <div className="space-y-3">
              <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                <div className="flex items-start gap-2">
                  <ArrowTrendingUpIcon className="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-green-400">Growing Engagement</p>
                    <p className="text-xs mt-1">15% increase in attendance over last quarter</p>
                  </div>
                </div>
              </div>
              <div className="p-3 bg-amber-500/10 border border-amber-500/30 rounded-lg">
                <div className="flex items-start gap-2">
                  <ExclamationTriangleIcon className="h-4 w-4 text-amber-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-amber-400">Follow-up Needed</p>
                    <p className="text-xs mt-1">23 first-time visitors from last Sunday</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Demographics</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-zinc-400">Age 18-25</span>
                <span className="text-sm font-medium">18%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-zinc-400">Age 26-40</span>
                <span className="text-sm font-medium">35%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-zinc-400">Age 41-60</span>
                <span className="text-sm font-medium">32%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-zinc-400">Age 60+</span>
                <span className="text-sm font-medium">15%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Engagement Mode Component
function EngagementMode({ people, onPersonClick }: any) {
  return (
    <div className="h-full p-6">
      <div className="grid grid-cols-12 gap-4 h-full">
        {/* Engagement Matrix */}
        <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
          <h3 className="text-lg font-medium mb-4">Engagement Matrix</h3>
          
          {/* 2x2 Matrix */}
          <div className="h-[calc(100%-3rem)] relative">
            <div className="absolute inset-0 grid grid-cols-2 grid-rows-2 gap-4">
              {/* High Engagement, High Attendance */}
              <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-6">
                <h4 className="text-sm font-medium text-green-400 mb-2">Champions</h4>
                <p className="text-3xl font-bold mb-1">234</p>
                <p className="text-xs text-zinc-400">Highly engaged, regular attendance</p>
                <div className="mt-4 space-y-2">
                  {people.filter((p: Person) => p.engagementScore > 80).slice(0, 3).map((person: Person) => (
                    <div 
                      key={person.id}
                      className="flex items-center gap-2 p-2 bg-white/5 rounded-lg cursor-pointer hover:bg-white/10"
                      onClick={() => onPersonClick(person)}
                    >
                      <UserIcon className="h-4 w-4" />
                      <span className="text-sm">{person.firstName} {person.lastName}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Low Engagement, High Attendance */}
              <div className="bg-blue-500/10 border border-blue-500/30 rounded-xl p-6">
                <h4 className="text-sm font-medium text-blue-400 mb-2">Regular Attenders</h4>
                <p className="text-3xl font-bold mb-1">156</p>
                <p className="text-xs text-zinc-400">Consistent but less engaged</p>
              </div>

              {/* High Engagement, Low Attendance */}
              <div className="bg-purple-500/10 border border-purple-500/30 rounded-xl p-6">
                <h4 className="text-sm font-medium text-purple-400 mb-2">Remote Engaged</h4>
                <p className="text-3xl font-bold mb-1">89</p>
                <p className="text-xs text-zinc-400">Engaged online/groups</p>
              </div>

              {/* Low Engagement, Low Attendance */}
              <div className="bg-amber-500/10 border border-amber-500/30 rounded-xl p-6">
                <h4 className="text-sm font-medium text-amber-400 mb-2">At Risk</h4>
                <p className="text-3xl font-bold mb-1">67</p>
                <p className="text-xs text-zinc-400">Need follow-up</p>
              </div>
            </div>

            {/* Axis Labels */}
            <div className="absolute left-1/2 bottom-0 -translate-x-1/2 text-xs text-zinc-400">
              Attendance →
            </div>
            <div className="absolute left-0 top-1/2 -translate-y-1/2 -rotate-90 text-xs text-zinc-400">
              Engagement →
            </div>
          </div>
        </div>

        {/* Live Activity Feed */}
        <div className="col-span-4 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium">Live Activity</h3>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-xs text-green-400">Live</span>
            </div>
          </div>

          <div className="space-y-2 max-h-[500px] overflow-y-auto custom-scrollbar">
            {[
              { name: "John Smith", action: "checked in", time: "2 min ago" },
              { name: "Sarah Johnson", action: "joined small group", time: "15 min ago" },
              { name: "Mike Davis", action: "completed profile", time: "1 hour ago" },
              { name: "Emily Chen", action: "signed up to volunteer", time: "2 hours ago" }
            ].map((activity, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: idx * 0.1 }}
                className="p-2 bg-white/5 rounded-lg"
              >
                <p className="text-sm">
                  <span className="font-medium">{activity.name}</span> {activity.action}
                </p>
                <p className="text-xs text-zinc-400">{activity.time}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Person Details Component
function PersonDetails({ person, expanded }: { person: Person; expanded: boolean }) {
  if (!expanded) {
    // Compact view
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-white/5 rounded-lg p-3">
            <p className="text-xs text-zinc-400 mb-1">Phone</p>
            <p className="font-medium">{person.phone}</p>
          </div>
          <div className="bg-white/5 rounded-lg p-3">
            <p className="text-xs text-zinc-400 mb-1">Member Since</p>
            <p className="font-medium">{new Date(person.memberSince).toLocaleDateString()}</p>
          </div>
        </div>

        <div className="bg-white/5 rounded-lg p-3">
          <p className="text-xs text-zinc-400 mb-2">Groups</p>
          <div className="flex flex-wrap gap-2">
            {person.groups.length > 0 ? (
              person.groups.map((group) => (
                <Badge key={group} variant="secondary">
                  {group}
                </Badge>
              ))
            ) : (
              <span className="text-sm text-zinc-400">No groups</span>
            )}
          </div>
        </div>

        <div className="bg-white/5 rounded-lg p-3">
          <p className="text-xs text-zinc-400 mb-2">Tags</p>
          <div className="flex flex-wrap gap-2">
            {person.tags.map((tag) => (
              <Badge key={tag} variant="secondary" size="sm">
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        <div className="flex gap-2">
          <Button variant="glass" size="sm" className="flex-1">
            <EnvelopeIcon className="h-4 w-4" />
          </Button>
          <Button variant="glass" size="sm" className="flex-1">
            <PhoneIcon className="h-4 w-4" />
          </Button>
          <Button variant="primary" size="sm" className="flex-1">
            View Full Profile
          </Button>
        </div>
      </div>
    )
  }

  // Expanded view
  return (
    <div className="grid grid-cols-12 gap-4">
      {/* Left Column - Basic Info */}
      <div className="col-span-4 space-y-4">
        <div className="text-center">
          <div className="w-32 h-32 rounded-full bg-white/10 mx-auto mb-4 flex items-center justify-center">
            <UserIcon className="h-16 w-16 text-zinc-400" />
          </div>
          <h2 className="text-xl font-bold mb-1">{person.firstName} {person.lastName}</h2>
          <p className="text-zinc-400">{person.email}</p>
          <p className="text-zinc-400">{person.phone}</p>
        </div>

        <div className="space-y-2">
          <Button variant="primary" size="sm" className="w-full">
            <EnvelopeIcon className="h-4 w-4 mr-2" />
            Send Message
          </Button>
          <Button variant="glass" size="sm" className="w-full">
            <PhoneIcon className="h-4 w-4 mr-2" />
            Call
          </Button>
        </div>
      </div>

      {/* Middle Column - Details */}
      <div className="col-span-4 space-y-4">
        <div>
          <h3 className="font-medium mb-3">Engagement</h3>
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-zinc-400">Overall Score</span>
                <span className="text-sm font-medium">{person.engagementScore}%</span>
              </div>
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-[var(--color-primary)] transition-all"
                  style={{ width: `${person.engagementScore}%` }}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <CalendarDaysIcon className="h-6 w-6 mx-auto mb-1 text-blue-400" />
                <p className="text-sm font-medium">12</p>
                <p className="text-xs text-zinc-400">Attendances</p>
              </div>
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <GiftIcon className="h-6 w-6 mx-auto mb-1 text-amber-400" />
                <p className="text-sm font-medium">$250</p>
                <p className="text-xs text-zinc-400">Monthly Avg</p>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-3">Groups & Activities</h3>
          <div className="space-y-2">
            {person.groups.map((group) => (
              <div key={group} className="flex items-center justify-between p-2 bg-white/5 rounded-lg">
                <span className="text-sm">{group}</span>
                <CheckCircleIcon className="h-4 w-4 text-green-400" />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Column - Timeline */}
      <div className="col-span-4">
        <h3 className="font-medium mb-3">Recent Activity</h3>
        <div className="space-y-3">
          {[
            { date: "Jan 14", activity: "Attended Sunday Service" },
            { date: "Jan 10", activity: "Joined Small Group" },
            { date: "Jan 7", activity: "First time visitor" },
            { date: "Jan 7", activity: "Completed connection card" }
          ].map((item, idx) => (
            <div key={idx} className="flex items-start gap-3">
              <div className="w-2 h-2 bg-[var(--color-primary)] rounded-full mt-1.5" />
              <div className="flex-1">
                <p className="text-sm">{item.activity}</p>
                <p className="text-xs text-zinc-400">{item.date}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Bulk Actions Panel
function BulkActionsPanel({ selectedCount, onClose }: { selectedCount: number; onClose: () => void }) {
  return (
    <div className="h-full flex flex-col p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Bulk Actions</h3>
        <button onClick={onClose} className="p-1 hover:bg-white/10 rounded">
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      <p className="text-sm text-zinc-400 mb-4">{selectedCount} people selected</p>

      <div className="space-y-3">
        <Button variant="glass" size="sm" className="w-full justify-start">
          <EnvelopeIcon className="h-4 w-4 mr-2" />
          Send Email
        </Button>
        <Button variant="glass" size="sm" className="w-full justify-start">
          <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
          Send SMS
        </Button>
        <Button variant="glass" size="sm" className="w-full justify-start">
          <TagIcon className="h-4 w-4 mr-2" />
          Add Tags
        </Button>
        <Button variant="glass" size="sm" className="w-full justify-start">
          <UserGroupIcon className="h-4 w-4 mr-2" />
          Add to Group
        </Button>
        <Button variant="glass" size="sm" className="w-full justify-start">
          <PrinterIcon className="h-4 w-4 mr-2" />
          Print Labels
        </Button>
        <Button variant="glass" size="sm" className="w-full justify-start">
          <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
          Export Data
        </Button>
      </div>
    </div>
  )
}