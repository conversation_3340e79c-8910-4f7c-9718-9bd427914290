@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 15, 15, 15;
  --background-end-rgb: 0, 0, 0;

  /* Custom color variables */
  --color-primary: 250, 204, 21; /* Yellow accent */
  --color-primary-light: 254, 240, 138;
  --color-primary-dark: 245, 158, 11;

  --color-purple: 139, 92, 246;
  --color-green: 34, 197, 94;
  --color-amber: 245, 158, 11;
  --color-red: 239, 68, 68;

  /* Glassmorphic design variables */
  --bg-glass: rgba(255, 255, 255, 0.03);
  --bg-glass-hover: rgba(255, 255, 255, 0.08);
  --bg-sidebar: rgba(0, 0, 0, 0.4);
  --bg-card: rgba(0, 0, 0, 0.3);
  
  --border-subtle: rgba(255, 255, 255, 0.05);
  --border-light: rgba(255, 255, 255, 0.1);
}

html {
  color-scheme: dark;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(135deg, rgb(var(--background-start-rgb)), rgb(var(--background-end-rgb)));
  min-height: 100vh;
}

@layer base {
  /* Custom Scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.15);
  }

  /* Custom selection color */
  ::selection {
    background-color: rgba(var(--color-primary), 0.3);
  }

  /* Focus styles */
  :focus-visible {
    outline: 2px solid rgba(var(--color-primary), 0.6);
    outline-offset: 2px;
  }
}

@layer components {
  /* Modern Card styles */
  .card-modern {
    @apply bg-white/[0.03] backdrop-blur-xl border border-white/5 rounded-2xl;
    @apply hover:bg-white/[0.05] hover:border-white/10 transition-all;
  }

  /* Profile card styles */
  .profile-card {
    @apply bg-black/40 backdrop-blur-xl border border-white/10 rounded-3xl shadow-2xl overflow-hidden;
  }

  /* Metric card */
  .metric-card {
    @apply bg-black/30 backdrop-blur-md border border-white/10 rounded-2xl p-6;
    @apply hover:bg-black/40 hover:border-white/20 transition-all;
  }

  /* Action button styles */
  .action-button {
    @apply flex items-center justify-center gap-2 px-6 py-3 rounded-2xl transition-all;
    @apply bg-white/5 border border-white/10 backdrop-blur-sm;
    @apply hover:bg-white/10 hover:border-white/20 hover:scale-105;
  }

  .action-button-primary {
    @apply bg-yellow-400/20 border-yellow-400/30;
    @apply hover:bg-yellow-400/30 hover:border-yellow-400/40;
    @apply text-yellow-400;
  }

  /* Icon button */
  .icon-button {
    @apply w-12 h-12 rounded-2xl flex items-center justify-center;
    @apply bg-white/5 border border-white/10;
    @apply hover:bg-white/10 hover:border-white/20 transition-all;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-full;
    @apply bg-black/30 border border-white/10;
  }

  .badge-primary {
    @apply bg-yellow-400/20 text-yellow-400 border-yellow-400/30;
  }

  .badge-success {
    @apply bg-green-400/20 text-green-400 border-green-400/30;
  }

  .badge-warning {
    @apply bg-amber-400/20 text-amber-400 border-amber-400/30;
  }

  .badge-danger {
    @apply bg-red-400/20 text-red-400 border-red-400/30;
  }

  /* Tab styles */
  .tab-modern {
    @apply px-6 py-3 rounded-xl text-sm font-medium transition-all;
    @apply text-zinc-400 hover:text-white;
  }

  .tab-modern-active {
    @apply bg-white/10 text-white;
  }

  /* Input styles */
  .input-modern {
    @apply w-full px-4 py-3 rounded-xl bg-white/5 border border-white/10;
    @apply placeholder:text-zinc-500 text-white;
    @apply focus:bg-white/10 focus:border-white/20 focus:outline-none;
    @apply transition-all;
  }

  /* Search bar */
  .search-modern {
    @apply flex items-center gap-3 px-4 py-3 rounded-2xl;
    @apply bg-white/5 border border-white/10;
    @apply hover:bg-white/10 hover:border-white/20;
    @apply transition-all;
  }

  /* Sidebar item */
  .sidebar-item-modern {
    @apply flex items-center gap-3 px-3 py-2.5 rounded-xl;
    @apply text-zinc-400 hover:text-white;
    @apply hover:bg-white/5 transition-all;
  }

  .sidebar-item-modern-active {
    @apply bg-yellow-400/15 text-yellow-400;
    @apply border border-yellow-400/30;
  }

  /* Contact list item */
  .contact-item {
    @apply flex items-center gap-3 px-3 py-3 rounded-xl;
    @apply hover:bg-white/5 transition-all cursor-pointer;
  }

  .contact-item-active {
    @apply bg-yellow-400/10 border border-yellow-400/20;
  }

  /* Data visualization card */
  .data-card {
    @apply relative overflow-hidden rounded-2xl p-6;
    @apply bg-gradient-to-br from-black/40 to-black/20;
    @apply border border-white/10;
  }

  /* Shadow styles */
  .shadow-glow {
    box-shadow: 0 0 40px rgba(250, 204, 21, 0.1);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 60px rgba(250, 204, 21, 0.15);
  }

  /* Glass morphism enhanced */
  .glass-enhanced {
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Layout utilities */
  .layout-modern {
    @apply grid grid-cols-[280px_1fr_320px] h-screen overflow-hidden;
  }

  .sidebar-modern {
    @apply bg-black/40 backdrop-blur-2xl border-r border-white/5;
    @apply flex flex-col h-full;
  }

  .main-content-modern {
    @apply bg-transparent overflow-auto;
  }

  .right-panel-modern {
    @apply bg-black/40 backdrop-blur-2xl border-l border-white/5;
    @apply flex flex-col h-full;
  }
}

/* Animation delay utilities */
.animation-delay-0 {
  animation-delay: 0ms;
}
.animation-delay-100 {
  animation-delay: 100ms;
}
.animation-delay-150 {
  animation-delay: 150ms;
}
.animation-delay-200 {
  animation-delay: 200ms;
}
.animation-delay-300 {
  animation-delay: 300ms;
}
.animation-delay-500 {
  animation-delay: 500ms;
}
.animation-delay-700 {
  animation-delay: 700ms;
}
.animation-delay-1000 {
  animation-delay: 1000ms;
}

/* Smooth transitions */
* {
  transition-property: background-color, border-color, transform, box-shadow, color, opacity;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern rounded styles */
.rounded-modern {
  border-radius: 1.5rem;
}

.rounded-modern-sm {
  border-radius: 1rem;
}

.rounded-modern-lg {
  border-radius: 2rem;
}

/* Content section utilities */
.content-section {
  @apply space-y-6;
}

.section-title {
  @apply text-xl font-semibold text-white flex items-center gap-2;
}

/* Hover lift effect */
.hover-lift {
  @apply hover:scale-[1.02] hover:shadow-glow-lg;
}

/* Responsive adjustments */
@media (max-width: 1280px) {
  .layout-modern {
    @apply grid-cols-[240px_1fr_280px];
  }
}

@media (max-width: 1024px) {
  .layout-modern {
    @apply grid-cols-[60px_1fr];
  }

  .right-panel-modern {
    @apply hidden;
  }
}

@media (max-width: 768px) {
  .layout-modern {
    @apply grid-cols-1;
  }

  .sidebar-modern {
    @apply hidden;
  }
}
