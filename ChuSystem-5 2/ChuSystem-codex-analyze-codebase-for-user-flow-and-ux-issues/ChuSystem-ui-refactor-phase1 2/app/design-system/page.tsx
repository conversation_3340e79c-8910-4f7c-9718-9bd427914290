"use client"
import React from "react"
import { TabGroup } from "@/components/ui/tab-group"
import { DropdownMenu } from "@/components/ui/dropdown-menu"
import { Pencil, Copy, Archive, Trash, MessageSquare, Share2 } from "lucide-react"

export default function DesignSystemPage() {
  // Sample data for tabs
  const tabItems = [
    {
      name: "Recent",
      label: "Recent", // Added label
      content: (
        <ul>
          {[
            {
              id: 1,
              title: "Sunday Service Planning",
              date: "5h ago",
              commentCount: 5,
              shareCount: 2,
            },
            {
              id: 2,
              title: "Youth Group Event",
              date: "2h ago",
              commentCount: 3,
              shareCount: 2,
            },
          ].map((post) => (
            <li key={post.id} className="relative rounded-md p-3 text-sm/6 transition hover:bg-white/5">
              <a href="#" className="font-semibold text-white">
                <span className="absolute inset-0" />
                {post.title}
              </a>
              <ul className="flex gap-2 text-white/50" aria-hidden="true">
                <li>{post.date}</li>
                <li aria-hidden="true">&middot;</li>
                <li>{post.commentCount} comments</li>
                <li aria-hidden="true">&middot;</li>
                <li>{post.shareCount} shares</li>
              </ul>
            </li>
          ))}
        </ul>
      ),
    },
    {
      name: "Popular",
      label: "Popular", // Added label
      content: (
        <ul>
          {[
            {
              id: 1,
              title: "Christmas Service Planning",
              date: "Jan 7",
              commentCount: 29,
              shareCount: 16,
            },
            {
              id: 2,
              title: "Worship Team Schedule",
              date: "Mar 19",
              commentCount: 24,
              shareCount: 12,
            },
          ].map((post) => (
            <li key={post.id} className="relative rounded-md p-3 text-sm/6 transition hover:bg-white/5">
              <a href="#" className="font-semibold text-white">
                <span className="absolute inset-0" />
                {post.title}
              </a>
              <ul className="flex gap-2 text-white/50" aria-hidden="true">
                <li>{post.date}</li>
                <li aria-hidden="true">&middot;</li>
                <li>{post.commentCount} comments</li>
                <li aria-hidden="true">&middot;</li>
                <li>{post.shareCount} shares</li>
              </ul>
            </li>
          ))}
        </ul>
      ),
    },
    {
      name: "Trending",
      label: "Trending", // Added label
      content: (
        <ul>
          {[
            {
              id: 1,
              title: "10 Tips for Better Sermons",
              date: "2d ago",
              commentCount: 9,
              shareCount: 5,
            },
            {
              id: 2,
              title: "Volunteer Management Best Practices",
              date: "4d ago",
              commentCount: 1,
              shareCount: 2,
            },
          ].map((post) => (
            <li key={post.id} className="relative rounded-md p-3 text-sm/6 transition hover:bg-white/5">
              <a href="#" className="font-semibold text-white">
                <span className="absolute inset-0" />
                {post.title}
              </a>
              <ul className="flex gap-2 text-white/50" aria-hidden="true">
                <li>{post.date}</li>
                <li aria-hidden="true">&middot;</li>
                <li>{post.commentCount} comments</li>
                <li aria-hidden="true">&middot;</li>
                <li>{post.shareCount} shares</li>
              </ul>
            </li>
          ))}
        </ul>
      ),
    },
  ]

  // Sample data for dropdown menu
  const dropdownItems = [
    {
      id: "edit",
      label: "Edit",
      icon: <Pencil className="size-4" />,
      onClick: () => console.log("Edit clicked"),
      shortcut: "⌘E",
    },
    {
      id: "duplicate",
      label: "Duplicate",
      icon: <Copy className="size-4" />,
      onClick: () => console.log("Duplicate clicked"),
      shortcut: "⌘D",
    },
    {
      id: "divider1",
      label: "",
      isDivider: true,
    },
    {
      id: "archive",
      label: "Archive",
      icon: <Archive className="size-4" />,
      onClick: () => console.log("Archive clicked"),
      shortcut: "⌘A",
    },
    {
      id: "delete",
      label: "Delete",
      icon: <Trash className="size-4" />,
      onClick: () => console.log("Delete clicked"),
      shortcut: "⌘⌫",
    },
  ]

  return (
    <div className="min-h-screen bg-zinc-900 p-8">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-white">Design System</h1>

        <div className="mb-12">
          <h2 className="mb-4 text-xl font-semibold text-white">Tab Group</h2>
          <div className="rounded-xl bg-zinc-800/50 p-6">
            <TabGroup tabs={tabItems} />
          </div>
        </div>

        <div className="mb-12">
          <h2 className="mb-4 text-xl font-semibold text-white">Dropdown Menu</h2>
          <div className="rounded-xl bg-zinc-800/50 p-6">
            <DropdownMenu trigger="Options" items={dropdownItems} />
          </div>
        </div>

        <div className="mb-12">
          <h2 className="mb-4 text-xl font-semibold text-white">Combined Example</h2>
          <div className="rounded-xl bg-zinc-800/50 p-6">
            <div className="flex items-center justify-between">
              <TabGroup
                tabs={tabItems.map((tab) => ({
                  ...tab,
                  content: null,
                }))}
                className="w-auto"
              />
              <DropdownMenu trigger="Actions" items={dropdownItems} />
            </div>
            <div className="mt-4 rounded-xl bg-white/5 p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-white">Service Planning</h3>
                <div className="flex items-center gap-2">
                  <button className="rounded-full bg-white/10 p-1.5 text-white hover:bg-white/20">
                    <MessageSquare className="size-4" />
                  </button>
                  <button className="rounded-full bg-white/10 p-1.5 text-white hover:bg-white/20">
                    <Share2 className="size-4" />
                  </button>
                </div>
              </div>
              <p className="mt-2 text-sm text-white/70">
                Plan your upcoming services with our intuitive tools. Organize worship, sermons, and volunteer schedules
                all in one place.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
