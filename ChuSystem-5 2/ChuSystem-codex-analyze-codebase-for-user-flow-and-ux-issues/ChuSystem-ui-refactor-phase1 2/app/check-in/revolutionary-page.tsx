"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommand<PERSON>enter from "@/components/ui/floating-command-center"
import { 
  QrCodeIcon,
  PhoneIcon,
  MagnifyingGlassIcon,
  UserGroupIcon,
  ClockIcon,
  CheckCircleIcon,
  PrinterIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  HomeIcon,
  UserIcon,
  HashtagIcon,
  FingerPrintIcon,
  CreditCardIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  MapPinIcon,
  BellIcon,
  SparklesIcon,
  FireIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  CheckIcon,
  PlusIcon,
  MinusIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

type ViewMode = "kiosk" | "dashboard" | "manager" | "analytics"
type CheckInMethod = "phone" | "search" | "qr" | "face"
type CheckInStep = "method" | "selection" | "confirmation" | "success"

interface CheckInStats {
  totalCheckedIn: number
  expectedTotal: number
  newFamilies: number
  volunteers: number
  checkInRate: number
  avgCheckInTime: string
}

interface Family {
  id: string
  lastName: string
  members: FamilyMember[]
  lastCheckIn: string
}

interface FamilyMember {
  id: string
  firstName: string
  lastName: string
  age: number
  photo?: string
  allergies?: string[]
  specialNeeds?: string[]
  checkedIn?: boolean
}

export default function RevolutionaryCheckInPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("kiosk")
  const [checkInMethod, setCheckInMethod] = useState<CheckInMethod | null>(null)
  const [checkInStep, setCheckInStep] = useState<CheckInStep>("method")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null)
  const [selectedMembers, setSelectedMembers] = useState<Set<string>>(new Set())
  const [printingLabels, setPrintingLabels] = useState(false)

  // Sample data
  const stats: CheckInStats = {
    totalCheckedIn: 234,
    expectedTotal: 450,
    newFamilies: 12,
    volunteers: 45,
    checkInRate: 52,
    avgCheckInTime: "45s"
  }

  const sampleFamilies: Family[] = [
    {
      id: "1",
      lastName: "Johnson",
      lastCheckIn: "Last week",
      members: [
        { id: "1-1", firstName: "John", lastName: "Johnson", age: 35 },
        { id: "1-2", firstName: "Sarah", lastName: "Johnson", age: 33 },
        { id: "1-3", firstName: "Emma", lastName: "Johnson", age: 8, allergies: ["Peanuts"] },
        { id: "1-4", firstName: "Liam", lastName: "Johnson", age: 5 }
      ]
    },
    {
      id: "2",
      lastName: "Smith",
      lastCheckIn: "2 weeks ago",
      members: [
        { id: "2-1", firstName: "Michael", lastName: "Smith", age: 42 },
        { id: "2-2", firstName: "Lisa", lastName: "Smith", age: 40 },
        { id: "2-3", firstName: "Sophia", lastName: "Smith", age: 12 }
      ]
    }
  ]

  const handlePhoneInput = (digit: string) => {
    if (digit === "back") {
      setPhoneNumber(phoneNumber.slice(0, -1))
    } else if (phoneNumber.length < 10) {
      setPhoneNumber(phoneNumber + digit)
    }
  }

  const formatPhoneNumber = (phone: string) => {
    if (phone.length <= 3) return phone
    if (phone.length <= 6) return `(${phone.slice(0, 3)}) ${phone.slice(3)}`
    return `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`
  }

  const handleMemberToggle = (memberId: string) => {
    const newSelected = new Set(selectedMembers)
    if (newSelected.has(memberId)) {
      newSelected.delete(memberId)
    } else {
      newSelected.add(memberId)
    }
    setSelectedMembers(newSelected)
  }

  const handleCheckIn = async () => {
    setPrintingLabels(true)
    // Simulate printing delay
    setTimeout(() => {
      setCheckInStep("success")
      setPrintingLabels(false)
      // Reset after 5 seconds
      setTimeout(() => {
        setCheckInStep("method")
        setCheckInMethod(null)
        setSelectedFamily(null)
        setSelectedMembers(new Set())
        setPhoneNumber("")
        setSearchQuery("")
      }, 5000)
    }, 2000)
  }

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar - Hidden in Kiosk Mode */}
      {viewMode !== "kiosk" && <SidebarRevolutionary />}

      {/* Main Content */}
      <div className={cn("flex-1 flex flex-col", viewMode !== "kiosk" && "ml-80")}>
        {/* Navigation Bar - Different for Kiosk Mode */}
        {viewMode === "kiosk" ? (
          <div className="h-20 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)]">
            <div className="h-full px-8 flex items-center justify-between">
              <div className="flex items-center gap-6">
                <h1 className="text-3xl font-bold">Welcome to ChuSystem Church</h1>
                <Badge variant="secondary" size="lg" className="bg-green-500/20 text-green-400">
                  Sunday Service
                </Badge>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <p className="text-2xl font-bold">{new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
                  <p className="text-sm text-zinc-400">Check-in Open</p>
                </div>
                <button
                  onClick={() => setViewMode("dashboard")}
                  className="p-3 hover:bg-white/10 rounded-lg transition-colors"
                >
                  <ComputerDesktopIcon className="h-6 w-6" />
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)]">
            <div className="h-full px-6 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h1 className="text-xl font-bold">Check-in</h1>
                <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                  {[
                    { id: "kiosk", label: "Kiosk", icon: DevicePhoneMobileIcon },
                    { id: "dashboard", label: "Dashboard", icon: ChartBarIcon },
                    { id: "manager", label: "Manager", icon: UserGroupIcon },
                    { id: "analytics", label: "Analytics", icon: ChartBarIcon }
                  ].map((mode) => (
                    <button
                      key={mode.id}
                      onClick={() => setViewMode(mode.id as ViewMode)}
                      className={cn(
                        "flex items-center gap-2 px-4 py-2 rounded transition-all",
                        viewMode === mode.id
                          ? "bg-[var(--color-primary)] text-black"
                          : "hover:bg-white/10"
                      )}
                    >
                      <mode.icon className="h-4 w-4" />
                      <span className="text-sm font-medium">{mode.label}</span>
                    </button>
                  ))}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="glass" size="sm" leftIcon={<PrinterIcon className="h-4 w-4" />}>
                  Test Print
                </Button>
                <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                  New Family
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Stats Bar - Not in Kiosk Mode */}
        {viewMode !== "kiosk" && (
          <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center">
            <div className="grid grid-cols-6 gap-6 w-full">
              <div className="text-center">
                <p className="text-2xl font-bold">{stats.totalCheckedIn}</p>
                <p className="text-xs text-zinc-400">Checked In</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{stats.checkInRate}%</p>
                <p className="text-xs text-zinc-400">Check-in Rate</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-400">+{stats.newFamilies}</p>
                <p className="text-xs text-zinc-400">New Families</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{stats.volunteers}</p>
                <p className="text-xs text-zinc-400">Volunteers</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{stats.avgCheckInTime}</p>
                <p className="text-xs text-zinc-400">Avg Time</p>
              </div>
              <div className="text-center">
                <div className="h-8 w-8 mx-auto bg-green-500 rounded-full flex items-center justify-center animate-pulse">
                  <CheckCircleIcon className="h-5 w-5 text-black" />
                </div>
                <p className="text-xs text-zinc-400 mt-1">Active</p>
              </div>
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className="flex-1 relative">
          {viewMode === "kiosk" && (
            <div className="h-full flex items-center justify-center p-8">
              <AnimatePresence mode="wait">
                {checkInStep === "method" && (
                  <motion.div
                    key="method"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className="w-full max-w-6xl"
                  >
                    <div className="text-center mb-12">
                      <h2 className="text-4xl font-bold mb-4">Check In Your Family</h2>
                      <p className="text-xl text-zinc-400">Choose how you'd like to check in today</p>
                    </div>

                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
                      {[
                        { id: "phone", icon: PhoneIcon, label: "Phone Number", desc: "Enter your phone number" },
                        { id: "search", icon: MagnifyingGlassIcon, label: "Name Search", desc: "Search by family name" },
                        { id: "qr", icon: QrCodeIcon, label: "QR Code", desc: "Scan your family QR code" },
                        { id: "face", icon: FingerPrintIcon, label: "Face ID", desc: "Quick biometric check-in" }
                      ].map((method) => (
                        <button
                          key={method.id}
                          onClick={() => {
                            setCheckInMethod(method.id as CheckInMethod)
                            setCheckInStep("selection")
                          }}
                          className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-2xl p-8 hover:shadow-glow-xl transition-all group"
                        >
                          <method.icon className="h-16 w-16 mx-auto mb-4 group-hover:scale-110 transition-transform" />
                          <h3 className="text-xl font-medium mb-2">{method.label}</h3>
                          <p className="text-sm text-zinc-400">{method.desc}</p>
                        </button>
                      ))}
                    </div>

                    <div className="mt-12 text-center">
                      <p className="text-lg text-zinc-400 mb-4">First time here?</p>
                      <Button variant="primary" size="lg" leftIcon={<UserGroupIcon className="h-5 w-5" />}>
                        New Family Registration
                      </Button>
                    </div>
                  </motion.div>
                )}

                {checkInStep === "selection" && checkInMethod === "phone" && (
                  <motion.div
                    key="phone"
                    initial={{ opacity: 0, x: 100 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -100 }}
                    className="w-full max-w-4xl"
                  >
                    <button
                      onClick={() => setCheckInStep("method")}
                      className="flex items-center gap-2 mb-8 text-zinc-400 hover:text-white transition-colors"
                    >
                      <ArrowLeftIcon className="h-5 w-5" />
                      <span>Back</span>
                    </button>

                    <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-2xl p-8">
                      <h2 className="text-3xl font-bold text-center mb-8">Enter Your Phone Number</h2>
                      
                      {/* Phone Display */}
                      <div className="text-center mb-8">
                        <p className="text-5xl font-mono tracking-wider">
                          {formatPhoneNumber(phoneNumber) || "(___) ___-____"}
                        </p>
                      </div>

                      {/* Number Pad */}
                      <div className="max-w-md mx-auto">
                        <div className="grid grid-cols-3 gap-4">
                          {[1, 2, 3, 4, 5, 6, 7, 8, 9, "*", 0, "#"].map((digit) => (
                            <button
                              key={digit}
                              onClick={() => handlePhoneInput(digit.toString())}
                              disabled={digit === "*" || digit === "#"}
                              className={cn(
                                "h-20 text-2xl font-bold rounded-xl transition-all",
                                digit === "*" || digit === "#"
                                  ? "bg-white/5 text-zinc-600 cursor-not-allowed"
                                  : "bg-white/10 hover:bg-white/20 active:scale-95"
                              )}
                            >
                              {digit}
                            </button>
                          ))}
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 mt-4">
                          <button
                            onClick={() => handlePhoneInput("back")}
                            className="h-16 bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-xl font-medium transition-all"
                          >
                            Delete
                          </button>
                          <button
                            onClick={() => {
                              if (phoneNumber.length === 10) {
                                setSelectedFamily(sampleFamilies[0])
                                setCheckInStep("confirmation")
                              }
                            }}
                            disabled={phoneNumber.length !== 10}
                            className={cn(
                              "h-16 rounded-xl font-medium transition-all",
                              phoneNumber.length === 10
                                ? "bg-green-500 hover:bg-green-600 text-black"
                                : "bg-white/5 text-zinc-600 cursor-not-allowed"
                            )}
                          >
                            Continue
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {checkInStep === "confirmation" && selectedFamily && (
                  <motion.div
                    key="confirmation"
                    initial={{ opacity: 0, x: 100 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -100 }}
                    className="w-full max-w-6xl"
                  >
                    <button
                      onClick={() => setCheckInStep("selection")}
                      className="flex items-center gap-2 mb-8 text-zinc-400 hover:text-white transition-colors"
                    >
                      <ArrowLeftIcon className="h-5 w-5" />
                      <span>Back</span>
                    </button>

                    <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-2xl p-8">
                      <h2 className="text-3xl font-bold mb-2">Select Family Members</h2>
                      <p className="text-lg text-zinc-400 mb-8">Tap to select who's checking in today</p>

                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        {selectedFamily.members.map((member) => (
                          <button
                            key={member.id}
                            onClick={() => handleMemberToggle(member.id)}
                            className={cn(
                              "relative p-6 rounded-xl border-2 transition-all",
                              selectedMembers.has(member.id)
                                ? "bg-[var(--color-primary)]/20 border-[var(--color-primary)] shadow-glow"
                                : "bg-white/5 border-[var(--border-subtle)] hover:bg-white/10"
                            )}
                          >
                            {selectedMembers.has(member.id) && (
                              <div className="absolute top-2 right-2 w-8 h-8 bg-[var(--color-primary)] rounded-full flex items-center justify-center">
                                <CheckIcon className="h-5 w-5 text-black" />
                              </div>
                            )}
                            
                            <div className="w-20 h-20 bg-white/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                              <UserIcon className="h-10 w-10" />
                            </div>
                            
                            <h3 className="text-lg font-medium">{member.firstName}</h3>
                            <p className="text-sm text-zinc-400">Age {member.age}</p>
                            
                            {member.allergies && member.allergies.length > 0 && (
                              <div className="mt-2">
                                <Badge variant="secondary" size="sm" className="bg-red-500/20 text-red-400">
                                  Allergies
                                </Badge>
                              </div>
                            )}
                          </button>
                        ))}
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="text-lg">
                          <span className="text-zinc-400">Selected:</span>
                          <span className="ml-2 font-bold">{selectedMembers.size} members</span>
                        </div>
                        
                        <div className="flex gap-4">
                          <Button
                            variant="glass"
                            size="lg"
                            onClick={() => {
                              const allIds = selectedFamily.members.map(m => m.id)
                              setSelectedMembers(new Set(allIds))
                            }}
                          >
                            Select All
                          </Button>
                          <Button
                            variant="primary"
                            size="lg"
                            onClick={handleCheckIn}
                            disabled={selectedMembers.size === 0 || printingLabels}
                            leftIcon={printingLabels ? <PrinterIcon className="h-5 w-5 animate-pulse" /> : <CheckCircleIcon className="h-5 w-5" />}
                          >
                            {printingLabels ? "Printing Labels..." : "Check In & Print Labels"}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {checkInStep === "success" && (
                  <motion.div
                    key="success"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="text-center"
                  >
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring" }}
                      className="w-32 h-32 bg-green-500 rounded-full mx-auto mb-8 flex items-center justify-center"
                    >
                      <CheckCircleIcon className="h-20 w-20 text-black" />
                    </motion.div>
                    
                    <h2 className="text-4xl font-bold mb-4">Check-in Complete!</h2>
                    <p className="text-xl text-zinc-400 mb-8">Your name tags are printing</p>
                    
                    <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 max-w-md mx-auto">
                      <p className="text-lg mb-2">Please collect your name tags from the printer</p>
                      <p className="text-sm text-zinc-400">This screen will reset in 5 seconds</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          {viewMode === "dashboard" && (
            <div className="h-full p-6 grid grid-cols-12 gap-4">
              {/* Live Check-in Feed */}
              <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Live Check-ins</h3>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-green-400">Live</span>
                  </div>
                </div>

                <div className="space-y-3 max-h-[calc(100vh-400px)] overflow-y-auto custom-scrollbar">
                  {Array.from({ length: 10 }, (_, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: i * 0.05 }}
                      className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
                          <UserIcon className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">The Johnson Family</p>
                          <p className="text-sm text-zinc-400">4 members • {i + 1} min ago</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" size="sm">Room 201</Badge>
                        <CheckCircleIcon className="h-5 w-5 text-green-400" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Quick Stats */}
              <div className="col-span-4 space-y-4">
                {/* Check-in Progress */}
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Check-in Progress</h3>
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm">Overall</span>
                      <span className="text-sm font-medium">{stats.checkInRate}%</span>
                    </div>
                    <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-[var(--color-primary)] transition-all duration-1000"
                        style={{ width: `${stats.checkInRate}%` }}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 text-center">
                    <div className="bg-white/5 rounded-lg p-3">
                      <p className="text-2xl font-bold">{stats.totalCheckedIn}</p>
                      <p className="text-xs text-zinc-400">Checked In</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3">
                      <p className="text-2xl font-bold">{stats.expectedTotal}</p>
                      <p className="text-xs text-zinc-400">Expected</p>
                    </div>
                  </div>
                </div>

                {/* Room Status */}
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Room Status</h3>
                  <div className="space-y-2">
                    {[
                      { room: "Nursery", capacity: 20, current: 18, status: "filling" },
                      { room: "Preschool", capacity: 30, current: 22, status: "open" },
                      { room: "Elementary", capacity: 40, current: 40, status: "full" },
                      { room: "Youth", capacity: 50, current: 35, status: "open" }
                    ].map((room) => (
                      <div key={room.room}>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">{room.room}</span>
                          <Badge
                            variant="secondary"
                            size="sm"
                            className={cn(
                              room.status === "full" ? "bg-red-500/20 text-red-400" :
                              room.status === "filling" ? "bg-amber-500/20 text-amber-400" :
                              "bg-green-500/20 text-green-400"
                            )}
                          >
                            {room.current}/{room.capacity}
                          </Badge>
                        </div>
                        <div className="h-1.5 bg-white/10 rounded-full overflow-hidden">
                          <div
                            className={cn(
                              "h-full transition-all",
                              room.status === "full" ? "bg-red-500" :
                              room.status === "filling" ? "bg-amber-500" :
                              "bg-green-500"
                            )}
                            style={{ width: `${(room.current / room.capacity) * 100}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Alerts */}
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Alerts</h3>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <ExclamationTriangleIcon className="h-4 w-4 text-amber-400 flex-shrink-0 mt-0.5" />
                      <p className="text-xs">Elementary room nearing capacity</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <BellIcon className="h-4 w-4 text-blue-400 flex-shrink-0 mt-0.5" />
                      <p className="text-xs">3 families need allergy tags</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "manager" && (
            <div className="h-full p-6">
              <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <h3 className="text-lg font-medium mb-4">Check-in Management</h3>
                
                {/* Quick Actions Grid */}
                <div className="grid grid-cols-4 gap-4 mb-6">
                  {[
                    { icon: UserGroupIcon, label: "Manual Check-in", color: "bg-blue-500" },
                    { icon: PrinterIcon, label: "Reprint Labels", color: "bg-purple-500" },
                    { icon: MapPinIcon, label: "Room Assignment", color: "bg-green-500" },
                    { icon: ExclamationTriangleIcon, label: "Resolve Issues", color: "bg-amber-500" }
                  ].map((action, idx) => (
                    <button
                      key={idx}
                      className="p-6 bg-white/5 hover:bg-white/10 rounded-xl transition-all group"
                    >
                      <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center mb-3 mx-auto", action.color)}>
                        <action.icon className="h-6 w-6 text-white" />
                      </div>
                      <p className="text-sm font-medium">{action.label}</p>
                    </button>
                  ))}
                </div>

                {/* Search Bar */}
                <div className="relative mb-6">
                  <MagnifyingGlassIcon className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-zinc-400" />
                  <input
                    type="text"
                    placeholder="Search families, members, or check-in codes..."
                    className="w-full pl-12 pr-4 py-3 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                  />
                </div>

                {/* Recent Actions */}
                <div>
                  <h4 className="text-sm font-medium mb-3">Recent Actions</h4>
                  <div className="space-y-2">
                    {[
                      { action: "Manual check-in", family: "The Smith Family", time: "2 min ago", user: "Admin" },
                      { action: "Label reprint", family: "Johnson - Emma", time: "5 min ago", user: "Volunteer1" },
                      { action: "Room change", family: "Davis - Liam", time: "8 min ago", user: "Admin" }
                    ].map((log, idx) => (
                      <div key={idx} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div>
                          <p className="text-sm font-medium">{log.action}</p>
                          <p className="text-xs text-zinc-400">{log.family} • by {log.user}</p>
                        </div>
                        <span className="text-xs text-zinc-400">{log.time}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "analytics" && (
            <div className="h-full p-6 grid grid-cols-12 gap-4">
              {/* Check-in Trends */}
              <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <h3 className="text-lg font-medium mb-4">Check-in Patterns</h3>
                
                {/* Time Distribution */}
                <div className="mb-6">
                  <p className="text-sm text-zinc-400 mb-3">Check-ins by Time</p>
                  <div className="h-40 relative">
                    <div className="absolute inset-0 flex items-end justify-around">
                      {Array.from({ length: 12 }, (_, i) => (
                        <div key={i} className="flex-1 mx-0.5">
                          <div
                            className="bg-[var(--color-primary)] rounded-t transition-all hover:opacity-80"
                            style={{ height: `${Math.sin(i / 2) * 50 + 50}%` }}
                          />
                          <p className="text-xs text-center mt-1">{8 + Math.floor(i / 2)}:{i % 2 === 0 ? '00' : '30'}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Method Distribution */}
                <div className="grid grid-cols-4 gap-4">
                  {[
                    { method: "Phone", count: 156, percentage: 45, icon: PhoneIcon },
                    { method: "Search", count: 89, percentage: 26, icon: MagnifyingGlassIcon },
                    { method: "QR Code", count: 67, percentage: 19, icon: QrCodeIcon },
                    { method: "Manual", count: 34, percentage: 10, icon: UserIcon }
                  ].map((method) => (
                    <div key={method.method} className="text-center p-4 bg-white/5 rounded-lg">
                      <method.icon className="h-8 w-8 mx-auto mb-2 text-zinc-400" />
                      <p className="text-2xl font-bold">{method.count}</p>
                      <p className="text-sm text-zinc-400">{method.method}</p>
                      <p className="text-xs text-zinc-500">{method.percentage}%</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Key Metrics */}
              <div className="col-span-4 space-y-4">
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Performance Metrics</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-zinc-400">Avg Check-in Time</span>
                        <span className="text-sm font-medium">{stats.avgCheckInTime}</span>
                      </div>
                      <Badge variant="secondary" size="sm" className="bg-green-500/20 text-green-400">
                        15% faster than last week
                      </Badge>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-zinc-400">Self-Service Rate</span>
                        <span className="text-sm font-medium">89%</span>
                      </div>
                      <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                        <div className="h-full w-[89%] bg-blue-500" />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-zinc-400">Label Print Success</span>
                        <span className="text-sm font-medium">98.5%</span>
                      </div>
                      <p className="text-xs text-zinc-500">3 reprints today</p>
                    </div>
                  </div>
                </div>

                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Family Insights</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">Regular Families</span>
                      <span className="text-sm font-medium">312</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">New This Month</span>
                      <span className="text-sm font-medium text-green-400">+23</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">Avg Family Size</span>
                      <span className="text-sm font-medium">3.4</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Floating Command Center - Not in Kiosk Mode */}
      {viewMode !== "kiosk" && <FloatingCommandCenter position="bottom-right" />}
    </div>
  )
}