"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommand<PERSON>enter from "@/components/ui/floating-command-center"
import { 
  CalendarDaysIcon,
  MapIcon,
  ListBulletIcon,
  ClockIcon,
  UserGroupIcon,
  MapPinIcon,
  TicketIcon,
  MegaphoneIcon,
  CameraIcon,
  VideoCameraIcon,
  PlusIcon,
  FunnelIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  BellIcon,
  SparklesIcon,
  FireIcon,
  HeartIcon,
  AcademicCapIcon,
  MusicalNoteIcon,
  GlobeAltIcon,
  SunIcon,
  MoonIcon,
  CalendarIcon,
  Squares2X2Icon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowTrendingUpIcon,
  ExclamationTriangleIcon,
  ChartBarIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

type ViewMode = "calendar" | "timeline" | "map" | "analytics"
type CalendarView = "month" | "week" | "day" | "list"

interface FloatingEventPanel {
  id: string
  event: any
  position: { x: number; y: number }
}

interface EventCategory {
  id: string
  name: string
  icon: React.ComponentType<any>
  color: string
  count: number
}

export default function RevolutionaryEventsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("calendar")
  const [calendarView, setCalendarView] = useState<CalendarView>("month")
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [floatingPanels, setFloatingPanels] = useState<FloatingEventPanel[]>([])
  const [filterPanelOpen, setFilterPanelOpen] = useState(false)
  const [liveEventsMode, setLiveEventsMode] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  // Sample data
  const eventStats = {
    totalEvents: 24,
    thisWeek: 8,
    totalAttendees: 1234,
    avgAttendance: 51,
    categories: 6,
    venues: 4
  }

  const categories: EventCategory[] = [
    { id: "worship", name: "Worship Services", icon: SunIcon, color: "bg-amber-500", count: 12 },
    { id: "groups", name: "Small Groups", icon: UserGroupIcon, color: "bg-blue-500", count: 23 },
    { id: "youth", name: "Youth Events", icon: FireIcon, color: "bg-orange-500", count: 8 },
    { id: "classes", name: "Classes", icon: AcademicCapIcon, color: "bg-green-500", count: 15 },
    { id: "special", name: "Special Events", icon: SparklesIcon, color: "bg-purple-500", count: 4 },
    { id: "outreach", name: "Outreach", icon: GlobeAltIcon, color: "bg-pink-500", count: 6 }
  ]

  const upcomingEvents = [
    {
      id: 1,
      title: "Sunday Morning Service",
      category: "worship",
      date: "Dec 17",
      time: "10:00 AM",
      venue: "Main Sanctuary",
      attendees: 450,
      capacity: 600,
      status: "open"
    },
    {
      id: 2,
      title: "Youth Group",
      category: "youth",
      date: "Dec 17",
      time: "6:00 PM",
      venue: "Youth Center",
      attendees: 78,
      capacity: 100,
      status: "filling"
    },
    {
      id: 3,
      title: "Men's Bible Study",
      category: "groups",
      date: "Dec 18",
      time: "7:00 AM",
      venue: "Room 201",
      attendees: 15,
      capacity: 20,
      status: "open"
    },
    {
      id: 4,
      title: "Christmas Concert",
      category: "special",
      date: "Dec 24",
      time: "7:00 PM",
      venue: "Main Sanctuary",
      attendees: 580,
      capacity: 600,
      status: "full"
    }
  ]

  const addFloatingPanel = (event: any) => {
    const newPanel: FloatingEventPanel = {
      id: event.id,
      event,
      position: { x: window.innerWidth / 2, y: window.innerHeight / 2 }
    }
    setFloatingPanels([...floatingPanels, newPanel])
  }

  const removeFloatingPanel = (id: string) => {
    setFloatingPanels(floatingPanels.filter(panel => panel.id !== id))
  }

  // Calendar grid generation
  const generateCalendarDays = () => {
    const year = selectedDate.getFullYear()
    const month = selectedDate.getMonth()
    const firstDay = new Date(year, month, 1).getDay()
    const daysInMonth = new Date(year, month + 1, 0).getDate()
    const days = []

    // Previous month days
    for (let i = firstDay - 1; i >= 0; i--) {
      days.push({ day: null, isCurrentMonth: false })
    }

    // Current month days
    for (let i = 1; i <= daysInMonth; i++) {
      days.push({ day: i, isCurrentMonth: true })
    }

    return days
  }

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarRevolutionary />

      {/* Main Content - NO SCROLLING */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Primary Navigation Bar - Sticky */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] sticky top-0 z-30">
          <div className="h-full px-6 flex items-center justify-between">
            {/* View Mode Switcher */}
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">Events</h1>
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "calendar", label: "Calendar", icon: CalendarDaysIcon },
                  { id: "timeline", label: "Timeline", icon: ClockIcon },
                  { id: "map", label: "Map", icon: MapIcon },
                  { id: "analytics", label: "Analytics", icon: ChartBarIcon }
                ].map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => setViewMode(mode.id as ViewMode)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded transition-all",
                      viewMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <mode.icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{mode.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              {viewMode === "calendar" && (
                <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                  {[
                    { id: "month", icon: Squares2X2Icon },
                    { id: "week", icon: CalendarDaysIcon },
                    { id: "day", icon: CalendarIcon },
                    { id: "list", icon: ListBulletIcon }
                  ].map((view) => (
                    <button
                      key={view.id}
                      onClick={() => setCalendarView(view.id as CalendarView)}
                      className={cn(
                        "p-2 rounded transition-colors",
                        calendarView === view.id
                          ? "bg-[var(--color-primary)] text-black"
                          : "hover:bg-white/10"
                      )}
                    >
                      <view.icon className="h-4 w-4" />
                    </button>
                  ))}
                </div>
              )}

              <button
                onClick={() => setLiveEventsMode(!liveEventsMode)}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  liveEventsMode ? "bg-green-500/20 text-green-400" : "hover:bg-white/10"
                )}
                title="Live Events Mode"
              >
                <BellIcon className="h-5 w-5" />
              </button>

              <button
                onClick={() => setFilterPanelOpen(!filterPanelOpen)}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  filterPanelOpen ? "bg-[var(--color-primary)]/20 text-[var(--color-primary)]" : "hover:bg-white/10"
                )}
              >
                <FunnelIcon className="h-5 w-5" />
              </button>

              <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Create Event
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Bar - Always Visible */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center">
          <div className="grid grid-cols-6 gap-6 w-full">
            <div className="text-center">
              <p className="text-2xl font-bold">{eventStats.totalEvents}</p>
              <p className="text-xs text-zinc-400">Total Events</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">{eventStats.thisWeek}</p>
              <p className="text-xs text-zinc-400">This Week</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{eventStats.totalAttendees}</p>
              <p className="text-xs text-zinc-400">Expected Attendees</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{eventStats.avgAttendance}</p>
              <p className="text-xs text-zinc-400">Avg Attendance</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-400">{eventStats.categories}</p>
              <p className="text-xs text-zinc-400">Categories</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{eventStats.venues}</p>
              <p className="text-xs text-zinc-400">Active Venues</p>
            </div>
          </div>
        </div>

        {/* Main Content Area - Dynamic Based on View Mode */}
        <div className="flex-1 flex relative">
          {/* Filter Panel */}
          <AnimatePresence>
            {filterPanelOpen && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 280, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border-r border-[var(--border-subtle)] overflow-hidden"
              >
                <div className="w-[280px] p-4 h-full overflow-y-auto custom-scrollbar">
                  <h3 className="text-sm font-medium mb-4">Filters</h3>
                  
                  {/* Categories */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-3">Categories</p>
                    <div className="space-y-2">
                      {categories.map((category) => (
                        <label key={category.id} className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={selectedCategory === category.id}
                            onChange={() => setSelectedCategory(category.id)}
                            className="rounded"
                          />
                          <category.icon className="h-4 w-4" />
                          <span className="text-sm flex-1">{category.name}</span>
                          <Badge variant="secondary" size="sm">{category.count}</Badge>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Date Range */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-3">Date Range</p>
                    <div className="space-y-2">
                      <Button variant="glass" size="sm" className="w-full justify-start">
                        Today
                      </Button>
                      <Button variant="glass" size="sm" className="w-full justify-start">
                        This Week
                      </Button>
                      <Button variant="glass" size="sm" className="w-full justify-start">
                        This Month
                      </Button>
                      <Button variant="glass" size="sm" className="w-full justify-start">
                        Custom Range
                      </Button>
                    </div>
                  </div>

                  {/* Venues */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-3">Venues</p>
                    <div className="space-y-2">
                      {["Main Sanctuary", "Youth Center", "Fellowship Hall", "Room 201"].map((venue) => (
                        <label key={venue} className="flex items-center gap-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-sm">{venue}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <Button variant="primary" size="sm" className="w-full">
                    Apply Filters
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content */}
          <div className="flex-1">
            {viewMode === "calendar" && calendarView === "month" && (
              <div className="h-full p-6">
                {/* Calendar Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
                      <ChevronLeftIcon className="h-5 w-5" />
                    </button>
                    <h2 className="text-xl font-bold">
                      {selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                    </h2>
                    <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
                      <ChevronRightIcon className="h-5 w-5" />
                    </button>
                  </div>
                  <Button variant="glass" size="sm">Today</Button>
                </div>

                {/* Calendar Grid */}
                <div className="h-[calc(100%-3rem)] bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl overflow-hidden">
                  {/* Week Days */}
                  <div className="grid grid-cols-7 border-b border-[var(--border-subtle)]">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                      <div key={day} className="p-3 text-center text-sm font-medium text-zinc-400">
                        {day}
                      </div>
                    ))}
                  </div>

                  {/* Calendar Days */}
                  <div className="grid grid-cols-7 grid-rows-5 h-[calc(100%-3rem)]">
                    {generateCalendarDays().map((day, idx) => (
                      <div
                        key={idx}
                        className={cn(
                          "border-r border-b border-[var(--border-subtle)] p-2 relative group",
                          !day.isCurrentMonth && "opacity-30",
                          "hover:bg-white/5 transition-colors cursor-pointer"
                        )}
                      >
                        {day.day && (
                          <>
                            <p className="text-sm mb-1">{day.day}</p>
                            
                            {/* Sample Events */}
                            {day.day === 17 && (
                              <div className="space-y-1">
                                <div
                                  className="bg-amber-500/20 text-amber-400 text-xs px-1 py-0.5 rounded truncate cursor-pointer hover:bg-amber-500/30"
                                  onClick={() => addFloatingPanel({ id: 'event-1', title: 'Sunday Service' })}
                                >
                                  10:00 AM Service
                                </div>
                                <div
                                  className="bg-orange-500/20 text-orange-400 text-xs px-1 py-0.5 rounded truncate cursor-pointer hover:bg-orange-500/30"
                                  onClick={() => addFloatingPanel({ id: 'event-2', title: 'Youth Group' })}
                                >
                                  6:00 PM Youth
                                </div>
                              </div>
                            )}
                            {day.day === 24 && (
                              <div
                                className="bg-purple-500/20 text-purple-400 text-xs px-1 py-0.5 rounded truncate cursor-pointer hover:bg-purple-500/30"
                                onClick={() => addFloatingPanel({ id: 'event-3', title: 'Christmas Concert' })}
                              >
                                7:00 PM Concert
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {viewMode === "timeline" && (
              <div className="h-full p-6">
                <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                  <h3 className="text-lg font-medium mb-4">Event Timeline</h3>
                  
                  {/* Timeline */}
                  <div className="relative">
                    {/* Timeline Line */}
                    <div className="absolute left-16 top-0 bottom-0 w-0.5 bg-[var(--border-subtle)]" />
                    
                    {/* Timeline Events */}
                    <div className="space-y-6">
                      {upcomingEvents.map((event, idx) => (
                        <motion.div
                          key={event.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: idx * 0.1 }}
                          className="flex gap-4"
                        >
                          {/* Date */}
                          <div className="w-16 text-right">
                            <p className="text-sm font-medium">{event.date}</p>
                            <p className="text-xs text-zinc-400">{event.time}</p>
                          </div>
                          
                          {/* Dot */}
                          <div className="relative">
                            <div className={cn(
                              "w-4 h-4 rounded-full border-2 border-black",
                              event.status === "full" ? "bg-red-500" : event.status === "filling" ? "bg-amber-500" : "bg-green-500"
                            )} />
                          </div>
                          
                          {/* Event Card */}
                          <div
                            className="flex-1 p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                            onClick={() => addFloatingPanel(event)}
                          >
                            <div className="flex items-start justify-between">
                              <div>
                                <h4 className="font-medium mb-1">{event.title}</h4>
                                <p className="text-sm text-zinc-400 mb-2">{event.venue}</p>
                                <div className="flex items-center gap-4">
                                  <Badge variant="secondary" size="sm">
                                    {categories.find(c => c.id === event.category)?.name}
                                  </Badge>
                                  <div className="flex items-center gap-1">
                                    <UserGroupIcon className="h-4 w-4 text-zinc-400" />
                                    <span className="text-sm">{event.attendees}/{event.capacity}</span>
                                  </div>
                                </div>
                              </div>
                              
                              {event.status === "full" && (
                                <Badge variant="secondary" size="sm" className="bg-red-500/20 text-red-400">
                                  FULL
                                </Badge>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {viewMode === "map" && (
              <div className="h-full p-6">
                <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl overflow-hidden relative">
                  {/* Map Placeholder */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20">
                    {/* Venue Markers */}
                    <div className="absolute top-1/4 left-1/3">
                      <div className="relative group">
                        <div className="w-12 h-12 bg-amber-500 rounded-full flex items-center justify-center shadow-glow animate-pulse">
                          <SunIcon className="h-6 w-6 text-black" />
                        </div>
                        <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="bg-black/90 rounded-lg p-2 whitespace-nowrap">
                            <p className="text-sm font-medium">Main Sanctuary</p>
                            <p className="text-xs text-zinc-400">3 events today</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="absolute top-1/2 right-1/3">
                      <div className="relative group">
                        <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center shadow-glow">
                          <FireIcon className="h-5 w-5 text-black" />
                        </div>
                        <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="bg-black/90 rounded-lg p-2 whitespace-nowrap">
                            <p className="text-sm font-medium">Youth Center</p>
                            <p className="text-xs text-zinc-400">1 event today</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="absolute bottom-1/3 left-1/2">
                      <div className="relative group">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-glow">
                          <AcademicCapIcon className="h-4 w-4 text-black" />
                        </div>
                        <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="bg-black/90 rounded-lg p-2 whitespace-nowrap">
                            <p className="text-sm font-medium">Room 201</p>
                            <p className="text-xs text-zinc-400">2 events this week</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Map Legend */}
                  <div className="absolute top-4 right-4 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-lg p-4">
                    <h3 className="text-sm font-medium mb-2">Venues</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-amber-500 rounded-full" />
                        <span className="text-xs">Main Campus</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-orange-500 rounded-full" />
                        <span className="text-xs">Youth Facilities</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full" />
                        <span className="text-xs">Classrooms</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {viewMode === "analytics" && (
              <div className="h-full p-6 grid grid-cols-12 gap-4">
                {/* Attendance Trends */}
                <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                  <h3 className="text-lg font-medium mb-4">Attendance Trends</h3>
                  
                  {/* Chart */}
                  <div className="h-64 relative mb-6">
                    <div className="absolute inset-0 flex items-end justify-around">
                      {Array.from({ length: 12 }, (_, i) => {
                        const height = Math.random() * 80 + 20
                        return (
                          <div key={i} className="flex-1 mx-1 flex flex-col items-center">
                            <div className="w-full bg-[var(--color-primary)] rounded-t-lg transition-all hover:opacity-80" style={{ height: `${height}%` }} />
                            <p className="text-xs mt-2">W{i + 1}</p>
                          </div>
                        )
                      })}
                    </div>
                  </div>

                  {/* Category Breakdown */}
                  <div className="grid grid-cols-3 gap-4">
                    {categories.slice(0, 3).map((category) => (
                      <div key={category.id} className="text-center p-3 bg-white/5 rounded-lg">
                        <category.icon className={cn("h-8 w-8 mx-auto mb-2", category.color.replace('bg-', 'text-'))} />
                        <p className="text-sm font-medium">{category.name}</p>
                        <p className="text-2xl font-bold mt-1">{Math.floor(Math.random() * 200 + 100)}</p>
                        <p className="text-xs text-zinc-400">avg attendance</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Insights */}
                <div className="col-span-4 space-y-4">
                  <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                    <h3 className="text-sm font-medium mb-3">Key Insights</h3>
                    <div className="space-y-3">
                      <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                        <div className="flex items-start gap-2">
                          <ArrowTrendingUpIcon className="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5" />
                          <div>
                            <p className="text-sm font-medium text-green-400">Growing Attendance</p>
                            <p className="text-xs mt-1">Sunday services up 15% this quarter</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                        <div className="flex items-start gap-2">
                          <UserGroupIcon className="h-4 w-4 text-blue-400 flex-shrink-0 mt-0.5" />
                          <div>
                            <p className="text-sm font-medium text-blue-400">Popular Events</p>
                            <p className="text-xs mt-1">Youth events seeing highest growth</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-3 bg-amber-500/10 border border-amber-500/30 rounded-lg">
                        <div className="flex items-start gap-2">
                          <ExclamationTriangleIcon className="h-4 w-4 text-amber-400 flex-shrink-0 mt-0.5" />
                          <div>
                            <p className="text-sm font-medium text-amber-400">Capacity Alert</p>
                            <p className="text-xs mt-1">3 events near capacity this week</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                    <h3 className="text-sm font-medium mb-3">Venue Utilization</h3>
                    <div className="space-y-2">
                      {["Main Sanctuary", "Youth Center", "Fellowship Hall", "Room 201"].map((venue, idx) => (
                        <div key={venue}>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">{venue}</span>
                            <span className="text-sm font-medium">{85 - idx * 10}%</span>
                          </div>
                          <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-[var(--color-primary)] transition-all"
                              style={{ width: `${85 - idx * 10}%` }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Live Events Panel */}
          <AnimatePresence>
            {liveEventsMode && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 320, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border-l border-[var(--border-subtle)] overflow-hidden"
              >
                <div className="w-80 p-4 h-full overflow-y-auto custom-scrollbar">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium">Live Events</h3>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      <span className="text-xs text-green-400">Live</span>
                    </div>
                  </div>

                  {/* Currently Running Events */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Now Running</p>
                    <div className="space-y-2">
                      <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <p className="text-sm font-medium">Sunday Morning Service</p>
                            <p className="text-xs text-zinc-400">Main Sanctuary</p>
                          </div>
                          <Badge variant="secondary" size="sm" className="bg-green-500/20 text-green-400">
                            LIVE
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <UserGroupIcon className="h-4 w-4 text-zinc-400" />
                            <span className="text-xs">423 checked in</span>
                          </div>
                          <Button variant="glass" size="sm">
                            <EyeIcon className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Starting Soon */}
                  <div className="mb-6">
                    <p className="text-xs text-zinc-400 mb-2">Starting Soon</p>
                    <div className="space-y-2">
                      {[
                        { name: "Youth Group", time: "15 min", venue: "Youth Center" },
                        { name: "Prayer Meeting", time: "45 min", venue: "Chapel" }
                      ].map((event, idx) => (
                        <div key={idx} className="p-3 bg-amber-500/10 border border-amber-500/30 rounded-lg">
                          <p className="text-sm font-medium">{event.name}</p>
                          <p className="text-xs text-zinc-400">{event.venue} • Starts in {event.time}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Recent Check-ins */}
                  <div>
                    <p className="text-xs text-zinc-400 mb-2">Recent Check-ins</p>
                    <div className="space-y-1">
                      {Array.from({ length: 5 }, (_, i) => (
                        <div key={i} className="flex items-center gap-2 p-2 hover:bg-white/5 rounded-lg transition-colors">
                          <Image
                            src="/placeholder-user.jpg"
                            alt="User"
                            width={24}
                            height={24}
                            className="rounded-full"
                          />
                          <div className="flex-1">
                            <p className="text-xs">John Doe checked in</p>
                            <p className="text-xs text-zinc-400">{i + 1} min ago</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Bottom Action Bar */}
        <div className="h-12 bg-[var(--bg-glass)] backdrop-blur-xl border-t border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm">
            {liveEventsMode && (
              <>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-green-400">1 event live</span>
                </div>
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-amber-400" />
                  <span className="text-amber-400">2 starting soon</span>
                </div>
              </>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="glass" size="sm" leftIcon={<TicketIcon className="h-4 w-4" />}>
              Registration
            </Button>
            <Button variant="glass" size="sm" leftIcon={<MegaphoneIcon className="h-4 w-4" />}>
              Promote
            </Button>
            <Button variant="glass" size="sm" leftIcon={<CameraIcon className="h-4 w-4" />}>
              Media
            </Button>
          </div>
        </div>
      </div>

      {/* Floating Event Panels */}
      {floatingPanels.map((panel) => (
        <motion.div
          key={panel.id}
          drag
          dragMomentum={false}
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="fixed z-40 w-96 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-xl"
          style={{ left: panel.position.x, top: panel.position.y }}
        >
          <div className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div>
                <h3 className="font-medium">{panel.event.title}</h3>
                <p className="text-sm text-zinc-400">{panel.event.date} • {panel.event.time}</p>
              </div>
              <button
                onClick={() => removeFloatingPanel(panel.id)}
                className="p-1 hover:bg-white/10 rounded transition-colors"
              >
                <XCircleIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="space-y-3">
              {/* Event Details */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <MapPinIcon className="h-4 w-4 text-zinc-400" />
                    <p className="text-xs text-zinc-400">Venue</p>
                  </div>
                  <p className="text-sm font-medium">{panel.event.venue || "Main Sanctuary"}</p>
                </div>
                <div className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <UserGroupIcon className="h-4 w-4 text-zinc-400" />
                    <p className="text-xs text-zinc-400">Attendance</p>
                  </div>
                  <p className="text-sm font-medium">{panel.event.attendees || 0}/{panel.event.capacity || 100}</p>
                </div>
              </div>

              {/* Progress Bar */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-zinc-400">Capacity</span>
                  <span className="text-xs font-medium">{Math.round((panel.event.attendees || 0) / (panel.event.capacity || 100) * 100)}%</span>
                </div>
                <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                  <div
                    className={cn(
                      "h-full transition-all",
                      panel.event.status === "full" ? "bg-red-500" : panel.event.status === "filling" ? "bg-amber-500" : "bg-green-500"
                    )}
                    style={{ width: `${(panel.event.attendees || 0) / (panel.event.capacity || 100) * 100}%` }}
                  />
                </div>
              </div>

              {/* Actions */}
              <div className="grid grid-cols-3 gap-2">
                <Button variant="glass" size="sm">
                  <TicketIcon className="h-4 w-4" />
                </Button>
                <Button variant="glass" size="sm">
                  <UserGroupIcon className="h-4 w-4" />
                </Button>
                <Button variant="primary" size="sm">
                  Manage
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      ))}

      {/* Floating Command Center */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}