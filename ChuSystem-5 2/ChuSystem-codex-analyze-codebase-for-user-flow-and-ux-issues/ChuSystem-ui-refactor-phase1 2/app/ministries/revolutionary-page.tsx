"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommand<PERSON><PERSON> from "@/components/ui/floating-command-center"
import { 
  UserGroupIcon,
  BriefcaseIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  HeartIcon,
  SparklesIcon,
  CogIcon,
  FolderIcon,
  BanknotesIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  FunnelIcon,
  ArrowsPointingInIcon,
  ArrowsPointingOutIcon,
  FireIcon,
  AcademicCapIcon,
  MusicalNoteIcon,
  VideoCameraIcon,
  MegaphoneIcon,
  BuildingLibraryIcon,
  HandRaisedIcon,
  BookOpenIcon,
  GlobeAltIcon,
  HomeIcon,
  ShieldCheckIcon,
  TruckIcon,
  CameraIcon,
  ComputerDesktopIcon,
  MicrophoneIcon,
  PaintBrushIcon,
  WrenchScrewdriverIcon,
  ChevronRightIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  MapPinIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  BellIcon,
  EyeIcon,
  XCircleIcon,
  UserPlusIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

type ViewMode = "overview" | "teams" | "resources" | "impact"
type DisplayMode = "grid" | "network" | "timeline"

interface MinistryPanel {
  id: string
  ministry: any
  position: { x: number; y: number }
}

interface Ministry {
  id: string
  name: string
  icon: React.ComponentType<any>
  color: string
  leader: string
  volunteers: number
  budget: number
  healthScore: number
  activeProjects: number
  impactMetric: string
  status: "thriving" | "stable" | "needs-attention"
}

interface Volunteer {
  id: string
  name: string
  ministries: string[]
  hours: number
  skills: string[]
  availability: string
}

export default function RevolutionaryMinistriesPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("overview")
  const [displayMode, setDisplayMode] = useState<DisplayMode>("grid")
  const [selectedMinistry, setSelectedMinistry] = useState<string | null>(null)
  const [floatingPanels, setFloatingPanels] = useState<MinistryPanel[]>([])
  const [resourceView, setResourceView] = useState<"budget" | "volunteers" | "equipment">("budget")
  const [liveMode, setLiveMode] = useState(false)

  // Sample data
  const ministryStats = {
    totalMinistries: 18,
    activeVolunteers: 342,
    monthlyBudget: 45000,
    activeProjects: 67,
    impactedLives: 2341,
    healthScore: 87
  }

  const ministries: Ministry[] = [
    {
      id: "worship",
      name: "Worship & Arts",
      icon: MusicalNoteIcon,
      color: "bg-purple-500",
      leader: "Sarah Johnson",
      volunteers: 45,
      budget: 8500,
      healthScore: 92,
      activeProjects: 5,
      impactMetric: "450 worshippers led weekly",
      status: "thriving"
    },
    {
      id: "children",
      name: "Children's Ministry",
      icon: AcademicCapIcon,
      color: "bg-green-500",
      leader: "Mike Davis",
      volunteers: 67,
      budget: 12000,
      healthScore: 88,
      activeProjects: 8,
      impactMetric: "234 kids attending weekly",
      status: "thriving"
    },
    {
      id: "youth",
      name: "Youth Ministry",
      icon: FireIcon,
      color: "bg-orange-500",
      leader: "John Smith",
      volunteers: 34,
      budget: 6500,
      healthScore: 75,
      activeProjects: 6,
      impactMetric: "156 teens engaged",
      status: "stable"
    },
    {
      id: "outreach",
      name: "Outreach & Missions",
      icon: GlobeAltIcon,
      color: "bg-blue-500",
      leader: "Lisa Chen",
      volunteers: 89,
      budget: 15000,
      healthScore: 95,
      activeProjects: 12,
      impactMetric: "500+ served monthly",
      status: "thriving"
    },
    {
      id: "media",
      name: "Media & Tech",
      icon: VideoCameraIcon,
      color: "bg-pink-500",
      leader: "David Kim",
      volunteers: 23,
      budget: 5000,
      healthScore: 65,
      activeProjects: 4,
      impactMetric: "10K online views",
      status: "needs-attention"
    },
    {
      id: "facilities",
      name: "Facilities",
      icon: BuildingLibraryIcon,
      color: "bg-amber-500",
      leader: "Robert Brown",
      volunteers: 15,
      budget: 3000,
      healthScore: 80,
      activeProjects: 3,
      impactMetric: "100% uptime",
      status: "stable"
    }
  ]

  const addFloatingPanel = (ministry: Ministry) => {
    const newPanel: MinistryPanel = {
      id: ministry.id,
      ministry,
      position: { x: window.innerWidth / 2 - 200, y: window.innerHeight / 2 - 150 }
    }
    setFloatingPanels([...floatingPanels, newPanel])
  }

  const removeFloatingPanel = (id: string) => {
    setFloatingPanels(floatingPanels.filter(panel => panel.id !== id))
  }

  // Calculate resource allocation
  const totalBudget = ministries.reduce((sum, m) => sum + m.budget, 0)
  const totalVolunteers = ministries.reduce((sum, m) => sum + m.volunteers, 0)

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarRevolutionary />

      {/* Main Content - NO SCROLLING */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Primary Navigation Bar - Sticky */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] sticky top-0 z-30">
          <div className="h-full px-6 flex items-center justify-between">
            {/* View Mode Switcher */}
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">Ministries</h1>
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "overview", label: "Overview", icon: BriefcaseIcon },
                  { id: "teams", label: "Teams", icon: UserGroupIcon, badge: totalVolunteers },
                  { id: "resources", label: "Resources", icon: FolderIcon },
                  { id: "impact", label: "Impact", icon: ChartBarIcon }
                ].map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => setViewMode(mode.id as ViewMode)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded transition-all",
                      viewMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <mode.icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{mode.label}</span>
                    {mode.badge && (
                      <Badge variant="secondary" size="sm">
                        {mode.badge}
                      </Badge>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              {viewMode === "overview" && (
                <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                  {[
                    { id: "grid", icon: ArrowsPointingInIcon },
                    { id: "network", icon: ArrowsPointingOutIcon },
                    { id: "timeline", icon: ClockIcon }
                  ].map((mode) => (
                    <button
                      key={mode.id}
                      onClick={() => setDisplayMode(mode.id as DisplayMode)}
                      className={cn(
                        "p-2 rounded transition-colors",
                        displayMode === mode.id
                          ? "bg-[var(--color-primary)] text-black"
                          : "hover:bg-white/10"
                      )}
                    >
                      <mode.icon className="h-4 w-4" />
                    </button>
                  ))}
                </div>
              )}

              <button
                onClick={() => setLiveMode(!liveMode)}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  liveMode ? "bg-green-500/20 text-green-400" : "hover:bg-white/10"
                )}
                title="Live Activity Mode"
              >
                <BellIcon className="h-5 w-5" />
              </button>

              <Button variant="glass" size="sm" leftIcon={<FunnelIcon className="h-4 w-4" />}>
                Filter
              </Button>

              <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                New Ministry
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Bar - Always Visible */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center">
          <div className="grid grid-cols-6 gap-6 w-full">
            <div className="text-center">
              <p className="text-2xl font-bold">{ministryStats.totalMinistries}</p>
              <p className="text-xs text-zinc-400">Active Ministries</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">{ministryStats.activeVolunteers}</p>
              <p className="text-xs text-zinc-400">Volunteers</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">${(ministryStats.monthlyBudget / 1000).toFixed(0)}K</p>
              <p className="text-xs text-zinc-400">Monthly Budget</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-400">{ministryStats.activeProjects}</p>
              <p className="text-xs text-zinc-400">Active Projects</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{ministryStats.impactedLives}</p>
              <p className="text-xs text-zinc-400">Lives Impacted</p>
            </div>
            <div className="text-center">
              <div className={cn(
                "text-2xl font-bold",
                ministryStats.healthScore >= 80 ? "text-green-400" : 
                ministryStats.healthScore >= 60 ? "text-amber-400" : "text-red-400"
              )}>
                {ministryStats.healthScore}%
              </div>
              <p className="text-xs text-zinc-400">Health Score</p>
            </div>
          </div>
        </div>

        {/* Main Content Area - Dynamic Based on View Mode */}
        <div className="flex-1 relative overflow-hidden">
          {viewMode === "overview" && displayMode === "grid" && (
            <div className="h-full p-6 overflow-y-auto custom-scrollbar">
              <div className="grid grid-cols-3 gap-4">
                {ministries.map((ministry, idx) => (
                  <motion.div
                    key={ministry.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: idx * 0.05 }}
                    className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 hover:shadow-glow-lg transition-all cursor-pointer"
                    onClick={() => addFloatingPanel(ministry)}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center", ministry.color)}>
                        <ministry.icon className="h-6 w-6 text-white" />
                      </div>
                      <Badge 
                        variant="secondary" 
                        size="sm"
                        className={cn(
                          ministry.status === "thriving" ? "bg-green-500/20 text-green-400" :
                          ministry.status === "stable" ? "bg-blue-500/20 text-blue-400" :
                          "bg-amber-500/20 text-amber-400"
                        )}
                      >
                        {ministry.status}
                      </Badge>
                    </div>

                    <h3 className="text-lg font-medium mb-1">{ministry.name}</h3>
                    <p className="text-sm text-zinc-400 mb-4">Led by {ministry.leader}</p>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-zinc-400">Health Score</span>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-24 bg-white/10 rounded-full overflow-hidden">
                            <div 
                              className={cn(
                                "h-full transition-all",
                                ministry.healthScore >= 80 ? "bg-green-500" :
                                ministry.healthScore >= 60 ? "bg-amber-500" : "bg-red-500"
                              )}
                              style={{ width: `${ministry.healthScore}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium">{ministry.healthScore}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-3 mb-4">
                      <div className="text-center p-2 bg-white/5 rounded-lg">
                        <p className="text-lg font-bold">{ministry.volunteers}</p>
                        <p className="text-xs text-zinc-400">Volunteers</p>
                      </div>
                      <div className="text-center p-2 bg-white/5 rounded-lg">
                        <p className="text-lg font-bold">${(ministry.budget / 1000).toFixed(1)}K</p>
                        <p className="text-xs text-zinc-400">Budget</p>
                      </div>
                      <div className="text-center p-2 bg-white/5 rounded-lg">
                        <p className="text-lg font-bold">{ministry.activeProjects}</p>
                        <p className="text-xs text-zinc-400">Projects</p>
                      </div>
                    </div>

                    <div className="p-2 bg-white/5 rounded-lg">
                      <p className="text-xs text-zinc-400 mb-1">Impact Metric</p>
                      <p className="text-sm font-medium">{ministry.impactMetric}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {viewMode === "overview" && displayMode === "network" && (
            <div className="h-full p-6">
              <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 relative">
                <h3 className="text-lg font-medium mb-4">Ministry Network</h3>
                
                {/* Network Visualization */}
                <svg className="w-full h-full">
                  {/* Connection lines */}
                  <line x1="50%" y1="50%" x2="25%" y2="20%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="75%" y2="20%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="25%" y2="80%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="75%" y2="80%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="85%" y2="50%" stroke="var(--border-subtle)" strokeWidth="2" />
                  <line x1="50%" y1="50%" x2="15%" y2="50%" stroke="var(--border-subtle)" strokeWidth="2" />
                  
                  {/* Central Church Hub */}
                  <g transform="translate(50%, 50%)">
                    <circle cx="0" cy="0" r="80" fill="var(--color-primary)" fillOpacity="0.2" stroke="var(--color-primary)" strokeWidth="2" />
                    <BuildingLibraryIcon className="h-10 w-10 text-white" x="-20" y="-20" />
                    <text x="0" y="35" textAnchor="middle" fill="white" className="text-sm font-medium">Church Core</text>
                  </g>
                  
                  {/* Ministry Nodes */}
                  {ministries.map((ministry, idx) => {
                    const angle = (idx / ministries.length) * 2 * Math.PI
                    const x = 50 + 30 * Math.cos(angle)
                    const y = 50 + 30 * Math.sin(angle)
                    
                    return (
                      <g 
                        key={ministry.id} 
                        transform={`translate(${x}%, ${y}%)`}
                        className="cursor-pointer"
                        onClick={() => addFloatingPanel(ministry)}
                      >
                        <circle 
                          cx="0" 
                          cy="0" 
                          r="50" 
                          fill={ministry.color.replace('bg-', 'rgba(').replace('-500', ', 0.2)')} 
                          stroke={ministry.color.replace('bg-', 'rgb(').replace('-500', ')')} 
                          strokeWidth="2" 
                          className="hover:fill-opacity-30 transition-all"
                        />
                        {React.createElement(ministry.icon, { 
                          className: "h-6 w-6 text-white", 
                          x: "-12", 
                          y: "-12" 
                        })}
                        <text x="0" y="30" textAnchor="middle" fill="white" className="text-xs">
                          {ministry.name.split(' ')[0]}
                        </text>
                      </g>
                    )
                  })}
                </svg>
              </div>
            </div>
          )}

          {viewMode === "teams" && (
            <div className="h-full flex">
              {/* Volunteer List */}
              <div className="flex-1 p-6 overflow-y-auto custom-scrollbar">
                <div className="grid grid-cols-12 gap-4">
                  {/* Ministry Overview */}
                  <div className="col-span-8 space-y-4">
                    <h3 className="text-lg font-medium">Ministry Teams</h3>
                    
                    {ministries.map((ministry) => (
                      <div
                        key={ministry.id}
                        className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6"
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-4">
                            <div className={cn("w-16 h-16 rounded-lg flex items-center justify-center", ministry.color)}>
                              <ministry.icon className="h-8 w-8 text-white" />
                            </div>
                            <div>
                              <h4 className="text-lg font-medium">{ministry.name}</h4>
                              <p className="text-sm text-zinc-400">Team Leader: {ministry.leader}</p>
                            </div>
                          </div>
                          <Button variant="glass" size="sm" leftIcon={<UserPlusIcon className="h-4 w-4" />}>
                            Add Member
                          </Button>
                        </div>

                        <div className="grid grid-cols-4 gap-4">
                          {Array.from({ length: Math.min(8, ministry.volunteers) }, (_, i) => (
                            <div key={i} className="flex items-center gap-2 p-2 bg-white/5 rounded-lg">
                              <Image
                                src="/placeholder-user.jpg"
                                alt="Volunteer"
                                width={32}
                                height={32}
                                className="rounded-full"
                              />
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">Volunteer {i + 1}</p>
                                <p className="text-xs text-zinc-400">Active</p>
                              </div>
                            </div>
                          ))}
                        </div>

                        {ministry.volunteers > 8 && (
                          <p className="text-xs text-zinc-400 mt-2 text-center">
                            +{ministry.volunteers - 8} more volunteers
                          </p>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Volunteer Stats */}
                  <div className="col-span-4 space-y-4">
                    <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                      <h3 className="text-sm font-medium mb-3">Volunteer Overview</h3>
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm text-zinc-400">Total Active</span>
                            <span className="text-sm font-medium">{totalVolunteers}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <ArrowTrendingUpIcon className="h-3 w-3 text-green-400" />
                            <span className="text-xs text-green-400">+12 this month</span>
                          </div>
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm text-zinc-400">Avg Hours/Week</span>
                            <span className="text-sm font-medium">4.5 hrs</span>
                          </div>
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm text-zinc-400">Retention Rate</span>
                            <span className="text-sm font-medium">87%</span>
                          </div>
                          <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                            <div className="h-full w-[87%] bg-green-500" />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                      <h3 className="text-sm font-medium mb-3">Urgent Needs</h3>
                      <div className="space-y-2">
                        {[
                          { ministry: "Media & Tech", need: "Video Editor", urgency: "high" },
                          { ministry: "Children's", need: "Teachers", urgency: "medium" },
                          { ministry: "Facilities", need: "Maintenance", urgency: "low" }
                        ].map((need, idx) => (
                          <div key={idx} className="p-2 bg-white/5 rounded-lg">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium">{need.need}</p>
                                <p className="text-xs text-zinc-400">{need.ministry}</p>
                              </div>
                              <Badge 
                                variant="secondary" 
                                size="sm"
                                className={cn(
                                  need.urgency === "high" ? "bg-red-500/20 text-red-400" :
                                  need.urgency === "medium" ? "bg-amber-500/20 text-amber-400" :
                                  "bg-blue-500/20 text-blue-400"
                                )}
                              >
                                {need.urgency}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                      <h3 className="text-sm font-medium mb-3">Quick Actions</h3>
                      <div className="space-y-2">
                        <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<HandRaisedIcon className="h-4 w-4" />}>
                          Volunteer Sign-up Form
                        </Button>
                        <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<CalendarDaysIcon className="h-4 w-4" />}>
                          Schedule Training
                        </Button>
                        <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<ChatBubbleLeftRightIcon className="h-4 w-4" />}>
                          Team Communication
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "resources" && (
            <div className="h-full p-6 grid grid-cols-12 gap-4">
              {/* Resource Allocation View */}
              <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Resource Allocation</h3>
                  <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                    {[
                      { id: "budget", label: "Budget", icon: BanknotesIcon },
                      { id: "volunteers", label: "Volunteers", icon: UserGroupIcon },
                      { id: "equipment", label: "Equipment", icon: WrenchScrewdriverIcon }
                    ].map((type) => (
                      <button
                        key={type.id}
                        onClick={() => setResourceView(type.id as any)}
                        className={cn(
                          "flex items-center gap-2 px-3 py-1.5 rounded transition-colors",
                          resourceView === type.id
                            ? "bg-[var(--color-primary)] text-black"
                            : "hover:bg-white/10"
                        )}
                      >
                        <type.icon className="h-4 w-4" />
                        <span className="text-sm">{type.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Resource Visualization */}
                <div className="h-[400px] relative">
                  {resourceView === "budget" && (
                    <div className="h-full flex items-end justify-around gap-4">
                      {ministries.map((ministry) => (
                        <div key={ministry.id} className="flex-1 flex flex-col items-center">
                          <div 
                            className={cn("w-full rounded-t-lg transition-all hover:opacity-80", ministry.color)}
                            style={{ height: `${(ministry.budget / totalBudget) * 100 * 3}%` }}
                          />
                          <div className="text-center mt-2">
                            <ministry.icon className="h-5 w-5 mx-auto mb-1" />
                            <p className="text-xs">{ministry.name.split(' ')[0]}</p>
                            <p className="text-sm font-bold">${(ministry.budget / 1000).toFixed(1)}K</p>
                            <p className="text-xs text-zinc-400">{((ministry.budget / totalBudget) * 100).toFixed(0)}%</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {resourceView === "volunteers" && (
                    <div className="h-full flex items-end justify-around gap-4">
                      {ministries.map((ministry) => (
                        <div key={ministry.id} className="flex-1 flex flex-col items-center">
                          <div 
                            className={cn("w-full rounded-t-lg transition-all hover:opacity-80", ministry.color)}
                            style={{ height: `${(ministry.volunteers / totalVolunteers) * 100 * 3}%` }}
                          />
                          <div className="text-center mt-2">
                            <ministry.icon className="h-5 w-5 mx-auto mb-1" />
                            <p className="text-xs">{ministry.name.split(' ')[0]}</p>
                            <p className="text-sm font-bold">{ministry.volunteers}</p>
                            <p className="text-xs text-zinc-400">{((ministry.volunteers / totalVolunteers) * 100).toFixed(0)}%</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Summary Stats */}
                <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-[var(--border-subtle)]">
                  <div className="text-center">
                    <p className="text-2xl font-bold">${(totalBudget / 1000).toFixed(0)}K</p>
                    <p className="text-xs text-zinc-400">Total Monthly Budget</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{totalVolunteers}</p>
                    <p className="text-xs text-zinc-400">Total Volunteers</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">94%</p>
                    <p className="text-xs text-zinc-400">Resource Utilization</p>
                  </div>
                </div>
              </div>

              {/* Resource Insights */}
              <div className="col-span-4 space-y-4">
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Resource Health</h3>
                  <div className="space-y-3">
                    <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                      <div className="flex items-start gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-green-400">Well Resourced</p>
                          <p className="text-xs mt-1">4 ministries operating at optimal capacity</p>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-amber-500/10 border border-amber-500/30 rounded-lg">
                      <div className="flex items-start gap-2">
                        <ExclamationTriangleIcon className="h-4 w-4 text-amber-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-amber-400">Needs Attention</p>
                          <p className="text-xs mt-1">Media team understaffed by 30%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Budget Breakdown</h3>
                  <div className="space-y-2">
                    {[
                      { category: "Personnel", amount: 25000, percentage: 55.6 },
                      { category: "Programs", amount: 12000, percentage: 26.7 },
                      { category: "Equipment", amount: 5000, percentage: 11.1 },
                      { category: "Other", amount: 3000, percentage: 6.6 }
                    ].map((item) => (
                      <div key={item.category}>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">{item.category}</span>
                          <span className="text-sm font-medium">${(item.amount / 1000).toFixed(1)}K</span>
                        </div>
                        <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-[var(--color-primary)]"
                            style={{ width: `${item.percentage}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "impact" && (
            <div className="h-full p-6 grid grid-cols-12 gap-4">
              {/* Impact Dashboard */}
              <div className="col-span-8 space-y-4">
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                  <h3 className="text-lg font-medium mb-4">Ministry Impact Overview</h3>
                  
                  {/* Impact Metrics Grid */}
                  <div className="grid grid-cols-2 gap-4">
                    {ministries.map((ministry) => (
                      <div key={ministry.id} className="bg-white/5 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center", ministry.color)}>
                              <ministry.icon className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">{ministry.name}</h4>
                              <p className="text-xs text-zinc-400">{ministry.impactMetric}</p>
                            </div>
                          </div>
                          <Badge variant="secondary" size="sm">
                            <ArrowTrendingUpIcon className="h-3 w-3" />
                          </Badge>
                        </div>
                        
                        {/* Mini impact chart */}
                        <div className="h-20 flex items-end justify-around gap-1">
                          {Array.from({ length: 6 }, (_, i) => (
                            <div key={i} className="flex-1 bg-white/20 rounded-t" style={{ height: `${Math.random() * 80 + 20}%` }} />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Testimonials / Stories */}
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                  <h3 className="text-lg font-medium mb-4">Recent Impact Stories</h3>
                  <div className="space-y-3">
                    {[
                      { ministry: "Outreach", story: "Served 500 meals to local families this week", icon: HeartIcon },
                      { ministry: "Youth", story: "12 teens made faith commitments at retreat", icon: FireIcon },
                      { ministry: "Children's", story: "Record attendance with 234 kids on Sunday", icon: SparklesIcon }
                    ].map((story, idx) => (
                      <div key={idx} className="flex items-start gap-3 p-3 bg-white/5 rounded-lg">
                        <story.icon className="h-5 w-5 text-[var(--color-primary)] flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-sm">{story.story}</p>
                          <p className="text-xs text-zinc-400 mt-1">{story.ministry} Ministry</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Impact Stats */}
              <div className="col-span-4 space-y-4">
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">This Month's Impact</h3>
                  <div className="space-y-3">
                    <div className="text-center p-3 bg-white/5 rounded-lg">
                      <p className="text-3xl font-bold text-green-400">{ministryStats.impactedLives}</p>
                      <p className="text-xs text-zinc-400">Lives Touched</p>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-center p-2 bg-white/5 rounded-lg">
                        <p className="text-xl font-bold">156</p>
                        <p className="text-xs text-zinc-400">Salvations</p>
                      </div>
                      <div className="text-center p-2 bg-white/5 rounded-lg">
                        <p className="text-xl font-bold">89</p>
                        <p className="text-xs text-zinc-400">Baptisms</p>
                      </div>
                      <div className="text-center p-2 bg-white/5 rounded-lg">
                        <p className="text-xl font-bold">500+</p>
                        <p className="text-xs text-zinc-400">Served</p>
                      </div>
                      <div className="text-center p-2 bg-white/5 rounded-lg">
                        <p className="text-xl font-bold">1.2K</p>
                        <p className="text-xs text-zinc-400">Hours</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Impact Trends</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">Growth Rate</span>
                      <div className="flex items-center gap-2">
                        <ArrowTrendingUpIcon className="h-4 w-4 text-green-400" />
                        <span className="text-sm font-medium text-green-400">+23%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">Engagement</span>
                      <span className="text-sm font-medium">87%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">Satisfaction</span>
                      <span className="text-sm font-medium">4.8/5</span>
                    </div>
                  </div>
                </div>

                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Quick Actions</h3>
                  <div className="space-y-2">
                    <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<DocumentTextIcon className="h-4 w-4" />}>
                      Generate Impact Report
                    </Button>
                    <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<CameraIcon className="h-4 w-4" />}>
                      Capture Testimony
                    </Button>
                    <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<ChartBarIcon className="h-4 w-4" />}>
                      View Analytics
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Live Activity Panel */}
        <AnimatePresence>
          {liveMode && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 120, opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="border-t border-[var(--border-subtle)] bg-[var(--bg-glass)] backdrop-blur-xl overflow-hidden"
            >
              <div className="h-full px-6 py-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium">Live Ministry Activity</h3>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-green-400">Live</span>
                  </div>
                </div>
                
                <div className="flex gap-4 overflow-x-auto">
                  {[
                    { ministry: "Worship", activity: "Sound check in progress", time: "now" },
                    { ministry: "Children's", activity: "Check-in started", time: "5 min ago" },
                    { ministry: "Outreach", activity: "Food distribution prep", time: "10 min ago" }
                  ].map((activity, idx) => (
                    <motion.div
                      key={idx}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: idx * 0.1 }}
                      className="flex-shrink-0 w-64 p-3 bg-white/5 rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-1">
                        <p className="text-sm font-medium">{activity.ministry}</p>
                        <span className="text-xs text-zinc-400">{activity.time}</span>
                      </div>
                      <p className="text-xs text-zinc-400">{activity.activity}</p>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Floating Ministry Panels */}
      {floatingPanels.map((panel) => (
        <motion.div
          key={panel.id}
          drag
          dragMomentum={false}
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="fixed z-40 w-[400px] bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-xl"
          style={{ left: panel.position.x, top: panel.position.y }}
        >
          <div className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center", panel.ministry.color)}>
                  <panel.ministry.icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-medium">{panel.ministry.name}</h3>
                  <p className="text-sm text-zinc-400">Led by {panel.ministry.leader}</p>
                </div>
              </div>
              <button
                onClick={() => removeFloatingPanel(panel.id)}
                className="p-1 hover:bg-white/10 rounded transition-colors"
              >
                <XCircleIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="grid grid-cols-3 gap-3 mb-4">
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <p className="text-2xl font-bold">{panel.ministry.volunteers}</p>
                <p className="text-xs text-zinc-400">Volunteers</p>
              </div>
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <p className="text-2xl font-bold">${(panel.ministry.budget / 1000).toFixed(1)}K</p>
                <p className="text-xs text-zinc-400">Budget</p>
              </div>
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <p className="text-2xl font-bold">{panel.ministry.healthScore}%</p>
                <p className="text-xs text-zinc-400">Health</p>
              </div>
            </div>

            <div className="space-y-3 mb-4">
              <div>
                <p className="text-xs text-zinc-400 mb-1">Current Impact</p>
                <p className="text-sm font-medium">{panel.ministry.impactMetric}</p>
              </div>
              
              <div>
                <p className="text-xs text-zinc-400 mb-1">Active Projects</p>
                <div className="flex items-center gap-2">
                  <div className="h-2 flex-1 bg-white/10 rounded-full overflow-hidden">
                    <div className="h-full w-3/4 bg-[var(--color-primary)]" />
                  </div>
                  <span className="text-sm">{panel.ministry.activeProjects} projects</span>
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="glass" size="sm" className="flex-1">
                <EyeIcon className="h-4 w-4" />
              </Button>
              <Button variant="glass" size="sm" className="flex-1">
                <ChartBarIcon className="h-4 w-4" />
              </Button>
              <Button variant="primary" size="sm" className="flex-1">
                Manage
              </Button>
            </div>
          </div>
        </motion.div>
      ))}

      {/* Floating Command Center */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}