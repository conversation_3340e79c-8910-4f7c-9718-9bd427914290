"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommand<PERSON>enter from "@/components/ui/floating-command-center"
import { 
  BanknotesIcon,
  ChartBarIcon,
  UserGroupIcon,
  FolderIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarDaysIcon,
  CreditCardIcon,
  DocumentTextIcon,
  PlusIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  GiftIcon,
  ReceiptPercentIcon,
  BuildingLibraryIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChartPieIcon,
  ArrowsRightLeftIcon,
  SparklesIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

type ViewMode = "dashboard" | "transactions" | "donors" | "analytics"
type TimeRange = "today" | "week" | "month" | "quarter" | "year" | "custom"

interface DonorPanel {
  id: string
  donor: any
  position: { x: number; y: number }
}

export default function RevolutionaryGivingPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("dashboard")
  const [timeRange, setTimeRange] = useState<TimeRange>("month")
  const [liveMode, setLiveMode] = useState(false)
  const [donorPanels, setDonorPanels] = useState<DonorPanel[]>([])
  const [selectedFund, setSelectedFund] = useState<string | null>(null)

  // Sample financial data
  const financialStats = {
    totalGiving: 156789,
    givers: 423,
    avgGift: 371,
    recurringGivers: 187,
    newGivers: 23,
    lapsedGivers: 12,
    weeklyTrend: +12.3,
    monthlyGoal: 175000,
    goalProgress: 89.6
  }

  const funds = [
    { id: "general", name: "General Fund", amount: 89234, percentage: 56.8, color: "bg-blue-500" },
    { id: "missions", name: "Missions", amount: 23456, percentage: 15.0, color: "bg-purple-500" },
    { id: "building", name: "Building Fund", amount: 34567, percentage: 22.0, color: "bg-green-500" },
    { id: "youth", name: "Youth Ministry", amount: 9532, percentage: 6.2, color: "bg-amber-500" }
  ]

  const recentTransactions = [
    { id: 1, donor: "John Smith", amount: 500, fund: "General", method: "Online", time: "2 min ago", recurring: true },
    { id: 2, donor: "Anonymous", amount: 1000, fund: "Building", method: "Check", time: "15 min ago", recurring: false },
    { id: 3, donor: "Sarah Johnson", amount: 150, fund: "Missions", method: "Online", time: "1 hour ago", recurring: true },
    { id: 4, donor: "Mike Davis", amount: 75, fund: "Youth", method: "Cash", time: "2 hours ago", recurring: false }
  ]

  const addDonorPanel = (donor: any) => {
    const newPanel: DonorPanel = {
      id: donor.id,
      donor,
      position: { x: window.innerWidth / 2, y: window.innerHeight / 2 }
    }
    setDonorPanels([...donorPanels, newPanel])
  }

  const removeDonorPanel = (id: string) => {
    setDonorPanels(donorPanels.filter(panel => panel.id !== id))
  }

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarRevolutionary />

      {/* Main Content - NO SCROLLING */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Primary Navigation Bar - Sticky */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] sticky top-0 z-30">
          <div className="h-full px-6 flex items-center justify-between">
            {/* View Mode Switcher */}
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">Giving</h1>
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "dashboard", label: "Dashboard", icon: ChartPieIcon },
                  { id: "transactions", label: "Transactions", icon: ArrowsRightLeftIcon, badge: liveMode ? "LIVE" : null },
                  { id: "donors", label: "Donors", icon: UserGroupIcon },
                  { id: "analytics", label: "Analytics", icon: ChartBarIcon }
                ].map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => setViewMode(mode.id as ViewMode)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded transition-all",
                      viewMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <mode.icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{mode.label}</span>
                    {mode.badge && (
                      <Badge variant="secondary" size="sm" className="bg-red-500 text-white animate-pulse">
                        {mode.badge}
                      </Badge>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              {/* Time Range Selector */}
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "today", label: "Today" },
                  { id: "week", label: "Week" },
                  { id: "month", label: "Month" },
                  { id: "quarter", label: "Quarter" },
                  { id: "year", label: "Year" }
                ].map((range) => (
                  <button
                    key={range.id}
                    onClick={() => setTimeRange(range.id as TimeRange)}
                    className={cn(
                      "px-3 py-1.5 rounded text-sm transition-colors",
                      timeRange === range.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    {range.label}
                  </button>
                ))}
              </div>

              <button
                onClick={() => setLiveMode(!liveMode)}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  liveMode ? "bg-green-500/20 text-green-400" : "hover:bg-white/10"
                )}
                title="Toggle Live Mode"
              >
                <SparklesIcon className="h-5 w-5" />
              </button>

              <Button variant="glass" size="sm" leftIcon={<ArrowDownTrayIcon className="h-4 w-4" />}>
                Export
              </Button>

              <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Record Gift
              </Button>
            </div>
          </div>
        </div>

        {/* Financial Overview Bar - Always Visible */}
        <div className="h-20 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center">
          <div className="grid grid-cols-6 gap-6 w-full">
            <div>
              <p className="text-3xl font-bold">${(financialStats.totalGiving / 1000).toFixed(1)}K</p>
              <p className="text-xs text-zinc-400">Total Giving</p>
              <div className="flex items-center gap-1 mt-1">
                <ArrowTrendingUpIcon className="h-3 w-3 text-green-400" />
                <span className="text-xs text-green-400">+{financialStats.weeklyTrend}%</span>
              </div>
            </div>
            <div>
              <p className="text-3xl font-bold">{financialStats.givers}</p>
              <p className="text-xs text-zinc-400">Givers</p>
              <div className="flex items-center gap-1 mt-1">
                <span className="text-xs text-blue-400">+{financialStats.newGivers} new</span>
              </div>
            </div>
            <div>
              <p className="text-3xl font-bold">${financialStats.avgGift}</p>
              <p className="text-xs text-zinc-400">Avg Gift</p>
            </div>
            <div>
              <p className="text-3xl font-bold">{financialStats.recurringGivers}</p>
              <p className="text-xs text-zinc-400">Recurring</p>
              <div className="flex items-center gap-1 mt-1">
                <CheckCircleIcon className="h-3 w-3 text-green-400" />
                <span className="text-xs text-green-400">Stable</span>
              </div>
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <p className="text-2xl font-bold">{financialStats.goalProgress}%</p>
                <Badge variant="secondary" size="sm">Monthly Goal</Badge>
              </div>
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-[var(--color-primary)] transition-all duration-1000"
                  style={{ width: `${financialStats.goalProgress}%` }}
                />
              </div>
            </div>
            <div className="text-right">
              <p className="text-xs text-zinc-400 mb-1">Need by month end</p>
              <p className="text-2xl font-bold text-amber-400">
                ${((financialStats.monthlyGoal - financialStats.totalGiving) / 1000).toFixed(1)}K
              </p>
            </div>
          </div>
        </div>

        {/* Main Content Area - Dynamic Based on View Mode */}
        <div className="flex-1 relative">
          {viewMode === "dashboard" && (
            <div className="h-full p-6 grid grid-cols-12 gap-4">
              {/* Fund Distribution */}
              <div className="col-span-5 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <h3 className="text-lg font-medium mb-4">Fund Distribution</h3>
                
                {/* Donut Chart Visualization */}
                <div className="relative h-64 mb-6">
                  <svg className="w-full h-full" viewBox="0 0 200 200">
                    {/* Background circle */}
                    <circle cx="100" cy="100" r="80" fill="none" stroke="var(--border-subtle)" strokeWidth="30" />
                    
                    {/* Fund segments */}
                    {funds.map((fund, idx) => {
                      const offset = funds.slice(0, idx).reduce((sum, f) => sum + f.percentage, 0)
                      const circumference = 2 * Math.PI * 80
                      const strokeDasharray = `${(fund.percentage / 100) * circumference} ${circumference}`
                      const strokeDashoffset = -(offset / 100) * circumference
                      
                      return (
                        <circle
                          key={fund.id}
                          cx="100"
                          cy="100"
                          r="80"
                          fill="none"
                          stroke={`rgb(${idx === 0 ? '59, 130, 246' : idx === 1 ? '168, 85, 247' : idx === 2 ? '34, 197, 94' : '251, 146, 60'})`}
                          strokeWidth="30"
                          strokeDasharray={strokeDasharray}
                          strokeDashoffset={strokeDashoffset}
                          transform="rotate(-90 100 100)"
                          className="cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => setSelectedFund(fund.id)}
                        />
                      )
                    })}
                    
                    {/* Center text */}
                    <text x="100" y="100" textAnchor="middle" dominantBaseline="middle" className="fill-white">
                      <tspan x="100" dy="-10" className="text-3xl font-bold">${(financialStats.totalGiving / 1000).toFixed(0)}K</tspan>
                      <tspan x="100" dy="30" className="text-sm opacity-60">Total</tspan>
                    </text>
                  </svg>
                </div>

                {/* Fund List */}
                <div className="space-y-3">
                  {funds.map((fund) => (
                    <div
                      key={fund.id}
                      className={cn(
                        "flex items-center justify-between p-3 rounded-lg border transition-all cursor-pointer",
                        selectedFund === fund.id
                          ? "bg-white/10 border-[var(--color-primary)]/30"
                          : "bg-white/5 border-[var(--border-subtle)] hover:bg-white/10"
                      )}
                      onClick={() => setSelectedFund(fund.id)}
                    >
                      <div className="flex items-center gap-3">
                        <div className={cn("w-3 h-3 rounded-full", fund.color)} />
                        <div>
                          <p className="text-sm font-medium">{fund.name}</p>
                          <p className="text-xs text-zinc-400">{fund.percentage}% of total</p>
                        </div>
                      </div>
                      <p className="text-lg font-bold">${(fund.amount / 1000).toFixed(1)}K</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Live Transaction Feed */}
              <div className="col-span-4 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Recent Transactions</h3>
                  {liveMode && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      <span className="text-xs text-green-400">Live</span>
                    </div>
                  )}
                </div>

                <div className="space-y-3 max-h-[calc(100vh-400px)] overflow-y-auto custom-scrollbar">
                  {recentTransactions.map((transaction) => (
                    <motion.div
                      key={transaction.id}
                      initial={liveMode ? { opacity: 0, x: -20 } : {}}
                      animate={{ opacity: 1, x: 0 }}
                      className="p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                      onClick={() => addDonorPanel({ id: `donor-${transaction.id}`, name: transaction.donor })}
                    >
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <p className="text-sm font-medium">{transaction.donor}</p>
                            {transaction.recurring && (
                              <Badge variant="secondary" size="sm" className="bg-blue-500/20 text-blue-400">
                                Recurring
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-zinc-400 mt-1">
                            {transaction.fund} • {transaction.method}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-green-400">${transaction.amount}</p>
                          <p className="text-xs text-zinc-400">{transaction.time}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <Button variant="glass" size="sm" className="w-full mt-4">
                  View All Transactions
                </Button>
              </div>

              {/* Quick Stats */}
              <div className="col-span-3 space-y-4">
                {/* Giving Methods */}
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Giving Methods</h3>
                  <div className="space-y-2">
                    {[
                      { method: "Online", amount: 89234, percentage: 56.8, icon: CreditCardIcon },
                      { method: "Check", amount: 45678, percentage: 29.1, icon: DocumentTextIcon },
                      { method: "Cash", amount: 21877, percentage: 14.1, icon: BanknotesIcon }
                    ].map((method) => (
                      <div key={method.method} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <method.icon className="h-4 w-4 text-zinc-400" />
                          <span className="text-sm">{method.method}</span>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">${(method.amount / 1000).toFixed(1)}K</p>
                          <p className="text-xs text-zinc-400">{method.percentage}%</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Donor Insights */}
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Donor Insights</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">New Donors</span>
                      <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                        +{financialStats.newGivers}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">Lapsed Donors</span>
                      <Badge variant="secondary" className="bg-amber-500/20 text-amber-400">
                        {financialStats.lapsedGivers}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">Retention Rate</span>
                      <span className="text-sm font-medium">94.2%</span>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Quick Actions</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="glass" size="sm">
                      <ReceiptPercentIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="glass" size="sm">
                      <GiftIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="glass" size="sm">
                      <EnvelopeIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="glass" size="sm">
                      <PhoneIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "transactions" && (
            <div className="h-full p-6">
              {/* Transaction Table with Live Updates */}
              <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl overflow-hidden">
                {/* Table Header */}
                <div className="grid grid-cols-12 gap-4 p-4 border-b border-[var(--border-subtle)] bg-white/5">
                  <div className="col-span-3 text-xs font-medium text-zinc-400">DONOR</div>
                  <div className="col-span-2 text-xs font-medium text-zinc-400">AMOUNT</div>
                  <div className="col-span-2 text-xs font-medium text-zinc-400">FUND</div>
                  <div className="col-span-2 text-xs font-medium text-zinc-400">METHOD</div>
                  <div className="col-span-2 text-xs font-medium text-zinc-400">DATE</div>
                  <div className="col-span-1 text-xs font-medium text-zinc-400">STATUS</div>
                </div>

                {/* Table Body */}
                <div className="overflow-y-auto max-h-[calc(100vh-300px)] custom-scrollbar">
                  {Array.from({ length: 20 }, (_, i) => (
                    <motion.div
                      key={i}
                      initial={liveMode && i === 0 ? { opacity: 0, y: -20 } : {}}
                      animate={{ opacity: 1, y: 0 }}
                      className="grid grid-cols-12 gap-4 p-4 border-b border-[var(--border-subtle)] hover:bg-white/5 transition-colors cursor-pointer"
                    >
                      <div className="col-span-3">
                        <p className="text-sm font-medium">John Doe {i + 1}</p>
                        <p className="text-xs text-zinc-400">johndoe{i}@email.com</p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-lg font-bold text-green-400">${(Math.random() * 1000).toFixed(0)}</p>
                      </div>
                      <div className="col-span-2">
                        <Badge variant="secondary" size="sm">General Fund</Badge>
                      </div>
                      <div className="col-span-2 flex items-center gap-2">
                        <CreditCardIcon className="h-4 w-4 text-zinc-400" />
                        <span className="text-sm">Online</span>
                      </div>
                      <div className="col-span-2">
                        <p className="text-sm">Dec 15, 2024</p>
                        <p className="text-xs text-zinc-400">10:30 AM</p>
                      </div>
                      <div className="col-span-1">
                        <CheckCircleIcon className="h-5 w-5 text-green-400" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {viewMode === "donors" && (
            <div className="h-full p-6 grid grid-cols-12 gap-4">
              {/* Donor Segments */}
              <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <h3 className="text-lg font-medium mb-4">Donor Segments</h3>
                
                {/* 2x2 Matrix */}
                <div className="h-[calc(100%-3rem)] relative">
                  <div className="absolute inset-0 grid grid-cols-2 grid-rows-2 gap-4">
                    {/* High Value, High Frequency */}
                    <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-6">
                      <h4 className="text-sm font-medium text-green-400 mb-2">Champions</h4>
                      <p className="text-3xl font-bold mb-1">87</p>
                      <p className="text-xs text-zinc-400">High value, frequent</p>
                      <div className="mt-4">
                        <p className="text-2xl font-bold">${(87 * 2500 / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-zinc-400">Total contribution</p>
                      </div>
                    </div>

                    {/* Low Value, High Frequency */}
                    <div className="bg-blue-500/10 border border-blue-500/30 rounded-xl p-6">
                      <h4 className="text-sm font-medium text-blue-400 mb-2">Loyal</h4>
                      <p className="text-3xl font-bold mb-1">234</p>
                      <p className="text-xs text-zinc-400">Regular small gifts</p>
                      <div className="mt-4">
                        <p className="text-2xl font-bold">${(234 * 150 / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-zinc-400">Total contribution</p>
                      </div>
                    </div>

                    {/* High Value, Low Frequency */}
                    <div className="bg-purple-500/10 border border-purple-500/30 rounded-xl p-6">
                      <h4 className="text-sm font-medium text-purple-400 mb-2">Major Donors</h4>
                      <p className="text-3xl font-bold mb-1">45</p>
                      <p className="text-xs text-zinc-400">Large occasional gifts</p>
                      <div className="mt-4">
                        <p className="text-2xl font-bold">${(45 * 5000 / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-zinc-400">Total contribution</p>
                      </div>
                    </div>

                    {/* Low Value, Low Frequency */}
                    <div className="bg-amber-500/10 border border-amber-500/30 rounded-xl p-6">
                      <h4 className="text-sm font-medium text-amber-400 mb-2">Occasional</h4>
                      <p className="text-3xl font-bold mb-1">156</p>
                      <p className="text-xs text-zinc-400">Infrequent small gifts</p>
                      <div className="mt-4">
                        <p className="text-2xl font-bold">${(156 * 75 / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-zinc-400">Total contribution</p>
                      </div>
                    </div>
                  </div>

                  {/* Axis Labels */}
                  <div className="absolute left-1/2 bottom-0 -translate-x-1/2 text-xs text-zinc-400">
                    Gift Size →
                  </div>
                  <div className="absolute left-0 top-1/2 -translate-y-1/2 -rotate-90 text-xs text-zinc-400">
                    Frequency →
                  </div>
                </div>
              </div>

              {/* Top Donors */}
              <div className="col-span-4 space-y-4">
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Top Donors This Month</h3>
                  <div className="space-y-3">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <div
                        key={i}
                        className="flex items-center justify-between p-2 hover:bg-white/5 rounded-lg transition-colors cursor-pointer"
                        onClick={() => addDonorPanel({ id: `top-donor-${i}`, name: `Top Donor ${i}` })}
                      >
                        <div className="flex items-center gap-3">
                          <Image
                            src="/placeholder-user.jpg"
                            alt="Donor"
                            width={32}
                            height={32}
                            className="rounded-full"
                          />
                          <div>
                            <p className="text-sm font-medium">Donor {i}</p>
                            <p className="text-xs text-zinc-400">5 gifts</p>
                          </div>
                        </div>
                        <p className="text-sm font-bold">${(5000 - i * 500)}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Donor Actions</h3>
                  <div className="space-y-2">
                    <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<EnvelopeIcon className="h-4 w-4" />}>
                      Send Thank You Notes
                    </Button>
                    <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<DocumentTextIcon className="h-4 w-4" />}>
                      Generate Statements
                    </Button>
                    <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
                      Create Donor Segment
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {viewMode === "analytics" && (
            <div className="h-full p-6 grid grid-cols-12 gap-4">
              {/* Trend Chart */}
              <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Giving Trends</h3>
                  <div className="flex items-center gap-2">
                    <Button variant="glass" size="sm">Daily</Button>
                    <Button variant="primary" size="sm">Weekly</Button>
                    <Button variant="glass" size="sm">Monthly</Button>
                  </div>
                </div>

                {/* Simplified Chart Visualization */}
                <div className="h-64 relative">
                  <div className="absolute inset-0 flex items-end justify-around">
                    {Array.from({ length: 12 }, (_, i) => (
                      <div key={i} className="flex-1 mx-1">
                        <div
                          className="bg-[var(--color-primary)] rounded-t-lg transition-all hover:opacity-80"
                          style={{ height: `${Math.random() * 80 + 20}%` }}
                        />
                        <p className="text-xs text-center mt-2">W{i + 1}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Summary Stats */}
                <div className="grid grid-cols-4 gap-4 mt-6 pt-6 border-t border-[var(--border-subtle)]">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-400">+23%</p>
                    <p className="text-xs text-zinc-400">vs Last Period</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">$423K</p>
                    <p className="text-xs text-zinc-400">Total This Period</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">892</p>
                    <p className="text-xs text-zinc-400">Total Gifts</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">$474</p>
                    <p className="text-xs text-zinc-400">Average Gift</p>
                  </div>
                </div>
              </div>

              {/* Analytics Insights */}
              <div className="col-span-4 space-y-4">
                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Key Insights</h3>
                  <div className="space-y-3">
                    <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                      <div className="flex items-start gap-2">
                        <ArrowTrendingUpIcon className="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-green-400">Strong Growth</p>
                          <p className="text-xs mt-1">Online giving up 34% this month</p>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                      <div className="flex items-start gap-2">
                        <UserGroupIcon className="h-4 w-4 text-blue-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-blue-400">New Donors</p>
                          <p className="text-xs mt-1">23 first-time givers this month</p>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-amber-500/10 border border-amber-500/30 rounded-lg">
                      <div className="flex items-start gap-2">
                        <ExclamationTriangleIcon className="h-4 w-4 text-amber-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-amber-400">Attention Needed</p>
                          <p className="text-xs mt-1">12 regular donors haven't given in 60+ days</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
                  <h3 className="text-sm font-medium mb-3">Projections</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">End of Month</span>
                      <span className="text-sm font-medium">$198K</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">End of Quarter</span>
                      <span className="text-sm font-medium">$578K</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-zinc-400">End of Year</span>
                      <span className="text-sm font-medium">$2.3M</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bottom Action Bar - Context Sensitive */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-t border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <div className="flex items-center gap-4">
            {viewMode === "transactions" && (
              <>
                <span className="text-sm text-zinc-400">
                  Showing {recentTransactions.length} of 1,234 transactions
                </span>
                <Button variant="glass" size="sm">Filter</Button>
              </>
            )}
            {viewMode === "dashboard" && selectedFund && (
              <>
                <span className="text-sm">
                  Selected: <span className="font-medium">{funds.find(f => f.id === selectedFund)?.name}</span>
                </span>
                <Button variant="glass" size="sm">View Fund Details</Button>
              </>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="glass" size="sm" leftIcon={<DocumentTextIcon className="h-4 w-4" />}>
              Reports
            </Button>
            <Button variant="glass" size="sm" leftIcon={<BuildingLibraryIcon className="h-4 w-4" />}>
              Deposit
            </Button>
            <Button variant="primary" size="sm" leftIcon={<CurrencyDollarIcon className="h-4 w-4" />}>
              Quick Give
            </Button>
          </div>
        </div>
      </div>

      {/* Floating Donor Panels */}
      {donorPanels.map((panel) => (
        <motion.div
          key={panel.id}
          drag
          dragMomentum={false}
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="fixed z-40 w-80 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl shadow-glow-xl"
          style={{ left: panel.position.x, top: panel.position.y }}
        >
          <div className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <Image
                  src="/placeholder-user.jpg"
                  alt="Donor"
                  width={48}
                  height={48}
                  className="rounded-full"
                />
                <div>
                  <h3 className="font-medium">{panel.donor.name}</h3>
                  <p className="text-xs text-zinc-400">Donor ID: {panel.donor.id}</p>
                </div>
              </div>
              <button
                onClick={() => removeDonorPanel(panel.id)}
                className="p-1 hover:bg-white/10 rounded transition-colors"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="bg-white/5 rounded-lg p-2">
                  <p className="text-lg font-bold">$3.2K</p>
                  <p className="text-xs text-zinc-400">Total Given</p>
                </div>
                <div className="bg-white/5 rounded-lg p-2">
                  <p className="text-lg font-bold">24</p>
                  <p className="text-xs text-zinc-400">Gifts</p>
                </div>
                <div className="bg-white/5 rounded-lg p-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-400 mx-auto mb-1" />
                  <p className="text-xs text-zinc-400">Recurring</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-zinc-400">Last Gift</span>
                  <span>$250 • 3 days ago</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-zinc-400">Average Gift</span>
                  <span>$133</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-zinc-400">Member Since</span>
                  <span>2019</span>
                </div>
              </div>

              <div className="flex gap-2 pt-2">
                <Button variant="glass" size="sm" className="flex-1">
                  <EnvelopeIcon className="h-4 w-4" />
                </Button>
                <Button variant="glass" size="sm" className="flex-1">
                  <PhoneIcon className="h-4 w-4" />
                </Button>
                <Button variant="primary" size="sm" className="flex-1">
                  View Profile
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      ))}

      {/* Floating Command Center */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}