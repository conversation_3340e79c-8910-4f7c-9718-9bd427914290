"use client"

import React, { useState, useRef, useEffect } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence, useMotionValue, useTransform } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommand<PERSON>enter from "@/components/ui/floating-command-center"
import { 
  ChartBarIcon,
  UserGroupIcon,
  CalendarDaysIcon,
  BanknotesIcon,
  TicketIcon,
  UserIcon,
  MusicalNoteIcon,
  BellIcon,
  SparklesIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ClockIcon,
  FireIcon,
  ChartPieIcon,
  Squares2X2Icon,
  ArrowsPointingInIcon,
  ArrowsPointingOutIcon,
  EyeIcon,
  PlayIcon,
  PauseIcon,
  VideoCameraIcon,
  MapPinIcon,
  HeartIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ArrowUpIcon,
  XMarkIcon,
  PlusIcon,
  CubeIcon,
  HomeIcon,
  CogIcon,
  PhotoIcon,
  MicrophoneIcon,
  WifiIcon,
  ComputerDesktopIcon,
  UserPlusIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import Link from "next/link"

type DockItem = {
  id: string
  label: string
  icon: React.ComponentType<any>
  color: string
  href?: string
  preview?: React.ReactNode
  badge?: string | number
  pulse?: boolean
}

export default function RevolutionaryDashboard() {
  const [hoveredDockItem, setHoveredDockItem] = useState<string | null>(null)
  const [expandedDock, setExpandedDock] = useState(false)
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null)
  const [liveMode, setLiveMode] = useState(true)
  const [currentTime, setCurrentTime] = useState(new Date())
  
  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  // Dock items with previews
  const dockItems: DockItem[] = [
    {
      id: "dashboard",
      label: "Overview",
      icon: HomeIcon,
      color: "bg-gradient-to-br from-blue-500 to-blue-600",
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Quick Stats</h3>
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-white/5 rounded-lg p-3">
              <p className="text-2xl font-bold">342</p>
              <p className="text-xs text-zinc-400">Attendance Today</p>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <p className="text-2xl font-bold">$12.8K</p>
              <p className="text-xs text-zinc-400">Weekly Giving</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "services",
      label: "Services",
      icon: MusicalNoteIcon,
      color: "bg-gradient-to-br from-purple-500 to-purple-600",
      href: "/services",
      badge: "Live",
      pulse: true,
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Current Service</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Sunday Morning</span>
              <Badge variant="secondary" className="bg-green-500/20 text-green-400">Live</Badge>
            </div>
            <div className="h-2 bg-white/10 rounded-full overflow-hidden">
              <div className="h-full w-3/4 bg-purple-500" />
            </div>
            <p className="text-xs text-zinc-400">45 min remaining</p>
          </div>
        </div>
      )
    },
    {
      id: "people",
      label: "People",
      icon: UserGroupIcon,
      color: "bg-gradient-to-br from-green-500 to-green-600",
      href: "/people",
      badge: 847,
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Recent Activity</h3>
          <div className="space-y-2">
            {["Johnson Family", "Sarah Chen", "Mike Davis"].map((name, i) => (
              <div key={i} className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-white/10" />
                <span className="text-sm">{name} checked in</span>
              </div>
            ))}
          </div>
        </div>
      )
    },
    {
      id: "giving",
      label: "Giving",
      icon: BanknotesIcon,
      color: "bg-gradient-to-br from-amber-500 to-amber-600",
      href: "/giving",
      badge: "$45.7K",
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Today's Giving</h3>
          <div className="space-y-2">
            <p className="text-3xl font-bold">$12,840</p>
            <div className="flex items-center gap-2">
              <ArrowTrendingUpIcon className="h-4 w-4 text-green-400" />
              <span className="text-sm text-green-400">+23% vs last week</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "events",
      label: "Events",
      icon: CalendarDaysIcon,
      color: "bg-gradient-to-br from-pink-500 to-pink-600",
      href: "/events",
      badge: 12,
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Upcoming Today</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span>Youth Group</span>
              <span className="text-zinc-400">6:30 PM</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Prayer Meeting</span>
              <span className="text-zinc-400">7:00 PM</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "checkin",
      label: "Check-in",
      icon: TicketIcon,
      color: "bg-gradient-to-br from-cyan-500 to-cyan-600",
      href: "/check-in",
      badge: 234,
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Check-in Stats</h3>
          <div className="grid grid-cols-3 gap-2 text-center">
            <div>
              <p className="text-lg font-bold">234</p>
              <p className="text-xs text-zinc-400">Total</p>
            </div>
            <div>
              <p className="text-lg font-bold">45</p>
              <p className="text-xs text-zinc-400">Kids</p>
            </div>
            <div>
              <p className="text-lg font-bold">12</p>
              <p className="text-xs text-zinc-400">New</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "groups",
      label: "Groups",
      icon: UserGroupIcon,
      color: "bg-gradient-to-br from-indigo-500 to-indigo-600",
      href: "/groups",
      badge: 67,
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Active This Week</h3>
          <div className="space-y-1 text-sm">
            <div className="flex items-center justify-between">
              <span>Small Groups</span>
              <span className="text-zinc-400">23 meetings</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Bible Studies</span>
              <span className="text-zinc-400">12 meetings</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "media",
      label: "Media",
      icon: PhotoIcon,
      color: "bg-gradient-to-br from-red-500 to-red-600",
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Live Stream</h3>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-sm">234 viewers</span>
            </div>
            <div className="aspect-video bg-white/5 rounded-lg" />
          </div>
        </div>
      )
    },
    {
      id: "settings",
      label: "Settings",
      icon: CogIcon,
      color: "bg-gradient-to-br from-zinc-600 to-zinc-700",
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Quick Settings</h3>
          <div className="space-y-2">
            <button className="w-full text-left text-sm p-2 hover:bg-white/5 rounded">Notifications</button>
            <button className="w-full text-left text-sm p-2 hover:bg-white/5 rounded">Display</button>
            <button className="w-full text-left text-sm p-2 hover:bg-white/5 rounded">Security</button>
          </div>
        </div>
      )
    }
  ]

  // Main dashboard widgets
  const widgets = [
    {
      id: "live-service",
      size: "large",
      content: (
        <div className="relative h-full">
          <div className="absolute top-4 left-4 z-10">
            <Badge variant="secondary" className="bg-green-500/20 text-green-400">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1" />
              Sunday Service Live
            </Badge>
          </div>
          
          {/* Live Service Visual */}
          <div className="h-full bg-gradient-to-br from-purple-900/20 to-purple-800/10 rounded-2xl p-6 flex flex-col">
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="w-32 h-32 mx-auto mb-4 bg-purple-500/20 rounded-full flex items-center justify-center">
                  <MusicalNoteIcon className="h-16 w-16 text-purple-400" />
                </div>
                <h3 className="text-2xl font-bold mb-2">Worship in Progress</h3>
                <p className="text-zinc-400 mb-4">342 attendees • 45 min remaining</p>
                <div className="flex justify-center gap-2">
                  <Button variant="glass" size="sm">View Details</Button>
                  <Button variant="primary" size="sm">Control Room</Button>
                </div>
              </div>
            </div>
            
            {/* Service Timeline */}
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-zinc-400">Service Progress</span>
                <span>65%</span>
              </div>
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <div className="h-full w-[65%] bg-purple-500 transition-all" />
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "real-time-stats",
      size: "medium",
      content: (
        <div className="h-full bg-gradient-to-br from-blue-900/20 to-blue-800/10 rounded-2xl p-6">
          <h3 className="text-lg font-medium mb-4">Real-time Stats</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-white/5 rounded-xl">
              <UserGroupIcon className="h-8 w-8 mx-auto mb-2 text-blue-400" />
              <p className="text-2xl font-bold">342</p>
              <p className="text-xs text-zinc-400">Checked In</p>
            </div>
            <div className="text-center p-4 bg-white/5 rounded-xl">
              <BanknotesIcon className="h-8 w-8 mx-auto mb-2 text-green-400" />
              <p className="text-2xl font-bold">$12.8K</p>
              <p className="text-xs text-zinc-400">Today's Giving</p>
            </div>
            <div className="text-center p-4 bg-white/5 rounded-xl">
              <WifiIcon className="h-8 w-8 mx-auto mb-2 text-purple-400" />
              <p className="text-2xl font-bold">234</p>
              <p className="text-xs text-zinc-400">Online Viewers</p>
            </div>
            <div className="text-center p-4 bg-white/5 rounded-xl">
              <HeartIcon className="h-8 w-8 mx-auto mb-2 text-pink-400" />
              <p className="text-2xl font-bold">12</p>
              <p className="text-xs text-zinc-400">Prayer Requests</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "activity-feed",
      size: "medium",
      content: (
        <div className="h-full bg-gradient-to-br from-green-900/20 to-green-800/10 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Live Activity</h3>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-xs text-green-400">Live</span>
            </div>
          </div>
          
          <div className="space-y-3 max-h-64 overflow-y-auto custom-scrollbar">
            {[
              { icon: TicketIcon, text: "Johnson Family checked in", time: "just now", color: "text-green-400" },
              { icon: BanknotesIcon, text: "$500 donation received", time: "2 min ago", color: "text-purple-400" },
              { icon: UserPlusIcon, text: "New visitor registered", time: "5 min ago", color: "text-blue-400" },
              { icon: MicrophoneIcon, text: "Worship team ready", time: "10 min ago", color: "text-amber-400" }
            ].map((item, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: i * 0.1 }}
                className="flex items-center gap-3 p-2 bg-white/5 rounded-lg"
              >
                <item.icon className={cn("h-5 w-5", item.color)} />
                <div className="flex-1">
                  <p className="text-sm">{item.text}</p>
                  <p className="text-xs text-zinc-400">{item.time}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )
    },
    {
      id: "quick-actions",
      size: "small",
      content: (
        <div className="h-full bg-gradient-to-br from-amber-900/20 to-amber-800/10 rounded-2xl p-6">
          <h3 className="text-lg font-medium mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            {[
              { icon: PlusIcon, label: "Check-in", color: "bg-green-500" },
              { icon: BanknotesIcon, label: "Record Gift", color: "bg-purple-500" },
              { icon: CalendarDaysIcon, label: "Schedule", color: "bg-blue-500" },
              { icon: BellIcon, label: "Announce", color: "bg-amber-500" }
            ].map((action, i) => (
              <button
                key={i}
                className="p-3 bg-white/5 hover:bg-white/10 rounded-xl transition-all group"
              >
                <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center mx-auto mb-2", action.color)}>
                  <action.icon className="h-5 w-5 text-white" />
                </div>
                <p className="text-xs">{action.label}</p>
              </button>
            ))}
          </div>
        </div>
      )
    },
    {
      id: "upcoming-events",
      size: "small",
      content: (
        <div className="h-full bg-gradient-to-br from-pink-900/20 to-pink-800/10 rounded-2xl p-6">
          <h3 className="text-lg font-medium mb-4">Today's Events</h3>
          <div className="space-y-3">
            {[
              { time: "6:30 PM", event: "Youth Group", location: "Youth Center" },
              { time: "7:00 PM", event: "Prayer Meeting", location: "Chapel" },
              { time: "8:00 PM", event: "Worship Practice", location: "Main Hall" }
            ].map((item, i) => (
              <div key={i} className="p-2 bg-white/5 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{item.event}</span>
                  <Badge variant="secondary" size="sm">{item.time}</Badge>
                </div>
                <p className="text-xs text-zinc-400 mt-1">{item.location}</p>
              </div>
            ))}
          </div>
        </div>
      )
    },
    {
      id: "system-health",
      size: "small",
      content: (
        <div className="h-full bg-gradient-to-br from-cyan-900/20 to-cyan-800/10 rounded-2xl p-6">
          <h3 className="text-lg font-medium mb-4">System Health</h3>
          <div className="space-y-3">
            {[
              { system: "Check-in Kiosks", status: "online", health: 100 },
              { system: "Live Stream", status: "broadcasting", health: 98 },
              { system: "Database", status: "optimal", health: 100 },
              { system: "Network", status: "stable", health: 95 }
            ].map((item, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    item.health === 100 ? "bg-green-500" : "bg-amber-500"
                  )} />
                  <span className="text-sm">{item.system}</span>
                </div>
                <span className="text-xs text-zinc-400">{item.status}</span>
              </div>
            ))}
          </div>
        </div>
      )
    }
  ]

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarRevolutionary />

      {/* Main Dashboard Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Bar */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold">Church Command Center</h1>
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
              {currentTime.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
            </Badge>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Live Time */}
            <div className="text-2xl font-mono">
              {currentTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit' })}
            </div>
            
            {/* Live Mode Toggle */}
            <button
              onClick={() => setLiveMode(!liveMode)}
              className={cn(
                "px-4 py-2 rounded-lg flex items-center gap-2 transition-all",
                liveMode ? "bg-green-500/20 text-green-400" : "bg-white/10"
              )}
            >
              {liveMode ? <PlayIcon className="h-4 w-4" /> : <PauseIcon className="h-4 w-4" />}
              <span className="text-sm font-medium">{liveMode ? "Live Mode" : "Paused"}</span>
            </button>
          </div>
        </div>

        {/* Widget Grid */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="grid grid-cols-12 gap-4 auto-rows-[200px]">
            {/* Live Service - Large Widget */}
            <div className="col-span-6 row-span-2">
              {widgets.find(w => w.id === "live-service")?.content}
            </div>
            
            {/* Real-time Stats - Medium Widget */}
            <div className="col-span-6">
              {widgets.find(w => w.id === "real-time-stats")?.content}
            </div>
            
            {/* Activity Feed - Medium Widget */}
            <div className="col-span-6">
              {widgets.find(w => w.id === "activity-feed")?.content}
            </div>
            
            {/* Quick Actions - Small Widget */}
            <div className="col-span-4">
              {widgets.find(w => w.id === "quick-actions")?.content}
            </div>
            
            {/* Upcoming Events - Small Widget */}
            <div className="col-span-4">
              {widgets.find(w => w.id === "upcoming-events")?.content}
            </div>
            
            {/* System Health - Small Widget */}
            <div className="col-span-4">
              {widgets.find(w => w.id === "system-health")?.content}
            </div>
          </div>
        </div>

        {/* Bottom Dock */}
        <div className={cn(
          "absolute bottom-0 left-80 right-0 bg-[var(--bg-glass)] backdrop-blur-2xl border-t border-[var(--border-subtle)] transition-all duration-300",
          expandedDock ? "h-64" : "h-20"
        )}>
          {/* Dock Expand Handle */}
          <button
            onClick={() => setExpandedDock(!expandedDock)}
            className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-6 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-full flex items-center justify-center hover:bg-white/10 transition-colors"
          >
            {expandedDock ? <ChevronDownIcon className="h-4 w-4" /> : <ChevronUpIcon className="h-4 w-4" />}
          </button>

          {/* Dock Items */}
          <div className="h-20 px-8 flex items-center justify-center gap-4">
            {dockItems.map((item) => (
              <div
                key={item.id}
                className="relative"
                onMouseEnter={() => setHoveredDockItem(item.id)}
                onMouseLeave={() => setHoveredDockItem(null)}
              >
                {/* Hover Preview */}
                <AnimatePresence>
                  {hoveredDockItem === item.id && item.preview && !expandedDock && (
                    <motion.div
                      initial={{ opacity: 0, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 10, scale: 0.95 }}
                      className="absolute bottom-full mb-4 left-1/2 -translate-x-1/2 w-64 bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)] rounded-xl shadow-glow-2xl"
                    >
                      {item.preview}
                      {/* Arrow */}
                      <div className="absolute top-full left-1/2 -translate-x-1/2 -translate-y-px">
                        <div className="w-3 h-3 bg-[var(--bg-glass)] border-r border-b border-[var(--border-subtle)] rotate-45" />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Dock Item */}
                <motion.div
                  whileHover={{ scale: 1.2, y: -10 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative"
                >
                  {item.href ? (
                    <Link href={item.href}>
                      <div className={cn(
                        "w-14 h-14 rounded-xl flex items-center justify-center relative cursor-pointer",
                        item.color,
                        "shadow-lg hover:shadow-2xl transition-shadow"
                      )}>
                        <item.icon className="h-7 w-7 text-white" />
                        {item.badge && (
                          <Badge
                            variant="secondary"
                            size="sm"
                            className="absolute -top-2 -right-2 min-w-[1.5rem] h-5 px-1"
                          >
                            {item.badge}
                          </Badge>
                        )}
                        {item.pulse && (
                          <div className="absolute -top-1 -right-1">
                            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                          </div>
                        )}
                      </div>
                    </Link>
                  ) : (
                    <button
                      onClick={() => console.log(`${item.label} clicked`)}
                      className={cn(
                        "w-14 h-14 rounded-xl flex items-center justify-center relative",
                        item.color,
                        "shadow-lg hover:shadow-2xl transition-shadow"
                      )}
                    >
                      <item.icon className="h-7 w-7 text-white" />
                      {item.badge && (
                        <Badge
                          variant="secondary"
                          size="sm"
                          className="absolute -top-2 -right-2 min-w-[1.5rem] h-5 px-1"
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </button>
                  )}
                </motion.div>

                {/* Label */}
                <AnimatePresence>
                  {hoveredDockItem === item.id && (
                    <motion.div
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 5 }}
                      className="absolute top-full mt-2 left-1/2 -translate-x-1/2 whitespace-nowrap"
                    >
                      <span className="text-xs font-medium px-2 py-1 bg-black/80 rounded">{item.label}</span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>

          {/* Expanded Dock Content */}
          <AnimatePresence>
            {expandedDock && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex-1 px-8 pb-4 overflow-x-auto"
              >
                <div className="grid grid-cols-5 gap-4 min-w-[1000px]">
                  {dockItems.slice(0, 5).map((item) => (
                    <div
                      key={item.id}
                      className="bg-white/5 backdrop-blur rounded-xl p-4 hover:bg-white/10 transition-colors cursor-pointer"
                    >
                      <div className="flex items-center gap-3 mb-3">
                        <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center", item.color)}>
                          <item.icon className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="font-medium">{item.label}</h3>
                        {item.badge && (
                          <Badge variant="secondary" size="sm">{item.badge}</Badge>
                        )}
                      </div>
                      {item.preview}
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Floating Command Center */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}