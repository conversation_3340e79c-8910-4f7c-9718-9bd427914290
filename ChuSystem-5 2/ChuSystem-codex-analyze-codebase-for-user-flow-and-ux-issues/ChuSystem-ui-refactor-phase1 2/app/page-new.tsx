"use client"

import {
  PlusIcon,
  ChartBarIcon,
  CalendarIcon,
  UserGroupIcon,
  HeartIcon,
  ArrowRightIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
} from "@heroicons/react/16/solid"
import { ModuleLayout } from "@/components/ui/module-layout"
import { Card } from "@/components/ui/card"
import { GlassCard } from "@/components/ui/glass-card"
import { GlassModal } from "@/components/ui/glass-modal"
import { GlassSheet } from "@/components/ui/glass-sheet"
import DataCard from "@/components/ui/data-card"
import MinistryCard from "@/components/ministry-card"
import TaskCard from "@/components/task-card"
import UpcomingEvents from "@/components/upcoming-events"
import AttendanceSnapshot from "@/components/attendance-snapshot"
import { Button } from "@/components/ui/button"
import QuickActions from "@/components/ui/quick-actions"
import Link from "next/link"
import { useState } from "react"
import GlassTooltip from "@/components/ui/glass-tooltip"

export default function Home() {
  const [isServiceModalOpen, setIsServiceModalOpen] = useState(false)
  const [isPeopleSheetOpen, setIsPeopleSheetOpen] = useState(false)
  const [isEventsModalOpen, setIsEventsModalOpen] = useState(false)
  const [isGivingSheetOpen, setIsGivingSheetOpen] = useState(false)

  const quickActions = [
    {
      id: "new-service",
      icon: <PlusIcon className="h-5 w-5" />,
      label: "New Service",
      onClick: () => setIsServiceModalOpen(true),
      color: "bg-blue-500",
    },
    {
      id: "add-person",
      icon: <UserGroupIcon className="h-5 w-5" />,
      label: "Add Person",
      onClick: () => setIsPeopleSheetOpen(true),
      color: "bg-green-500",
    },
    {
      id: "schedule-event",
      icon: <CalendarIcon className="h-5 w-5" />,
      label: "Schedule Event", 
      onClick: () => setIsEventsModalOpen(true),
      color: "bg-purple-500",
    },
    {
      id: "record-giving",
      icon: <CurrencyDollarIcon className="h-5 w-5" />,
      label: "Record Giving",
      onClick: () => setIsGivingSheetOpen(true),
      color: "bg-amber-500",
    },
  ]

  return (
    <>
      <ModuleLayout
        title="Church Dashboard"
        subtitle="Welcome back! Here's an overview of your church's activities."
        actions={
          <div className="flex gap-3">
            <GlassTooltip content="Filter by date range" side="bottom">
              <Button variant="outline" size="md">
                Last 30 Days
              </Button>
            </GlassTooltip>
            <GlassTooltip content="Create new content" side="bottom">
              <Button variant="glass" size="md" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Create
              </Button>
            </GlassTooltip>
          </div>
        }
        quickActions={<QuickActions actions={quickActions} />}
      >
        <div className="space-y-8 p-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <GlassTooltip content="Total active members" side="top">
              <DataCard
                title="Active Members"
                value="1,247"
                change="+12%"
                trend="up"
                icon={<UserGroupIcon className="h-5 w-5" />}
                className="hover-lift"
              />
            </GlassTooltip>
            
            <GlassTooltip content="This week's service attendance" side="top">
              <DataCard
                title="Weekly Attendance"
                value="892"
                change="+5%"
                trend="up"
                icon={<ClockIcon className="h-5 w-5" />}
                className="hover-lift"
              />
            </GlassTooltip>
            
            <GlassTooltip content="Total giving this month" side="top">
              <DataCard
                title="Monthly Giving"
                value="$23,840"
                change="+18%"
                trend="up"
                icon={<CurrencyDollarIcon className="h-5 w-5" />}
                className="hover-lift"
              />
            </GlassTooltip>
            
            <GlassTooltip content="Upcoming events this month" side="top">
              <DataCard
                title="Upcoming Events"
                value="12"
                change="+3"
                trend="up"
                icon={<CalendarIcon className="h-5 w-5" />}
                className="hover-lift"
              />
            </GlassTooltip>
          </div>

          {/* Action Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <GlassTooltip content="Create and plan worship services" side="top">
              <GlassCard
                variant="glow"
                className="hover-lift hover:bg-gradient-to-br hover:from-blue-900/20 hover:to-blue-800/5 group animate-fade-in-up cursor-pointer p-6"
                onClick={() => setIsServiceModalOpen(true)}
              >
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-blue-500/10 border border-blue-500/20 group-hover:bg-blue-500/20">
                    <CalendarIcon className="h-6 w-6 text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1 group-hover:text-blue-400">Schedule Service</h3>
                    <p className="text-sm text-zinc-400 mb-3">Create a new service plan</p>
                    <div className="flex items-center text-sm text-blue-400 font-medium">
                      <span>Get Started</span>
                      <ArrowRightIcon className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </GlassCard>
            </GlassTooltip>

            <GlassTooltip content="Manage church members and visitors" side="top">
              <GlassCard
                variant="glow"
                className="hover-lift hover:bg-gradient-to-br hover:from-green-900/20 hover:to-green-800/5 group animate-fade-in-up animation-delay-100 cursor-pointer p-6"
                onClick={() => setIsPeopleSheetOpen(true)}
              >
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-green-500/10 border border-green-500/20 group-hover:bg-green-500/20">
                    <UserGroupIcon className="h-6 w-6 text-green-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1 group-hover:text-green-400">Manage People</h3>
                    <p className="text-sm text-zinc-400 mb-3">Church directory and member tools</p>
                    <div className="flex items-center text-sm text-green-400 font-medium">
                      <span>Open Directory</span>
                      <ArrowRightIcon className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </GlassCard>
            </GlassTooltip>

            <GlassTooltip content="Track contributions and financial insights" side="top">
              <GlassCard
                variant="glow"
                className="hover-lift hover:bg-gradient-to-br hover:from-amber-900/20 hover:to-amber-800/5 group animate-fade-in-up animation-delay-200 cursor-pointer p-6"
                onClick={() => setIsGivingSheetOpen(true)}
              >
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-amber-500/10 border border-amber-500/20 group-hover:bg-amber-500/20">
                    <HeartIcon className="h-6 w-6 text-amber-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1 group-hover:text-amber-400">Giving Overview</h3>
                    <p className="text-sm text-zinc-400 mb-3">Track contributions and insights</p>
                    <div className="flex items-center text-sm text-amber-400 font-medium">
                      <span>View Reports</span>
                      <ArrowRightIcon className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </GlassCard>
            </GlassTooltip>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <AttendanceSnapshot />
              <UpcomingEvents />
            </div>
            <div className="space-y-6">
              <MinistryCard />
              <TaskCard />
            </div>
          </div>
        </div>
      </ModuleLayout>

      {/* Glass Modals and Sheets */}
      <GlassModal
        isOpen={isServiceModalOpen}
        onClose={() => setIsServiceModalOpen(false)}
        title="Create New Service"
        description="Plan and schedule a new worship service with team coordination."
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-zinc-300">Service planning features:</p>
          <ul className="list-disc list-inside text-sm text-zinc-400 space-y-1">
            <li>Schedule worship elements and timing</li>
            <li>Assign team members and volunteers</li>
            <li>Coordinate music and media resources</li>
            <li>Plan special events and announcements</li>
          </ul>
          <div className="flex gap-3 pt-4">
            <Button variant="primary" size="md" leftIcon={<PlusIcon className="h-4 w-4" />}>
              Start Planning
            </Button>
            <Link href="/services/new">
              <Button variant="glass" size="md">
                Full Service Builder
              </Button>
            </Link>
          </div>
        </div>
      </GlassModal>

      <GlassSheet
        isOpen={isPeopleSheetOpen}
        onClose={() => setIsPeopleSheetOpen(false)}
        title="People Management"
        description="Access your church directory and member management tools."
        side="right"
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-zinc-300">Available actions:</p>
          <div className="grid grid-cols-2 gap-3">
            <Link href="/people">
              <Button variant="glass" size="sm" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
                View Directory
              </Button>
            </Link>
            <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
              Add Member
            </Button>
          </div>
        </div>
      </GlassSheet>

      <GlassModal
        isOpen={isEventsModalOpen}
        onClose={() => setIsEventsModalOpen(false)}
        title="Events & Ministries"
        description="Manage church events, ministries, and community activities."
        size="md"
      >
        <div className="space-y-4">
          <p className="text-zinc-300">Quick actions:</p>
          <div className="grid grid-cols-2 gap-3">
            <Button variant="primary" size="sm" leftIcon={<CalendarIcon className="h-4 w-4" />}>
              Create Event
            </Button>
            <Link href="/ministries">
              <Button variant="glass" size="sm" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
                View Ministries
              </Button>
            </Link>
          </div>
        </div>
      </GlassModal>

      <GlassSheet
        isOpen={isGivingSheetOpen}
        onClose={() => setIsGivingSheetOpen(false)}
        title="Giving Management"
        description="Record contributions and view financial insights."
        side="right"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-zinc-300">Quick actions:</p>
          <div className="space-y-3">
            <Button variant="primary" size="md" leftIcon={<CurrencyDollarIcon className="h-4 w-4" />}>
              Record Contribution
            </Button>
            <Link href="/giving">
              <Button variant="glass" size="md" leftIcon={<ChartBarIcon className="h-4 w-4" />}>
                View Reports
              </Button>
            </Link>
          </div>
        </div>
      </GlassSheet>
    </>
  )
}
