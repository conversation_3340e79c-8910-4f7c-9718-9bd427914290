import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
// SidebarModern is now part of ModuleLayout or specific page layouts, not directly in RootLayout
// import SidebarModern from "@/components/sidebar-modern" 
import { ActionsProvider } from "@/lib/actions-context" // Import ActionsProvider
import { CommandPaletteProvider } from "@/components/command-palette-provider"
import { cn } from "@/lib/utils"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Orbital Church Management",
  description: "Modern church management system",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.className, "bg-black text-white")} suppressHydrationWarning>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false}>
          <ActionsProvider> {/* Wrap content with ActionsProvider */}
            <CommandPaletteProvider>
              {/* 
                The original RootLayout had a flex container with SidebarModern and main.
                This structure is now generally handled by ModuleLayout on a per-page basis
                or by specific page layouts if ModuleLayout is not used.
                RootLayout should provide providers and global structure if any, but not specific sidebars.
              */}
              {children} 
            </CommandPaletteProvider>
          </ActionsProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
