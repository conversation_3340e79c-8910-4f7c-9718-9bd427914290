"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { formatNumber, formatCompactNumber } from "@/lib/format"
import ModuleLayout from "@/components/ui/module-layout"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { GlassCard } from "@/components/ui/glass-card"
import DataCard from "@/components/ui/data-card"
import ClientOnly from "@/components/ui/client-only"
import { AnimatePresence, motion } from "framer-motion"
import dynamic from "next/dynamic"

// Dynamically import components that might cause hydration issues
const SparkBuilder = dynamic(() => import("@/components/publish/spark-builder"), {
  ssr: false,
  loading: () => <div className="p-6 text-center">Loading builder...</div>
})
const PublishPreview = dynamic(() => import("@/components/publish/publish-preview"), {
  ssr: false,
  loading: () => <div className="p-6 text-center">Loading preview...</div>
})
const AnalyticsDashboard = dynamic(() => import("@/components/publish/analytics-dashboard"), {
  ssr: false,
  loading: () => <div className="p-6 text-center">Loading analytics...</div>
})
const ThemeSelector = dynamic(() => import("@/components/publish/theme-selector"), {
  ssr: false,
  loading: () => <div className="p-6 text-center">Loading themes...</div>
})
const EmbedGenerator = dynamic(() => import("@/components/publish/embed-generator"), {
  ssr: false
})
const AIContentAssistant = dynamic(() => import("@/components/publish/ai-content-assistant"), {
  ssr: false
})
const SocialMediaIntegration = dynamic(() => import("@/components/publish/social-media-integration"), {
  ssr: false
})
const DomainManager = dynamic(() => import("@/components/publish/domain-manager"), {
  ssr: false
})
import { 
  CloudArrowUpIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  GlobeAltIcon,
  PaintBrushIcon,
  SparklesIcon,
  ShareIcon,
  QrCodeIcon,
  LinkIcon,
  ChartBarIcon,
  EyeIcon,
  CodeBracketIcon,
  RocketLaunchIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  PlusIcon,
  CheckCircleIcon,
  XMarkIcon,
  ArrowPathIcon,
  BuildingStorefrontIcon,
  CubeTransparentIcon,
  SwatchIcon,
  MegaphoneIcon,
  ShieldCheckIcon,
  CommandLineIcon,
  PhotoIcon,
  VideoCameraIcon,
  BanknotesIcon
} from "@heroicons/react/24/outline"

type PublishMode = "dashboard" | "builder" | "themes" | "preview" | "analytics" | "settings"
type AppType = "landing" | "events" | "giving" | "connect" | "stream" | "groups" | "custom"

interface PublishProject {
  id: string
  name: string
  type: AppType
  status: "draft" | "published" | "scheduled"
  lastUpdated: string
  views: number
  conversions: number
  url?: string
  theme: string
  modules: string[]
  customDomain?: string
  analytics?: {
    visitors: number
    pageViews: number
    avgDuration: string
    bounceRate: number
  }
}

export default function PublishPage() {
  const [publishMode, setPublishMode] = useState<PublishMode>("dashboard")
  const [selectedProject, setSelectedProject] = useState<PublishProject | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isPublishing, setIsPublishing] = useState(false)

  // Sample projects with enhanced data
  const projects: PublishProject[] = [
    { 
      id: "1", 
      name: "Main Church Website", 
      type: "landing", 
      status: "published", 
      lastUpdated: "2 hours ago", 
      views: 45234, 
      conversions: 3421,
      url: "gracechurch.live", 
      theme: "modern-glass",
      modules: ["services", "events", "giving", "groups"],
      customDomain: "gracechurch.com",
      analytics: {
        visitors: 12453,
        pageViews: 45234,
        avgDuration: "3:24",
        bounceRate: 32
      }
    },
    { 
      id: "2", 
      name: "Easter 2025 Campaign", 
      type: "events", 
      status: "scheduled", 
      lastUpdated: "1 day ago", 
      views: 0,
      conversions: 0,
      theme: "celebration",
      modules: ["events", "giving", "connect"]
    },
    { 
      id: "3", 
      name: "Online Giving Portal", 
      type: "giving", 
      status: "published", 
      lastUpdated: "3 days ago", 
      views: 8923,
      conversions: 892,
      url: "give.gracechurch.live", 
      theme: "trust",
      modules: ["giving", "testimonials"],
      customDomain: "give.gracechurch.com"
    }
  ]

  const handlePublish = async () => {
    setIsPublishing(true)
    // Simulate publishing
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsPublishing(false)
  }

  const renderContent = () => {
    switch (publishMode) {
      case "dashboard":
        return (
          <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-4 gap-4">
              <DataCard
                title="Total Apps"
                value={projects.length}
                icon={<RocketLaunchIcon className="h-5 w-5" />}
                trend={{ value: 2, isPositive: true }}
                footer="Active projects"
                variant="elevated"
              />
              <DataCard
                title="Total Views"
                value="54.2K"
                icon={<EyeIcon className="h-5 w-5" />}
                trend={{ value: 18, isPositive: true }}
                footer="Last 30 days"
                variant="elevated"
              />
              <DataCard
                title="Conversions"
                value="5,205"
                icon={<ArrowTrendingUpIcon className="h-5 w-5" />}
                trend={{ value: 23, isPositive: true }}
                footer="Goals completed"
                variant="elevated"
              />
              <DataCard
                title="Active Users"
                value="1,847"
                icon={<UserGroupIcon className="h-5 w-5" />}
                trend={{ value: 12, isPositive: true }}
                footer="Unique visitors"
                variant="elevated"
              />
            </div>

            {/* Projects Grid */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium">Your Publishing Projects</h2>
                <Button 
                  variant="primary" 
                  size="sm" 
                  leftIcon={<PlusIcon className="h-4 w-4" />}
                  onClick={() => setShowCreateModal(true)}
                >
                  Create New App
                </Button>
              </div>
              
              <div className="grid grid-cols-3 gap-6">
                {projects.map((project) => (
                  <motion.div
                    key={project.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <GlassCard 
                      variant="elevated" 
                      className="cursor-pointer hover:shadow-glow-lg transition-all"
                      onClick={() => {
                        setSelectedProject(project)
                        setPublishMode("builder")
                      }}
                    >
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className={cn(
                            "w-12 h-12 rounded-xl flex items-center justify-center",
                            project.type === "landing" ? "bg-gradient-to-br from-blue-500 to-blue-600" :
                            project.type === "events" ? "bg-gradient-to-br from-purple-500 to-purple-600" :
                            project.type === "giving" ? "bg-gradient-to-br from-green-500 to-green-600" :
                            "bg-gradient-to-br from-amber-500 to-amber-600"
                          )}>
                            {project.type === "landing" ? <GlobeAltIcon className="h-6 w-6 text-white" /> :
                             project.type === "events" ? <PhotoIcon className="h-6 w-6 text-white" /> :
                             project.type === "giving" ? <BanknotesIcon className="h-6 w-6 text-white" /> :
                             <UserGroupIcon className="h-6 w-6 text-white" />}
                          </div>
                          <Badge 
                            color={
                              project.status === "published" ? "success" :
                              project.status === "scheduled" ? "warning" :
                              "default"
                            }
                          >
                            {project.status}
                          </Badge>
                        </div>
                        
                        <h3 className="font-semibold text-lg mb-1">{project.name}</h3>
                        <p className="text-sm text-zinc-400 mb-4">{project.lastUpdated}</p>
                        
                        {project.url && (
                          <div className="flex items-center gap-2 mb-3">
                            <LinkIcon className="h-4 w-4 text-blue-400" />
                            <span className="text-sm text-blue-400">{project.url}</span>
                          </div>
                        )}
                        
                        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-white/10">
                          <div>
                            <ClientOnly fallback={<p className="text-2xl font-bold">--</p>}>
                              <p className="text-2xl font-bold">{formatNumber(project.views)}</p>
                            </ClientOnly>
                            <p className="text-xs text-zinc-400">Views</p>
                          </div>
                          <div>
                            <ClientOnly fallback={<p className="text-2xl font-bold">--</p>}>
                              <p className="text-2xl font-bold">{formatNumber(project.conversions)}</p>
                            </ClientOnly>
                            <p className="text-xs text-zinc-400">Conversions</p>
                          </div>
                        </div>
                      </div>
                    </GlassCard>
                  </motion.div>
                ))}

                {/* Create New Card */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <GlassCard 
                    variant="subtle" 
                    className="cursor-pointer hover:shadow-glow-lg transition-all h-full"
                    onClick={() => setShowCreateModal(true)}
                  >
                    <div className="p-6 h-full flex flex-col items-center justify-center text-center">
                      <div className="w-16 h-16 rounded-full bg-white/10 flex items-center justify-center mb-4">
                        <PlusIcon className="h-8 w-8" />
                      </div>
                      <h3 className="font-medium mb-2">Create New App</h3>
                      <p className="text-sm text-zinc-400">Build a custom app for your church</p>
                    </div>
                  </GlassCard>
                </motion.div>
              </div>
            </div>
          </div>
        )

      case "builder":
        return selectedProject ? (
          <SparkBuilder 
            project={selectedProject}
            onUpdate={(updates) => setSelectedProject({ ...selectedProject, ...updates })}
            onPublish={handlePublish}
          />
        ) : null

      case "themes":
        return (
          <ThemeSelector 
            selectedTheme={selectedProject?.theme}
            onSelectTheme={(theme) => selectedProject && setSelectedProject({ ...selectedProject, theme })}
          />
        )

      case "preview":
        return selectedProject ? (
          <PublishPreview project={selectedProject} />
        ) : null

      case "analytics":
        return selectedProject ? (
          <AnalyticsDashboard project={selectedProject} />
        ) : null

      case "settings":
        return selectedProject ? (
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-6">
              <DomainManager project={selectedProject} />
              <EmbedGenerator project={selectedProject} />
            </div>
            <div className="space-y-6">
              <SocialMediaIntegration project={selectedProject} />
              <AIContentAssistant project={selectedProject} />
            </div>
          </div>
        ) : null

      default:
        return null
    }
  }

  const sidebarActions = selectedProject ? [
    {
      label: "Dashboard",
      icon: BuildingStorefrontIcon,
      onClick: () => setPublishMode("dashboard"),
      active: publishMode === "dashboard"
    },
    {
      label: "App Builder",
      icon: CubeTransparentIcon,
      onClick: () => setPublishMode("builder"),
      active: publishMode === "builder",
      badge: "AI"
    },
    {
      label: "Themes",
      icon: SwatchIcon,
      onClick: () => setPublishMode("themes"),
      active: publishMode === "themes"
    },
    {
      label: "Preview",
      icon: DevicePhoneMobileIcon,
      onClick: () => setPublishMode("preview"),
      active: publishMode === "preview"
    },
    {
      label: "Analytics",
      icon: ChartBarIcon,
      onClick: () => setPublishMode("analytics"),
      active: publishMode === "analytics"
    },
    {
      label: "Settings",
      icon: ShieldCheckIcon,
      onClick: () => setPublishMode("settings"),
      active: publishMode === "settings"
    }
  ] : []

  const topBarActions = (
    <>
      {selectedProject && publishMode !== "dashboard" && (
        <>
          <Button 
            variant="glass" 
            size="sm" 
            leftIcon={<ArrowPathIcon className="h-4 w-4" />}
            onClick={() => console.log("Sync data")}
          >
            Sync Data
          </Button>

          <Button 
            variant="glass" 
            size="sm" 
            leftIcon={<ShareIcon className="h-4 w-4" />}
            onClick={() => console.log("Share")}
          >
            Share
          </Button>

          <Button 
            variant="primary" 
            size="sm" 
            leftIcon={<CloudArrowUpIcon className="h-4 w-4" />}
            onClick={handlePublish}
            disabled={isPublishing}
          >
            {isPublishing ? "Publishing..." : "Publish Changes"}
          </Button>
        </>
      )}
    </>
  )

  return (
    <ClientOnly fallback={
      <ModuleLayout
        title="Publish"
        subtitle="Loading..."
        searchPlaceholder="Search projects..."
        sidebarActions={[]}
        topBarActions={<></>}
      >
        <div className="p-6 text-center">Loading publish page...</div>
      </ModuleLayout>
    }>
      <ModuleLayout
        title={selectedProject ? selectedProject.name : "Publishing Center"}
        subtitle={selectedProject ? `${selectedProject.type} • ${selectedProject.status}` : "Create and manage church apps"}
        searchPlaceholder="Search projects..."
        sidebarActions={sidebarActions}
        topBarActions={topBarActions}
      >
        {renderContent()}

      {/* Create Modal */}
      <AnimatePresence>
        {showCreateModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-8"
            onClick={() => setShowCreateModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-[var(--bg-card)] border border-[var(--border-subtle)] rounded-2xl p-8 max-w-2xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-semibold">Create New App</h2>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                  aria-label="Close modal"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {[
                  { type: "landing", label: "Church Website", icon: GlobeAltIcon, color: "from-blue-500 to-blue-600" },
                  { type: "events", label: "Event Landing", icon: PhotoIcon, color: "from-purple-500 to-purple-600" },
                  { type: "giving", label: "Giving Portal", icon: BanknotesIcon, color: "from-green-500 to-green-600" },
                  { type: "connect", label: "Connect Card", icon: UserGroupIcon, color: "from-amber-500 to-amber-600" },
                  { type: "stream", label: "Live Stream", icon: VideoCameraIcon, color: "from-red-500 to-red-600" },
                  { type: "custom", label: "Custom App", icon: SparklesIcon, color: "from-pink-500 to-pink-600" }
                ].map((template) => (
                  <motion.button
                    key={template.type}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => {
                      // Use a counter-based ID to avoid hydration mismatches
                      const projectCount = projects.length + 1
                      const newProject: PublishProject = {
                        id: `${template.type}-${projectCount}`,
                        name: `New ${template.label}`,
                        type: template.type as AppType,
                        status: "draft",
                        lastUpdated: "Just now",
                        views: 0,
                        conversions: 0,
                        theme: "modern-glass",
                        modules: []
                      }
                      setSelectedProject(newProject)
                      setPublishMode("builder")
                      setShowCreateModal(false)
                    }}
                    className="p-6 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left"
                  >
                    <div className={cn(
                      "w-12 h-12 rounded-xl flex items-center justify-center mb-4",
                      `bg-gradient-to-br ${template.color}`
                    )}>
                      <template.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="font-medium mb-1">{template.label}</h3>
                    <p className="text-sm text-zinc-400">Start with a {template.label.toLowerCase()} template</p>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </ModuleLayout>
    </ClientOnly>
  )
}