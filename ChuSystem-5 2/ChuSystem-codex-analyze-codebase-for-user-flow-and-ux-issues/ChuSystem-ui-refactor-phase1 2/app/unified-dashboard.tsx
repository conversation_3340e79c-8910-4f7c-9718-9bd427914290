"use client"

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommandCenter from "@/components/ui/floating-command-center"
import FloatingPanel from "@/components/ui/floating-panel"
import { 
  ChartBarIcon,
  UserGroupIcon,
  CalendarDaysIcon,
  BanknotesIcon,
  TicketIcon,
  UserIcon,
  MusicalNoteIcon,
  BellIcon,
  SparklesIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ClockIcon,
  FireIcon,
  ChartPieIcon,
  Squares2X2Icon,
  EyeIcon,
  PlayIcon,
  PauseIcon,
  VideoCameraIcon,
  MapPinIcon,
  HeartIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  PlusIcon,
  HomeIcon,
  CogIcon,
  PhotoIcon,
  MicrophoneIcon,
  WifiIcon,
  ComputerDesktopIcon,
  UserPlusIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  XMarkIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  CubeTransparentIcon,
  ViewColumnsIcon,
  RectangleGroupIcon,
  ArrowPathIcon,
  AdjustmentsHorizontalIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import Link from "next/link"

type DashboardMode = "command" | "widgets" | "focus" | "analytics"
type WidgetSize = "small" | "medium" | "large" | "xl"

interface Widget {
  id: string
  type: string
  size: WidgetSize
  title: string
  icon: React.ComponentType<any>
  color: string
  content: React.ReactNode
  refreshInterval?: number
  interactive?: boolean
  expandable?: boolean
}

interface DockItem {
  id: string
  label: string
  icon: React.ComponentType<any>
  color: string
  href?: string
  preview?: React.ReactNode
  badge?: string | number
  pulse?: boolean
  action?: () => void
}

interface QuickAction {
  id: string
  label: string
  icon: React.ComponentType<any>
  shortcut: string
  action: () => void
}

export default function UnifiedDashboard() {
  const [dashboardMode, setDashboardMode] = useState<DashboardMode>("command")
  const [liveMode, setLiveMode] = useState(true)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [selectedWidgets, setSelectedWidgets] = useState<Set<string>>(new Set())
  const [floatingPanels, setFloatingPanels] = useState<any[]>([])
  const [dockExpanded, setDockExpanded] = useState(false)
  const [customizingLayout, setCustomizingLayout] = useState(false)
  const [focusedProduct, setFocusedProduct] = useState<string | null>(null)

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  // Auto-refresh data in live mode
  useEffect(() => {
    if (!liveMode) return
    const refreshInterval = setInterval(() => {
      // Refresh widget data
      console.log("Refreshing live data...")
    }, 30000) // Every 30 seconds
    return () => clearInterval(refreshInterval)
  }, [liveMode])

  // Dashboard modes
  const dashboardModes = [
    { id: "command", label: "Command Center", icon: ComputerDesktopIcon },
    { id: "widgets", label: "Widgets", icon: RectangleGroupIcon },
    { id: "focus", label: "Focus Mode", icon: ViewColumnsIcon },
    { id: "analytics", label: "Analytics", icon: ChartBarIcon }
  ]

  // Dock items - your key products
  const dockItems: DockItem[] = [
    {
      id: "dashboard",
      label: "Overview",
      icon: HomeIcon,
      color: "bg-gradient-to-br from-blue-500 to-blue-600",
      action: () => setFocusedProduct(null),
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Quick Stats</h3>
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-white/5 rounded-lg p-3">
              <p className="text-2xl font-bold">342</p>
              <p className="text-xs text-zinc-400">Attendance Today</p>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <p className="text-2xl font-bold">$12.8K</p>
              <p className="text-xs text-zinc-400">Weekly Giving</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "services",
      label: "Services",
      icon: MusicalNoteIcon,
      color: "bg-gradient-to-br from-purple-500 to-purple-600",
      href: "/services",
      badge: liveMode ? "LIVE" : null,
      pulse: liveMode,
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Current Service</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Sunday Morning</span>
              <Badge variant="secondary" className="bg-green-500/20 text-green-400">Live</Badge>
            </div>
            <div className="h-2 bg-white/10 rounded-full overflow-hidden">
              <div className="h-full w-3/4 bg-purple-500" />
            </div>
            <p className="text-xs text-zinc-400">45 min remaining</p>
          </div>
        </div>
      )
    },
    {
      id: "people",
      label: "People",
      icon: UserGroupIcon,
      color: "bg-gradient-to-br from-green-500 to-green-600",
      href: "/people",
      badge: 1234,
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Recent Activity</h3>
          <div className="space-y-2">
            {["Johnson Family", "Sarah Chen", "Mike Davis"].map((name, i) => (
              <div key={i} className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-white/10" />
                <span className="text-sm">{name} checked in</span>
              </div>
            ))}
          </div>
        </div>
      )
    },
    {
      id: "giving",
      label: "Giving",
      icon: BanknotesIcon,
      color: "bg-gradient-to-br from-amber-500 to-amber-600",
      href: "/giving",
      badge: "$45.7K",
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Today's Giving</h3>
          <div className="space-y-2">
            <p className="text-3xl font-bold">$12,840</p>
            <div className="flex items-center gap-2">
              <ArrowTrendingUpIcon className="h-4 w-4 text-green-400" />
              <span className="text-sm text-green-400">+23% vs last week</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "events",
      label: "Events",
      icon: CalendarDaysIcon,
      color: "bg-gradient-to-br from-pink-500 to-pink-600",
      href: "/events",
      badge: 12,
      preview: (
        <div className="p-4">
          <h3 className="font-medium mb-3">Upcoming Today</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span>Youth Group</span>
              <span className="text-zinc-400">6:30 PM</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Prayer Meeting</span>
              <span className="text-zinc-400">7:00 PM</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "checkin",
      label: "Check-in",
      icon: TicketIcon,
      color: "bg-gradient-to-br from-cyan-500 to-cyan-600",
      href: "/check-in",
      badge: 234,
      action: () => setFocusedProduct("checkin")
    },
    {
      id: "groups",
      label: "Groups",
      icon: UserGroupIcon,
      color: "bg-gradient-to-br from-indigo-500 to-indigo-600",
      href: "/groups",
      badge: 67,
      action: () => setFocusedProduct("groups")
    },
    {
      id: "ministries",
      label: "Ministries",
      icon: BriefcaseIcon,
      color: "bg-gradient-to-br from-rose-500 to-rose-600",
      href: "/ministries",
      badge: 18,
      action: () => setFocusedProduct("ministries")
    },
    {
      id: "media",
      label: "Media",
      icon: PhotoIcon,
      color: "bg-gradient-to-br from-red-500 to-red-600",
      action: () => setFocusedProduct("media")
    },
    {
      id: "settings",
      label: "Settings",
      icon: CogIcon,
      color: "bg-gradient-to-br from-zinc-600 to-zinc-700",
      action: () => console.log("Settings clicked")
    }
  ]

  // Widgets for widget mode
  const widgets: Widget[] = [
    {
      id: "live-service",
      type: "service",
      size: "large",
      title: "Live Service Monitor",
      icon: MusicalNoteIcon,
      color: "bg-purple-500",
      content: <LiveServiceWidget />,
      refreshInterval: 10000,
      interactive: true,
      expandable: true
    },
    {
      id: "attendance",
      type: "stats",
      size: "medium",
      title: "Real-time Attendance",
      icon: UserGroupIcon,
      color: "bg-blue-500",
      content: <AttendanceWidget />,
      refreshInterval: 30000
    },
    {
      id: "giving",
      type: "financial",
      size: "medium",
      title: "Live Giving",
      icon: BanknotesIcon,
      color: "bg-green-500",
      content: <GivingWidget />,
      refreshInterval: 60000
    },
    {
      id: "activity-feed",
      type: "feed",
      size: "medium",
      title: "Activity Feed",
      icon: BellIcon,
      color: "bg-amber-500",
      content: <ActivityFeedWidget />,
      refreshInterval: 5000,
      interactive: true
    },
    {
      id: "upcoming-events",
      type: "calendar",
      size: "small",
      title: "Today's Events",
      icon: CalendarDaysIcon,
      color: "bg-pink-500",
      content: <UpcomingEventsWidget />
    },
    {
      id: "system-health",
      type: "system",
      size: "small",
      title: "System Health",
      icon: ChartBarIcon,
      color: "bg-cyan-500",
      content: <SystemHealthWidget />,
      refreshInterval: 15000
    }
  ]

  // Quick actions
  const quickActions: QuickAction[] = [
    { id: "checkin", label: "Quick Check-in", icon: TicketIcon, shortcut: "⌘K", action: () => console.log("Check-in") },
    { id: "give", label: "Record Gift", icon: BanknotesIcon, shortcut: "⌘G", action: () => console.log("Give") },
    { id: "event", label: "Create Event", icon: CalendarDaysIcon, shortcut: "⌘E", action: () => console.log("Event") },
    { id: "person", label: "Add Person", icon: UserPlusIcon, shortcut: "⌘P", action: () => console.log("Person") }
  ]

  const addFloatingPanel = (data: any) => {
    setFloatingPanels([...floatingPanels, { id: Date.now(), ...data }])
  }

  const removeFloatingPanel = (id: number) => {
    setFloatingPanels(floatingPanels.filter(p => p.id !== id))
  }

  // Render content based on mode
  const renderContent = () => {
    switch (dashboardMode) {
      case "command":
        return <CommandCenterMode 
          widgets={widgets}
          quickActions={quickActions}
          dockItems={dockItems}
          onWidgetClick={(widget) => addFloatingPanel({ type: "widget", data: widget })}
        />
      case "widgets":
        return <WidgetsMode 
          widgets={widgets}
          customizing={customizingLayout}
          selectedWidgets={selectedWidgets}
          onWidgetSelect={(id) => {
            const newSelection = new Set(selectedWidgets)
            if (newSelection.has(id)) {
              newSelection.delete(id)
            } else {
              newSelection.add(id)
            }
            setSelectedWidgets(newSelection)
          }}
        />
      case "focus":
        return <FocusMode 
          focusedProduct={focusedProduct}
          products={dockItems}
          onProductSelect={setFocusedProduct}
        />
      case "analytics":
        return <AnalyticsMode />
      default:
        return null
    }
  }

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarRevolutionary />

      {/* Main Dashboard Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Bar */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold">Church Command Center</h1>
            
            {/* Mode Switcher */}
            <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
              {dashboardModes.map((mode) => (
                <button
                  key={mode.id}
                  onClick={() => setDashboardMode(mode.id as DashboardMode)}
                  className={cn(
                    "flex items-center gap-2 px-3 py-1.5 rounded transition-all",
                    dashboardMode === mode.id
                      ? "bg-[var(--color-primary)] text-black"
                      : "hover:bg-white/10"
                  )}
                >
                  <mode.icon className="h-4 w-4" />
                  <span className="text-sm font-medium">{mode.label}</span>
                </button>
              ))}
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Customization Toggle */}
            {dashboardMode === "widgets" && (
              <Button
                variant={customizingLayout ? "primary" : "glass"}
                size="sm"
                onClick={() => setCustomizingLayout(!customizingLayout)}
                leftIcon={<AdjustmentsHorizontalIcon className="h-4 w-4" />}
              >
                {customizingLayout ? "Done" : "Customize"}
              </Button>
            )}

            {/* Live Time */}
            <div className="text-2xl font-mono">
              {currentTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit' })}
            </div>
            
            {/* Live Mode Toggle */}
            <button
              onClick={() => setLiveMode(!liveMode)}
              className={cn(
                "px-4 py-2 rounded-lg flex items-center gap-2 transition-all",
                liveMode ? "bg-green-500/20 text-green-400" : "bg-white/10"
              )}
            >
              {liveMode ? <PlayIcon className="h-4 w-4" /> : <PauseIcon className="h-4 w-4" />}
              <span className="text-sm font-medium">{liveMode ? "Live Mode" : "Paused"}</span>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 relative">
          {renderContent()}
        </div>

        {/* Bottom Dock */}
        <motion.div
          initial={{ height: 80 }}
          animate={{ height: dockExpanded ? 280 : 80 }}
          className="relative bg-[var(--bg-glass)] backdrop-blur-2xl border-t border-[var(--border-subtle)]"
        >
          {/* Dock Expand Handle */}
          <button
            onClick={() => setDockExpanded(!dockExpanded)}
            className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-6 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-full flex items-center justify-center hover:bg-white/10 transition-colors"
          >
            {dockExpanded ? <ChevronDownIcon className="h-4 w-4" /> : <ChevronUpIcon className="h-4 w-4" />}
          </button>

          {/* Dock Items */}
          <div className="h-20 px-8 flex items-center justify-center gap-4">
            {dockItems.map((item) => (
              <DockItemComponent
                key={item.id}
                item={item}
                onClick={() => {
                  if (item.href) {
                    window.location.href = item.href
                  } else if (item.action) {
                    item.action()
                  }
                }}
              />
            ))}
          </div>

          {/* Expanded Dock Content */}
          <AnimatePresence>
            {dockExpanded && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="px-8 pb-4 overflow-x-auto"
              >
                <div className="grid grid-cols-5 gap-4 min-w-[1000px]">
                  {dockItems.slice(0, 5).map((item) => (
                    <div
                      key={item.id}
                      className="bg-white/5 backdrop-blur rounded-xl p-4 hover:bg-white/10 transition-colors cursor-pointer"
                      onClick={() => {
                        if (item.href) {
                          window.location.href = item.href
                        } else if (item.action) {
                          item.action()
                        }
                      }}
                    >
                      <div className="flex items-center gap-3 mb-3">
                        <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center", item.color)}>
                          <item.icon className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="font-medium">{item.label}</h3>
                        {item.badge && (
                          <Badge variant="secondary" size="sm">{item.badge}</Badge>
                        )}
                      </div>
                      {item.preview}
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      {/* Floating Panels */}
      <AnimatePresence>
        {floatingPanels.map((panel) => (
          <FloatingPanel
            key={panel.id}
            id={panel.id}
            title={panel.data.title || "Details"}
            icon={panel.data.icon}
            iconColor={panel.data.color}
            position={{ x: window.innerWidth / 2 - 200, y: 200 }}
            onClose={() => removeFloatingPanel(panel.id)}
          >
            {panel.data.content}
          </FloatingPanel>
        ))}
      </AnimatePresence>

      {/* Floating Command Center */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}

// Mode Components
function CommandCenterMode({ widgets, quickActions, dockItems, onWidgetClick }: any) {
  return (
    <div className="h-full p-6 overflow-y-auto custom-scrollbar">
      {/* Quick Actions Grid */}
      <div className="mb-8">
        <h2 className="text-lg font-medium mb-4">Quick Actions</h2>
        <div className="grid grid-cols-4 gap-4">
          {quickActions.map((action: QuickAction) => (
            <motion.button
              key={action.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={action.action}
              className="p-6 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl hover:shadow-glow-lg transition-all group"
            >
              <action.icon className="h-8 w-8 mx-auto mb-3 group-hover:scale-110 transition-transform" />
              <p className="font-medium">{action.label}</p>
              <p className="text-xs text-zinc-400 mt-1">{action.shortcut}</p>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Widget Grid */}
      <div className="grid grid-cols-12 gap-4 auto-rows-[200px]">
        {widgets.map((widget: Widget) => {
          const colSpan = widget.size === "small" ? 3 : widget.size === "medium" ? 4 : widget.size === "large" ? 6 : 12
          const rowSpan = widget.size === "large" || widget.size === "xl" ? 2 : 1
          
          return (
            <motion.div
              key={widget.id}
              className={cn(
                "bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4 cursor-pointer hover:shadow-glow-lg transition-all",
                `col-span-${colSpan}`,
                rowSpan === 2 && "row-span-2"
              )}
              onClick={() => widget.interactive && onWidgetClick(widget)}
              whileHover={{ scale: 1.01 }}
            >
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium">{widget.title}</h3>
                {widget.refreshInterval && (
                  <ArrowPathIcon className="h-4 w-4 text-zinc-400 animate-spin-slow" />
                )}
              </div>
              <div className="h-[calc(100%-2rem)]">
                {widget.content}
              </div>
            </motion.div>
          )
        })}
      </div>
    </div>
  )
}

function WidgetsMode({ widgets, customizing, selectedWidgets, onWidgetSelect }: any) {
  return (
    <div className="h-full p-6 overflow-y-auto custom-scrollbar">
      {customizing && (
        <div className="mb-4 p-4 bg-amber-500/10 border border-amber-500/30 rounded-lg">
          <p className="text-sm">
            <span className="font-medium">Customization Mode:</span> Click widgets to select, then drag to rearrange or resize.
          </p>
        </div>
      )}

      <div className="grid grid-cols-12 gap-4 auto-rows-[200px]">
        {widgets.map((widget: Widget) => {
          const colSpan = widget.size === "small" ? 3 : widget.size === "medium" ? 4 : widget.size === "large" ? 6 : 12
          const rowSpan = widget.size === "large" || widget.size === "xl" ? 2 : 1
          const isSelected = selectedWidgets.has(widget.id)
          
          return (
            <motion.div
              key={widget.id}
              className={cn(
                "relative bg-[var(--bg-glass)] backdrop-blur-xl border-2 rounded-xl p-4 transition-all",
                `col-span-${colSpan}`,
                rowSpan === 2 && "row-span-2",
                customizing && "cursor-pointer",
                isSelected ? "border-[var(--color-primary)] shadow-glow" : "border-[var(--border-subtle)]"
              )}
              onClick={() => customizing && onWidgetSelect(widget.id)}
              whileHover={customizing ? { scale: 1.02 } : {}}
              drag={customizing && isSelected}
              dragMomentum={false}
            >
              {customizing && isSelected && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-[var(--color-primary)] rounded-full flex items-center justify-center">
                  <CheckCircleIcon className="h-4 w-4 text-black" />
                </div>
              )}
              
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium">{widget.title}</h3>
                <widget.icon className={cn("h-4 w-4", widget.color.replace('bg-', 'text-'))} />
              </div>
              <div className="h-[calc(100%-2rem)]">
                {widget.content}
              </div>
            </motion.div>
          )
        })}
      </div>
    </div>
  )
}

function FocusMode({ focusedProduct, products, onProductSelect }: any) {
  if (!focusedProduct) {
    return (
      <div className="h-full p-6">
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <CubeTransparentIcon className="h-16 w-16 mx-auto mb-4 text-zinc-400" />
            <h2 className="text-2xl font-bold mb-2">Focus Mode</h2>
            <p className="text-zinc-400 mb-8">Select a product to focus on</p>
            
            <div className="grid grid-cols-3 gap-4 max-w-3xl mx-auto">
              {products.slice(1, 7).map((product: DockItem) => (
                <button
                  key={product.id}
                  onClick={() => onProductSelect(product.id)}
                  className="p-6 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl hover:shadow-glow-lg transition-all"
                >
                  <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3", product.color)}>
                    <product.icon className="h-6 w-6 text-white" />
                  </div>
                  <p className="font-medium">{product.label}</p>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Focused product view - embed the actual product interface
  return (
    <div className="h-full">
      <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl">
        <div className="p-4 border-b border-[var(--border-subtle)] flex items-center justify-between">
          <h2 className="text-lg font-medium">Focus: {focusedProduct}</h2>
          <Button variant="glass" size="sm" onClick={() => onProductSelect(null)}>
            Exit Focus
          </Button>
        </div>
        <div className="h-[calc(100%-80px)]">
          {/* Here you would embed the actual product interface */}
          <iframe 
            src={`/${focusedProduct}`} 
            className="w-full h-full border-0"
            title={focusedProduct}
          />
        </div>
      </div>
    </div>
  )
}

function AnalyticsMode() {
  return (
    <div className="h-full p-6 overflow-y-auto custom-scrollbar">
      <div className="grid grid-cols-12 gap-4">
        {/* Church Health Score */}
        <div className="col-span-4 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
          <h3 className="text-lg font-medium mb-4">Church Health Score</h3>
          <div className="relative h-48">
            <svg className="w-full h-full" viewBox="0 0 200 200">
              <circle cx="100" cy="100" r="80" fill="none" stroke="var(--border-subtle)" strokeWidth="20" />
              <circle 
                cx="100" 
                cy="100" 
                r="80" 
                fill="none" 
                stroke="var(--color-primary)" 
                strokeWidth="20"
                strokeDasharray={`${0.87 * 2 * Math.PI * 80} ${2 * Math.PI * 80}`}
                strokeDashoffset={-0.25 * 2 * Math.PI * 80}
                transform="rotate(-90 100 100)"
              />
              <text x="100" y="100" textAnchor="middle" dominantBaseline="middle" className="fill-white">
                <tspan x="100" dy="-10" className="text-4xl font-bold">87%</tspan>
                <tspan x="100" dy="35" className="text-sm opacity-60">Healthy</tspan>
              </text>
            </svg>
          </div>
          <div className="grid grid-cols-2 gap-3 mt-4">
            <div className="text-center p-2 bg-white/5 rounded-lg">
              <p className="text-sm font-medium">Attendance</p>
              <p className="text-xs text-green-400">↑ 12%</p>
            </div>
            <div className="text-center p-2 bg-white/5 rounded-lg">
              <p className="text-sm font-medium">Engagement</p>
              <p className="text-xs text-green-400">↑ 8%</p>
            </div>
          </div>
        </div>

        {/* Trends */}
        <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
          <h3 className="text-lg font-medium mb-4">Key Metrics Trends</h3>
          <div className="h-64 relative">
            <div className="absolute inset-0 flex items-end justify-around">
              {Array.from({ length: 12 }, (_, i) => (
                <div key={i} className="flex-1 mx-1">
                  <div 
                    className="w-full bg-[var(--color-primary)] rounded-t-lg transition-all hover:opacity-80" 
                    style={{ height: `${Math.sin(i / 2) * 50 + 50}%` }} 
                  />
                  <p className="text-xs text-center mt-1">W{i + 1}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Widget Components
function LiveServiceWidget() {
  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 mx-auto mb-3 bg-purple-500/20 rounded-full flex items-center justify-center animate-pulse">
            <MusicalNoteIcon className="h-10 w-10 text-purple-400" />
          </div>
          <p className="text-lg font-medium">Worship in Progress</p>
          <p className="text-sm text-zinc-400">342 attendees</p>
        </div>
      </div>
      <div className="pt-3 border-t border-[var(--border-subtle)]">
        <div className="flex items-center justify-between text-xs">
          <span className="text-zinc-400">Progress</span>
          <span>65%</span>
        </div>
        <div className="h-2 bg-white/10 rounded-full overflow-hidden mt-1">
          <div className="h-full w-[65%] bg-purple-500 transition-all" />
        </div>
      </div>
    </div>
  )
}

function AttendanceWidget() {
  return (
    <div className="h-full flex flex-col justify-center">
      <div className="text-center">
        <p className="text-4xl font-bold mb-1">342</p>
        <p className="text-sm text-zinc-400 mb-3">Total Checked In</p>
        <div className="grid grid-cols-3 gap-2 text-center">
          <div>
            <p className="text-lg font-medium">156</p>
            <p className="text-xs text-zinc-400">Adults</p>
          </div>
          <div>
            <p className="text-lg font-medium">89</p>
            <p className="text-xs text-zinc-400">Kids</p>
          </div>
          <div>
            <p className="text-lg font-medium">23</p>
            <p className="text-xs text-zinc-400">New</p>
          </div>
        </div>
      </div>
    </div>
  )
}

function GivingWidget() {
  return (
    <div className="h-full flex flex-col justify-center">
      <div className="text-center">
        <p className="text-3xl font-bold text-green-400 mb-1">$12,840</p>
        <p className="text-sm text-zinc-400 mb-3">Today's Giving</p>
        <div className="flex items-center justify-center gap-2">
          <ArrowTrendingUpIcon className="h-4 w-4 text-green-400" />
          <span className="text-sm text-green-400">+23% vs last week</span>
        </div>
      </div>
    </div>
  )
}

function ActivityFeedWidget() {
  const activities = [
    { icon: TicketIcon, text: "Johnson Family checked in", time: "just now" },
    { icon: BanknotesIcon, text: "$500 donation received", time: "2 min ago" },
    { icon: UserPlusIcon, text: "New visitor registered", time: "5 min ago" }
  ]

  return (
    <div className="h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-2">
        {activities.map((activity, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: i * 0.1 }}
            className="flex items-center gap-2 p-2 bg-white/5 rounded-lg"
          >
            <activity.icon className="h-4 w-4 text-zinc-400" />
            <div className="flex-1">
              <p className="text-xs">{activity.text}</p>
              <p className="text-xs text-zinc-400">{activity.time}</p>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

function UpcomingEventsWidget() {
  return (
    <div className="h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-2">
        {["Youth Group - 6:30 PM", "Prayer Meeting - 7:00 PM", "Worship Practice - 8:00 PM"].map((event, i) => (
          <div key={i} className="p-2 bg-white/5 rounded-lg">
            <p className="text-xs">{event}</p>
          </div>
        ))}
      </div>
    </div>
  )
}

function SystemHealthWidget() {
  return (
    <div className="h-full flex flex-col justify-center">
      <div className="space-y-2">
        {[
          { system: "Check-in", status: "online" },
          { system: "Stream", status: "live" },
          { system: "Network", status: "stable" }
        ].map((item) => (
          <div key={item.system} className="flex items-center justify-between">
            <span className="text-xs">{item.system}</span>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span className="text-xs text-green-400">{item.status}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Dock Item Component
function DockItemComponent({ item, onClick }: { item: DockItem; onClick: () => void }) {
  const [hovered, setHovered] = useState(false)

  return (
    <div
      className="relative"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {/* Hover Preview */}
      <AnimatePresence>
        {hovered && item.preview && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            className="absolute bottom-full mb-4 left-1/2 -translate-x-1/2 w-64 bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)] rounded-xl shadow-glow-2xl"
          >
            {item.preview}
            <div className="absolute top-full left-1/2 -translate-x-1/2 -translate-y-px">
              <div className="w-3 h-3 bg-[var(--bg-glass)] border-r border-b border-[var(--border-subtle)] rotate-45" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Dock Item */}
      <motion.div
        whileHover={{ scale: 1.2, y: -10 }}
        whileTap={{ scale: 0.95 }}
        onClick={onClick}
        className={cn(
          "relative w-14 h-14 rounded-xl flex items-center justify-center cursor-pointer",
          item.color,
          "shadow-lg hover:shadow-2xl transition-shadow"
        )}
      >
        <item.icon className="h-7 w-7 text-white" />
        {item.badge && (
          <Badge
            variant="secondary"
            size="sm"
            className="absolute -top-2 -right-2 min-w-[1.5rem] h-5 px-1"
          >
            {item.badge}
          </Badge>
        )}
        {item.pulse && (
          <div className="absolute -top-1 -right-1">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
          </div>
        )}
      </motion.div>

      {/* Label */}
      <AnimatePresence>
        {hovered && (
          <motion.div
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 5 }}
            className="absolute top-full mt-2 left-1/2 -translate-x-1/2 whitespace-nowrap"
          >
            <span className="text-xs font-medium px-2 py-1 bg-black/80 rounded">{item.label}</span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}