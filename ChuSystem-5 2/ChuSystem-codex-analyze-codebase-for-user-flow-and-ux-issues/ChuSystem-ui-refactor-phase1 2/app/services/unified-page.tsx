"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import UnifiedLayout from "@/components/ui/unified-layout"
import FloatingPanel from "@/components/ui/floating-panel"
import { AnimatePresence, motion } from "framer-motion"
import { 
  CalendarDaysIcon,
  PlayIcon,
  MicrophoneIcon,
  ChartBarIcon,
  ClockIcon,
  UserGroupIcon,
  MusicalNoteIcon,
  VideoCameraIcon,
  DocumentTextIcon,
  MegaphoneIcon,
  CogIcon,
  PlusIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  SparklesIcon,
  BookOpenIcon,
  PencilIcon,
  TrashIcon,
  DuplicateIcon,
  ShareIcon,
  PrinterIcon,
  CloudArrowUpIcon,
  FolderIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  Squares2X2Icon,
  QueueListIcon,
  ViewColumnsIcon,
  EyeIcon,
  BellIcon,
  FireIcon
} from "@heroicons/react/24/outline"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

type ServiceMode = "planning" | "live" | "rehearsal" | "review"
type PlanView = "grid" | "timeline" | "outline"

interface ServiceElement {
  id: string
  type: "song" | "sermon" | "prayer" | "announcement" | "video" | "scripture"
  title: string
  duration: number
  leader?: string
  notes?: string
  key?: string
  tempo?: number
  status?: "ready" | "needs-work" | "not-started"
}

interface FloatingPanelData {
  id: string
  type: "element" | "person" | "resource"
  data: any
  position: { x: number; y: number }
}

export default function UnifiedServicesPage() {
  const [serviceMode, setServiceMode] = useState<ServiceMode>("planning")
  const [planView, setPlanView] = useState<PlanView>("timeline")
  const [selectedService, setSelectedService] = useState("sunday-morning")
  const [liveMode, setLiveMode] = useState(false)
  const [floatingPanels, setFloatingPanels] = useState<FloatingPanelData[]>([])
  const [musicStandOpen, setMusicStandOpen] = useState(false)
  const [publishingPanelOpen, setPublishingPanelOpen] = useState(false)

  // Layout modes for services
  const layoutModes = [
    { id: "planning", label: "Planning", icon: CalendarDaysIcon },
    { id: "live", label: "Live", icon: PlayIcon, badge: liveMode ? "ON AIR" : null, pulse: liveMode },
    { id: "rehearsal", label: "Rehearsal", icon: MicrophoneIcon },
    { id: "review", label: "Review", icon: ChartBarIcon }
  ]

  // Quick stats
  const quickStats = [
    { label: "Total Duration", value: "75 min", icon: ClockIcon },
    { label: "Team Members", value: 24, icon: UserGroupIcon, color: "text-blue-400" },
    { label: "Songs", value: 5, icon: MusicalNoteIcon, color: "text-purple-400" },
    { label: "Completion", value: "85%", icon: CheckCircleIcon, color: "text-green-400", trend: { value: 12, positive: true } }
  ]

  // Sample service elements
  const serviceElements: ServiceElement[] = [
    { id: "1", type: "song", title: "Opening Song - Great Are You Lord", duration: 5, leader: "Sarah", key: "G", tempo: 72, status: "ready" },
    { id: "2", type: "prayer", title: "Welcome & Opening Prayer", duration: 3, leader: "Pastor John", status: "ready" },
    { id: "3", type: "song", title: "What A Beautiful Name", duration: 5, leader: "Sarah", key: "D", tempo: 68, status: "needs-work" },
    { id: "4", type: "announcement", title: "Announcements", duration: 5, leader: "Mike", status: "ready" },
    { id: "5", type: "video", title: "Mission Moment Video", duration: 3, status: "ready" },
    { id: "6", type: "song", title: "Build My Life", duration: 5, leader: "Sarah", key: "G", tempo: 72, status: "ready" },
    { id: "7", type: "sermon", title: "Message - Hope in Hard Times", duration: 35, leader: "Pastor John", status: "not-started" },
    { id: "8", type: "song", title: "Response Song - Goodness of God", duration: 5, leader: "Sarah", key: "A", tempo: 76, status: "ready" },
    { id: "9", type: "prayer", title: "Closing Prayer & Benediction", duration: 2, leader: "Pastor John", status: "ready" }
  ]

  const addFloatingPanel = (type: string, data: any) => {
    const newPanel: FloatingPanelData = {
      id: `${type}-${Date.now()}`,
      type: type as any,
      data,
      position: { 
        x: window.innerWidth / 2 - 200 + floatingPanels.length * 20, 
        y: 200 + floatingPanels.length * 20 
      }
    }
    setFloatingPanels([...floatingPanels, newPanel])
  }

  const removeFloatingPanel = (id: string) => {
    setFloatingPanels(floatingPanels.filter(p => p.id !== id))
  }

  // Top bar actions
  const topBarActions = (
    <>
      {/* Plan View Switcher (only in planning mode) */}
      {serviceMode === "planning" && (
        <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
          {[
            { id: "grid", icon: Squares2X2Icon },
            { id: "timeline", icon: QueueListIcon },
            { id: "outline", icon: ViewColumnsIcon }
          ].map((view) => (
            <button
              key={view.id}
              onClick={() => setPlanView(view.id as PlanView)}
              className={cn(
                "p-2 rounded transition-colors",
                planView === view.id
                  ? "bg-[var(--color-primary)] text-black"
                  : "hover:bg-white/10"
              )}
            >
              <view.icon className="h-4 w-4" />
            </button>
          ))}
        </div>
      )}

      <Button 
        variant="glass" 
        size="sm" 
        leftIcon={<MusicalNoteIcon className="h-4 w-4" />}
        onClick={() => setMusicStandOpen(!musicStandOpen)}
      >
        Music Stand
      </Button>

      <Button 
        variant="glass" 
        size="sm" 
        leftIcon={<CloudArrowUpIcon className="h-4 w-4" />}
        onClick={() => setPublishingPanelOpen(!publishingPanelOpen)}
      >
        Publish
      </Button>

      <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
        Add Element
      </Button>
    </>
  )

  // Dock items
  const dockItems = [
    { id: "songs", label: "Songs", icon: MusicalNoteIcon, color: "bg-purple-500", badge: 5 },
    { id: "people", label: "People", icon: UserGroupIcon, color: "bg-blue-500", badge: 24 },
    { id: "media", label: "Media", icon: VideoCameraIcon, color: "bg-pink-500", badge: 3 },
    { id: "docs", label: "Docs", icon: DocumentTextIcon, color: "bg-green-500" },
    { id: "announce", label: "Announce", icon: MegaphoneIcon, color: "bg-amber-500" },
    { id: "settings", label: "Settings", icon: CogIcon, color: "bg-zinc-600" }
  ]

  // Main content based on mode
  const renderContent = () => {
    switch (serviceMode) {
      case "planning":
        return <PlanningMode 
          view={planView} 
          elements={serviceElements}
          onElementClick={(element) => addFloatingPanel("element", element)}
        />
      case "live":
        return <LiveMode 
          elements={serviceElements}
          currentElementIndex={2}
        />
      case "rehearsal":
        return <RehearsalMode 
          elements={serviceElements}
          onElementClick={(element) => addFloatingPanel("element", element)}
        />
      case "review":
        return <ReviewMode 
          serviceData={{
            duration: 73,
            attendance: 342,
            engagement: 87,
            issues: 2
          }}
        />
      default:
        return null
    }
  }

  // Floating panels
  const floatingPanels_ = (
    <AnimatePresence>
      {floatingPanels.map((panel) => (
        <FloatingPanel
          key={panel.id}
          id={panel.id}
          title={panel.data.title || "Details"}
          subtitle={panel.data.leader}
          icon={getIconForType(panel.data.type)}
          iconColor={getColorForType(panel.data.type)}
          position={panel.position}
          onClose={() => removeFloatingPanel(panel.id)}
          width={400}
        >
          {panel.type === "element" && <ElementDetails element={panel.data} />}
        </FloatingPanel>
      ))}
    </AnimatePresence>
  )

  // Right panel content
  const rightPanelContent = publishingPanelOpen ? (
    <PublishingPanel onClose={() => setPublishingPanelOpen(false)} />
  ) : musicStandOpen ? (
    <MusicStand songs={serviceElements.filter(e => e.type === "song")} />
  ) : null

  return (
    <UnifiedLayout
      productName="Services"
      layoutModes={layoutModes}
      currentMode={serviceMode}
      onModeChange={(mode) => setServiceMode(mode as ServiceMode)}
      topBarActions={topBarActions}
      showStatsBar={true}
      quickStats={quickStats}
      floatingPanels={floatingPanels_}
      showBottomDock={true}
      dockItems={dockItems}
      onDockItemClick={(item) => console.log("Dock item clicked:", item)}
      showLiveToggle={true}
      liveMode={liveMode}
      onLiveModeToggle={() => setLiveMode(!liveMode)}
      rightPanel={rightPanelContent}
      rightPanelOpen={musicStandOpen || publishingPanelOpen}
    >
      {renderContent()}
    </UnifiedLayout>
  )
}

// Planning Mode Component
function PlanningMode({ view, elements, onElementClick }: any) {
  if (view === "timeline") {
    return (
      <div className="h-full p-6 overflow-hidden">
        <div className="h-full bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl">
          {/* Timeline Header */}
          <div className="p-4 border-b border-[var(--border-subtle)] flex items-center justify-between">
            <h3 className="text-lg font-medium">Service Timeline</h3>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">Total: 75 min</Badge>
              <Button variant="glass" size="sm">Auto-arrange</Button>
            </div>
          </div>

          {/* Timeline Content */}
          <div className="p-4 h-[calc(100%-80px)] overflow-y-auto custom-scrollbar">
            <div className="relative">
              {/* Time markers */}
              <div className="absolute left-0 top-0 bottom-0 w-20 border-r border-[var(--border-subtle)]">
                {[0, 15, 30, 45, 60, 75].map((time) => (
                  <div key={time} className="absolute left-0 right-0" style={{ top: `${(time / 75) * 100}%` }}>
                    <span className="text-xs text-zinc-400 px-2">{time} min</span>
                  </div>
                ))}
              </div>

              {/* Elements */}
              <div className="ml-24 space-y-2">
                {elements.map((element: ServiceElement, idx: number) => {
                  const startTime = elements.slice(0, idx).reduce((sum: number, e: ServiceElement) => sum + e.duration, 0)
                  return (
                    <motion.div
                      key={element.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: idx * 0.05 }}
                      className={cn(
                        "p-4 rounded-lg border cursor-pointer transition-all hover:shadow-glow",
                        element.status === "ready" ? "bg-green-500/10 border-green-500/30" :
                        element.status === "needs-work" ? "bg-amber-500/10 border-amber-500/30" :
                        "bg-red-500/10 border-red-500/30"
                      )}
                      onClick={() => onElementClick(element)}
                      style={{ minHeight: `${Math.max(element.duration * 4, 60)}px` }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <div className={cn(
                            "w-10 h-10 rounded-lg flex items-center justify-center",
                            getColorForType(element.type)
                          )}>
                            {React.createElement(getIconForType(element.type), { className: "h-5 w-5 text-white" })}
                          </div>
                          <div>
                            <h4 className="font-medium">{element.title}</h4>
                            <div className="flex items-center gap-4 mt-1 text-sm text-zinc-400">
                              <span>{element.duration} min</span>
                              {element.leader && <span>{element.leader}</span>}
                              {element.key && <span>Key: {element.key}</span>}
                              {element.tempo && <span>{element.tempo} BPM</span>}
                            </div>
                          </div>
                        </div>
                        <Badge 
                          variant="secondary" 
                          size="sm"
                          className={cn(
                            element.status === "ready" ? "bg-green-500/20 text-green-400" :
                            element.status === "needs-work" ? "bg-amber-500/20 text-amber-400" :
                            "bg-red-500/20 text-red-400"
                          )}
                        >
                          {element.status}
                        </Badge>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Grid view
  return (
    <div className="h-full p-6 overflow-y-auto custom-scrollbar">
      <div className="grid grid-cols-3 gap-4">
        {elements.map((element: ServiceElement) => (
          <motion.div
            key={element.id}
            whileHover={{ scale: 1.02 }}
            className={cn(
              "p-4 rounded-xl border cursor-pointer transition-all",
              element.status === "ready" ? "bg-green-500/10 border-green-500/30" :
              element.status === "needs-work" ? "bg-amber-500/10 border-amber-500/30" :
              "bg-red-500/10 border-red-500/30"
            )}
            onClick={() => onElementClick(element)}
          >
            <div className="flex items-start justify-between mb-3">
              <div className={cn(
                "w-12 h-12 rounded-lg flex items-center justify-center",
                getColorForType(element.type)
              )}>
                {React.createElement(getIconForType(element.type), { className: "h-6 w-6 text-white" })}
              </div>
              <Badge variant="secondary" size="sm">{element.duration} min</Badge>
            </div>
            <h4 className="font-medium mb-1">{element.title}</h4>
            {element.leader && <p className="text-sm text-zinc-400">{element.leader}</p>}
          </motion.div>
        ))}
      </div>
    </div>
  )
}

// Live Mode Component
function LiveMode({ elements, currentElementIndex }: any) {
  const currentElement = elements[currentElementIndex]
  const nextElement = elements[currentElementIndex + 1]
  const progress = (currentElementIndex / elements.length) * 100

  return (
    <div className="h-full p-6">
      <div className="grid grid-cols-12 gap-4 h-full">
        {/* Main Display */}
        <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
          <div className="h-full flex flex-col">
            {/* Current Element */}
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className={cn(
                  "w-32 h-32 rounded-full mx-auto mb-6 flex items-center justify-center",
                  getColorForType(currentElement.type)
                )}>
                  {React.createElement(getIconForType(currentElement.type), { className: "h-16 w-16 text-white" })}
                </div>
                <h2 className="text-4xl font-bold mb-2">{currentElement.title}</h2>
                <p className="text-xl text-zinc-400 mb-4">{currentElement.leader}</p>
                
                {currentElement.type === "song" && (
                  <div className="flex items-center justify-center gap-6 text-lg">
                    <span>Key: {currentElement.key}</span>
                    <span>{currentElement.tempo} BPM</span>
                  </div>
                )}

                {/* Timer */}
                <div className="mt-8">
                  <div className="text-6xl font-mono">
                    {Math.floor(currentElement.duration / 60)}:{(currentElement.duration % 60).toString().padStart(2, '0')}
                  </div>
                  <div className="w-64 h-2 bg-white/10 rounded-full mx-auto mt-4">
                    <div className="h-full w-1/3 bg-[var(--color-primary)] rounded-full" />
                  </div>
                </div>
              </div>
            </div>

            {/* Service Progress */}
            <div className="pt-4 border-t border-[var(--border-subtle)]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-zinc-400">Service Progress</span>
                <span className="text-sm">{Math.round(progress)}%</span>
              </div>
              <div className="h-2 bg-white/10 rounded-full">
                <div className="h-full bg-[var(--color-primary)] rounded-full transition-all" style={{ width: `${progress}%` }} />
              </div>
            </div>
          </div>
        </div>

        {/* Side Panels */}
        <div className="col-span-4 space-y-4">
          {/* Next Up */}
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Next Up</h3>
            {nextElement && (
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-10 h-10 rounded-lg flex items-center justify-center",
                  getColorForType(nextElement.type)
                )}>
                  {React.createElement(getIconForType(nextElement.type), { className: "h-5 w-5 text-white" })}
                </div>
                <div>
                  <p className="font-medium">{nextElement.title}</p>
                  <p className="text-sm text-zinc-400">{nextElement.duration} min • {nextElement.leader}</p>
                </div>
              </div>
            )}
          </div>

          {/* Live Stats */}
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Live Stats</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <p className="text-2xl font-bold">342</p>
                <p className="text-xs text-zinc-400">Attendance</p>
              </div>
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <p className="text-2xl font-bold">234</p>
                <p className="text-xs text-zinc-400">Online</p>
              </div>
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <p className="text-2xl font-bold">45</p>
                <p className="text-xs text-zinc-400">Minutes Left</p>
              </div>
              <div className="text-center p-3 bg-white/5 rounded-lg">
                <p className="text-2xl font-bold">ON</p>
                <p className="text-xs text-zinc-400">Recording</p>
              </div>
            </div>
          </div>

          {/* Quick Controls */}
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Controls</h3>
            <div className="space-y-2">
              <Button variant="glass" size="sm" className="w-full">
                <ChevronLeftIcon className="h-4 w-4 mr-2" />
                Previous
              </Button>
              <Button variant="primary" size="sm" className="w-full">
                <ChevronRightIcon className="h-4 w-4 mr-2" />
                Next
              </Button>
              <Button variant="glass" size="sm" className="w-full">
                <BellIcon className="h-4 w-4 mr-2" />
                Send Alert
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Rehearsal Mode Component
function RehearsalMode({ elements, onElementClick }: any) {
  return (
    <div className="h-full p-6">
      <div className="grid grid-cols-12 gap-4 h-full">
        {/* Rehearsal Schedule */}
        <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
          <h3 className="text-lg font-medium mb-4">Rehearsal Schedule</h3>
          <div className="space-y-3">
            {elements.filter((e: ServiceElement) => e.type === "song" || e.type === "video").map((element: ServiceElement) => (
              <div 
                key={element.id}
                className="flex items-center justify-between p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                onClick={() => onElementClick(element)}
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-12 h-12 rounded-lg flex items-center justify-center",
                    getColorForType(element.type)
                  )}>
                    {React.createElement(getIconForType(element.type), { className: "h-6 w-6 text-white" })}
                  </div>
                  <div>
                    <h4 className="font-medium">{element.title}</h4>
                    <p className="text-sm text-zinc-400">{element.leader} • {element.duration} min</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {element.type === "song" && (
                    <>
                      <Badge variant="secondary">{element.key}</Badge>
                      <Badge variant="secondary">{element.tempo} BPM</Badge>
                    </>
                  )}
                  <Button variant="glass" size="sm">Rehearse</Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Team Availability */}
        <div className="col-span-4 space-y-4">
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Team Availability</h3>
            <div className="space-y-2">
              {["Sarah (Worship)", "Mike (Sound)", "Lisa (Keys)", "John (Drums)"].map((person) => (
                <div key={person} className="flex items-center justify-between p-2 bg-white/5 rounded-lg">
                  <span className="text-sm">{person}</span>
                  <CheckCircleIcon className="h-4 w-4 text-green-400" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Review Mode Component
function ReviewMode({ serviceData }: any) {
  return (
    <div className="h-full p-6">
      <div className="grid grid-cols-12 gap-4">
        {/* Service Metrics */}
        <div className="col-span-8 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
          <h3 className="text-lg font-medium mb-4">Service Review</h3>
          
          {/* Key Metrics */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-white/5 rounded-lg">
              <ClockIcon className="h-8 w-8 mx-auto mb-2 text-blue-400" />
              <p className="text-2xl font-bold">{serviceData.duration} min</p>
              <p className="text-xs text-zinc-400">Total Duration</p>
            </div>
            <div className="text-center p-4 bg-white/5 rounded-lg">
              <UserGroupIcon className="h-8 w-8 mx-auto mb-2 text-green-400" />
              <p className="text-2xl font-bold">{serviceData.attendance}</p>
              <p className="text-xs text-zinc-400">Attendance</p>
            </div>
            <div className="text-center p-4 bg-white/5 rounded-lg">
              <FireIcon className="h-8 w-8 mx-auto mb-2 text-purple-400" />
              <p className="text-2xl font-bold">{serviceData.engagement}%</p>
              <p className="text-xs text-zinc-400">Engagement</p>
            </div>
            <div className="text-center p-4 bg-white/5 rounded-lg">
              <ExclamationTriangleIcon className="h-8 w-8 mx-auto mb-2 text-amber-400" />
              <p className="text-2xl font-bold">{serviceData.issues}</p>
              <p className="text-xs text-zinc-400">Issues</p>
            </div>
          </div>

          {/* Feedback Section */}
          <div>
            <h4 className="font-medium mb-3">Service Feedback</h4>
            <div className="space-y-3">
              <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                <div className="flex items-start gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium text-green-400">Strengths</p>
                    <p className="text-sm mt-1">Worship flow was excellent. Smooth transitions between elements.</p>
                  </div>
                </div>
              </div>
              <div className="p-4 bg-amber-500/10 border border-amber-500/30 rounded-lg">
                <div className="flex items-start gap-2">
                  <ExclamationTriangleIcon className="h-5 w-5 text-amber-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium text-amber-400">Areas for Improvement</p>
                    <p className="text-sm mt-1">Sound levels needed adjustment during second song. Announcements ran long.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Items */}
        <div className="col-span-4 space-y-4">
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Action Items</h3>
            <div className="space-y-2">
              <Button variant="glass" size="sm" className="w-full justify-start">
                <ShareIcon className="h-4 w-4 mr-2" />
                Share Report
              </Button>
              <Button variant="glass" size="sm" className="w-full justify-start">
                <PrinterIcon className="h-4 w-4 mr-2" />
                Print Summary
              </Button>
              <Button variant="glass" size="sm" className="w-full justify-start">
                <DuplicateIcon className="h-4 w-4 mr-2" />
                Use as Template
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Helper Components
function ElementDetails({ element }: { element: ServiceElement }) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-3">
        <div className="bg-white/5 rounded-lg p-3">
          <p className="text-xs text-zinc-400 mb-1">Duration</p>
          <p className="font-medium">{element.duration} minutes</p>
        </div>
        <div className="bg-white/5 rounded-lg p-3">
          <p className="text-xs text-zinc-400 mb-1">Leader</p>
          <p className="font-medium">{element.leader || "Not assigned"}</p>
        </div>
      </div>

      {element.type === "song" && (
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-white/5 rounded-lg p-3">
            <p className="text-xs text-zinc-400 mb-1">Key</p>
            <p className="font-medium">{element.key}</p>
          </div>
          <div className="bg-white/5 rounded-lg p-3">
            <p className="text-xs text-zinc-400 mb-1">Tempo</p>
            <p className="font-medium">{element.tempo} BPM</p>
          </div>
        </div>
      )}

      <div className="space-y-2">
        <Button variant="glass" size="sm" className="w-full">
          <PencilIcon className="h-4 w-4 mr-2" />
          Edit Details
        </Button>
        <Button variant="glass" size="sm" className="w-full">
          <DuplicateIcon className="h-4 w-4 mr-2" />
          Duplicate
        </Button>
        <Button variant="destructive" size="sm" className="w-full">
          <TrashIcon className="h-4 w-4 mr-2" />
          Remove
        </Button>
      </div>
    </div>
  )
}

function MusicStand({ songs }: { songs: ServiceElement[] }) {
  const [selectedSong, setSelectedSong] = useState(songs[0])
  const [fontSize, setFontSize] = useState(16)
  const [transpose, setTranspose] = useState(0)

  return (
    <div className="h-full flex flex-col p-4">
      <h3 className="text-lg font-medium mb-4">Music Stand</h3>
      
      {/* Song List */}
      <div className="space-y-2 mb-4">
        {songs.map((song) => (
          <button
            key={song.id}
            onClick={() => setSelectedSong(song)}
            className={cn(
              "w-full text-left p-3 rounded-lg transition-colors",
              selectedSong?.id === song.id
                ? "bg-[var(--color-primary)]/20 border border-[var(--color-primary)]/30"
                : "bg-white/5 hover:bg-white/10"
            )}
          >
            <p className="font-medium">{song.title}</p>
            <p className="text-sm text-zinc-400">Key: {song.key} • {song.tempo} BPM</p>
          </button>
        ))}
      </div>

      {/* Controls */}
      <div className="space-y-3 pb-4 border-b border-[var(--border-subtle)]">
        <div>
          <label className="text-xs text-zinc-400">Font Size</label>
          <div className="flex items-center gap-2 mt-1">
            <button 
              onClick={() => setFontSize(Math.max(12, fontSize - 2))}
              className="p-1 hover:bg-white/10 rounded"
            >
              -
            </button>
            <span className="w-12 text-center">{fontSize}</span>
            <button 
              onClick={() => setFontSize(Math.min(24, fontSize + 2))}
              className="p-1 hover:bg-white/10 rounded"
            >
              +
            </button>
          </div>
        </div>

        <div>
          <label className="text-xs text-zinc-400">Transpose</label>
          <div className="flex items-center gap-2 mt-1">
            <button 
              onClick={() => setTranspose(transpose - 1)}
              className="p-1 hover:bg-white/10 rounded"
            >
              -
            </button>
            <span className="w-12 text-center">{transpose > 0 ? `+${transpose}` : transpose}</span>
            <button 
              onClick={() => setTranspose(transpose + 1)}
              className="p-1 hover:bg-white/10 rounded"
            >
              +
            </button>
          </div>
        </div>
      </div>

      {/* Lyrics/Chords Display */}
      <div className="flex-1 mt-4 overflow-y-auto">
        <div style={{ fontSize: `${fontSize}px` }}>
          <p className="font-mono whitespace-pre-wrap">
            {`[Verse 1]
G                 D
Great are You Lord
Em               C
You are worthy of praise

[Chorus]
G        D      Em    C
Holy, Holy, Holy Lord`}
          </p>
        </div>
      </div>
    </div>
  )
}

function PublishingPanel({ onClose }: { onClose: () => void }) {
  return (
    <div className="h-full flex flex-col p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Publishing Center</h3>
        <button onClick={onClose} className="p-1 hover:bg-white/10 rounded">
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-white/5 rounded-lg">
          <h4 className="font-medium mb-2">Planning Center</h4>
          <p className="text-sm text-zinc-400 mb-3">Sync your service plan</p>
          <Button variant="primary" size="sm" className="w-full">
            <CloudArrowUpIcon className="h-4 w-4 mr-2" />
            Publish to PCO
          </Button>
        </div>

        <div className="p-4 bg-white/5 rounded-lg">
          <h4 className="font-medium mb-2">ProPresenter</h4>
          <p className="text-sm text-zinc-400 mb-3">Export for presentation</p>
          <Button variant="glass" size="sm" className="w-full">
            Export Playlist
          </Button>
        </div>

        <div className="p-4 bg-white/5 rounded-lg">
          <h4 className="font-medium mb-2">Team Communication</h4>
          <p className="text-sm text-zinc-400 mb-3">Notify your team</p>
          <Button variant="glass" size="sm" className="w-full">
            Send Updates
          </Button>
        </div>
      </div>
    </div>
  )
}

// Helper functions
function getIconForType(type: string) {
  switch (type) {
    case "song": return MusicalNoteIcon
    case "sermon": return BookOpenIcon
    case "prayer": return SparklesIcon
    case "announcement": return MegaphoneIcon
    case "video": return VideoCameraIcon
    case "scripture": return BookOpenIcon
    default: return DocumentTextIcon
  }
}

function getColorForType(type: string) {
  switch (type) {
    case "song": return "bg-purple-500"
    case "sermon": return "bg-blue-500"
    case "prayer": return "bg-green-500"
    case "announcement": return "bg-amber-500"
    case "video": return "bg-pink-500"
    case "scripture": return "bg-indigo-500"
    default: return "bg-zinc-500"
  }
}