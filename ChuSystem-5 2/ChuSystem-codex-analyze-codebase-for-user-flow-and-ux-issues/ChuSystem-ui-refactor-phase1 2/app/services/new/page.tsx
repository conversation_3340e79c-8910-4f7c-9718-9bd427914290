"use client"

import { ArrowLeftIcon, PlusIcon, CalendarIcon, ClockIcon, MapPinIcon, UserGroupIcon } from "@heroicons/react/16/solid"
import { ModuleLayout } from "@/components/ui/module-layout"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import QuickActions from "@/components/ui/quick-actions"

export default function NewServicePage() {
  const quickActions = [
    {
      id: "use-template",
      icon: <CalendarIcon className="h-5 w-5" />,
      label: "Use Template",
      onClick: () => console.log("Use template"),
      color: "bg-blue-500",
    },
    {
      id: "schedule-team",
      icon: <UserGroupIcon className="h-5 w-5" />,
      label: "Schedule Team",
      onClick: () => console.log("Schedule team"),
      color: "bg-green-500",
    },
    {
      id: "save-draft",
      icon: <PlusIcon className="h-5 w-5" />,
      label: "Save Draft",
      onClick: () => console.log("Save draft"),
      color: "bg-purple-500",
    },
  ]

  return (
    <ModuleLayout
      title="Create New Service"
      description="Plan a new service or use a template"
      icon={<CalendarIcon className="h-6 w-6 text-zinc-400" />}
      actions={
        <div className="flex gap-3">
          <Button variant="outline" size="md" leftIcon={<ArrowLeftIcon className="h-4 w-4" />} asChild>
            <Link href="/services">Back to Services</Link>
          </Button>
          <Button variant="glass" size="md" leftIcon={<PlusIcon className="h-4 w-4" />}>
            Create Service
          </Button>
        </div>
      }
      quickActions={<QuickActions actions={quickActions} />}
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Service Builder */}
        <div className="lg:col-span-2">
          <Card variant="glass" padding="lg">
            <h2 className="text-xl font-semibold mb-6">Service Details</h2>

            <div className="space-y-6">
              <div>
                <Label htmlFor="service-title">Service Title</Label>
                <Input
                  id="service-title"
                  placeholder="Sunday Worship - May 28, 2023"
                  className="mt-1 bg-black/20 border-white/5"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="service-date">Date</Label>
                  <div className="relative mt-1">
                    <Input
                      id="service-date"
                      type="date"
                      defaultValue="2023-05-28"
                      className="pl-10 bg-black/20 border-white/5"
                    />
                    <CalendarIcon className="absolute left-3 top-2.5 h-5 w-5 text-zinc-400" />
                  </div>
                </div>
                <div>
                  <Label htmlFor="service-time">Time</Label>
                  <div className="relative mt-1">
                    <Input
                      id="service-time"
                      type="time"
                      defaultValue="09:00"
                      className="pl-10 bg-black/20 border-white/5"
                    />
                    <ClockIcon className="absolute left-3 top-2.5 h-5 w-5 text-zinc-400" />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="service-type">Service Type</Label>
                  <Select defaultValue="contemporary">
                    <SelectTrigger className="mt-1 bg-black/20 border-white/5">
                      <SelectValue placeholder="Select service type" />
                    </SelectTrigger>
                    <SelectContent className="bg-black/90 border-white/10">
                      <SelectItem value="contemporary">Contemporary</SelectItem>
                      <SelectItem value="traditional">Traditional</SelectItem>
                      <SelectItem value="youth">Youth Service</SelectItem>
                      <SelectItem value="midweek">Midweek</SelectItem>
                      <SelectItem value="special">Special Event</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="service-team">Primary Team</Label>
                  <div className="relative mt-1">
                    <Select defaultValue="worship-team-a">
                      <SelectTrigger className="pl-10 bg-black/20 border-white/5">
                        <SelectValue placeholder="Select primary team" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/90 border-white/10">
                        <SelectItem value="worship-team-a">Worship Team A</SelectItem>
                        <SelectItem value="worship-team-b">Worship Team B</SelectItem>
                        <SelectItem value="youth-band">Youth Band</SelectItem>
                        <SelectItem value="choir">Choir</SelectItem>
                      </SelectContent>
                    </Select>
                    <UserGroupIcon className="absolute left-3 top-2.5 h-5 w-5 text-zinc-400" />
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="service-location">Location</Label>
                <div className="relative mt-1">
                  <Select defaultValue="main-sanctuary">
                    <SelectTrigger className="pl-10 bg-black/20 border-white/5">
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent className="bg-black/90 border-white/10">
                      <SelectItem value="main-sanctuary">Main Sanctuary</SelectItem>
                      <SelectItem value="chapel">Chapel</SelectItem>
                      <SelectItem value="youth-center">Youth Center</SelectItem>
                      <SelectItem value="fellowship-hall">Fellowship Hall</SelectItem>
                    </SelectContent>
                  </Select>
                  <MapPinIcon className="absolute left-3 top-2.5 h-5 w-5 text-zinc-400" />
                </div>
              </div>

              <div className="pt-4 flex justify-end gap-3">
                <Button variant="outline" size="md">
                  Save as Draft
                </Button>
                <Button variant="glass" size="md">
                  Continue to Timeline
                </Button>
              </div>
            </div>
          </Card>
        </div>

        {/* Right Column - Templates */}
        <div>
          <Card variant="glass" padding="lg" className="h-full">
            <h2 className="text-xl font-semibold mb-6">Service Templates</h2>
            <div className="space-y-4">
              <p className="text-sm text-zinc-400">
                Start with a template to save time. Templates include pre-defined service elements and team roles.
              </p>

              <div className="space-y-3">
                <TemplateCard
                  title="Contemporary Worship"
                  description="60-minute service with worship, announcements, sermon"
                  elements={5}
                  duration="60 min"
                />

                <TemplateCard
                  title="Traditional Service"
                  description="75-minute service with hymns, liturgy, sermon"
                  elements={7}
                  duration="75 min"
                />

                <TemplateCard
                  title="Youth Service"
                  description="45-minute service with modern worship, games, message"
                  elements={4}
                  duration="45 min"
                />

                <TemplateCard
                  title="Midweek Bible Study"
                  description="60-minute service with worship, prayer, teaching"
                  elements={3}
                  duration="60 min"
                />
              </div>

              <Button variant="outline" size="sm" className="w-full mt-4" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Create New Template
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </ModuleLayout>
  )
}

function TemplateCard({
  title,
  description,
  elements,
  duration,
}: {
  title: string
  description: string
  elements: number
  duration: string
}) {
  return (
    <div className="p-3 bg-black/20 hover:bg-black/30 rounded-xl border border-white/5 hover:border-white/10 transition-all cursor-pointer">
      <div className="flex items-center justify-between mb-1">
        <h3 className="font-medium">{title}</h3>
        <span className="text-xs text-zinc-400">{duration}</span>
      </div>
      <p className="text-xs text-zinc-400 mb-2">{description}</p>
      <div className="flex items-center text-xs text-zinc-500">
        <CalendarIcon className="h-3.5 w-3.5 mr-1" />
        <span>{elements} elements</span>
      </div>
    </div>
  )
}
