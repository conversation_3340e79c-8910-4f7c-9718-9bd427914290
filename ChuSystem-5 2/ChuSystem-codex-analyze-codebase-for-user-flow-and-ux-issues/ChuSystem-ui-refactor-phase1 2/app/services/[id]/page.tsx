"use client"

import { useState } from "react"
import {
  ArrowLeftIcon,
  PlayIcon,
  PencilIcon,
  UserGroupIcon,
  DocumentTextIcon,
  MusicalNoteIcon,
  ClockIcon,
  PlusIcon,
} from "@heroicons/react/16/solid"
import { ModuleLayout } from "@/components/ui/module-layout"
import ServiceTimelineBuilder from "@/components/service-planning/service-timeline-builder"
import TeamView from "@/components/service-planning/team-view"
import ResourceManager from "@/components/service-planning/resource-manager"
import CollaborationPanel from "@/components/service-planning/collaboration-panel"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card } from "@/components/ui/card"
import QuickActions from "@/components/ui/quick-actions"
import Link from "next/link"

export default function ServiceDetailPage({ params }: { params: { id: string } }) {
  const [activeTab, setActiveTab] = useState("timeline")

  const quickActions = [
    {
      id: "add-song",
      icon: <MusicalNoteIcon className="h-5 w-5" />,
      label: "Add Song",
      onClick: () => console.log("Add song"),
      color: "bg-purple-500",
    },
    {
      id: "add-team-member",
      icon: <UserGroupIcon className="h-5 w-5" />,
      label: "Add Team Member",
      onClick: () => console.log("Add team member"),
      color: "bg-green-500",
    },
    {
      id: "live-mode",
      icon: <PlayIcon className="h-5 w-5" />,
      label: "Live Mode",
      onClick: () => console.log("Live mode"),
      color: "bg-blue-500",
    },
  ]

  return (
    <ModuleLayout
      title="Sunday Service - May 28, 2023"
      description="9:00 AM | Main Auditorium"
      icon={<ClockIcon className="h-6 w-6 text-zinc-400" />}
      actions={
        <div className="flex gap-3">
          <Button variant="outline" size="md" leftIcon={<ArrowLeftIcon className="h-4 w-4" />} asChild>
            <Link href="/services">Back</Link>
          </Button>
          <Button variant="outline" size="md" leftIcon={<PencilIcon className="h-4 w-4" />}>
            Edit
          </Button>
          <Button variant="glass" size="md" leftIcon={<PlayIcon className="h-4 w-4" />} asChild>
            <Link href={`/services/live/${params.id}`}>Live Mode</Link>
          </Button>
        </div>
      }
      quickActions={<QuickActions actions={quickActions} />}
      sidePanel={<CollaborationPanel serviceId={params.id} />}
    >
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6 bg-black/30 p-1 rounded-xl border border-white/5">
          <TabsTrigger value="timeline" className="data-[state=active]:bg-white/10 rounded-lg">
            <DocumentTextIcon className="h-4 w-4 mr-2" />
            Timeline
          </TabsTrigger>
          <TabsTrigger value="team" className="data-[state=active]:bg-white/10 rounded-lg">
            <UserGroupIcon className="h-4 w-4 mr-2" />
            Team
          </TabsTrigger>
          <TabsTrigger value="resources" className="data-[state=active]:bg-white/10 rounded-lg">
            <DocumentTextIcon className="h-4 w-4 mr-2" />
            Resources
          </TabsTrigger>
        </TabsList>

        <TabsContent value="timeline" className="mt-0">
          <Card variant="glass" padding="lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Service Timeline</h2>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  60 min
                </Button>
                <Button variant="outline" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                  Add Item
                </Button>
              </div>
            </div>
            <ServiceTimelineBuilder serviceId={params.id} />
          </Card>
        </TabsContent>

        <TabsContent value="team" className="mt-0">
          <Card variant="glass" padding="lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Team Members</h2>
              <Button variant="outline" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Add Member
              </Button>
            </div>
            <TeamView serviceId={params.id} />
          </Card>
        </TabsContent>

        <TabsContent value="resources" className="mt-0">
          <Card variant="glass" padding="lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Service Resources</h2>
              <Button variant="outline" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Add Resource
              </Button>
            </div>
            <ResourceManager serviceId={params.id} />
          </Card>
        </TabsContent>
      </Tabs>
    </ModuleLayout>
  )
}
