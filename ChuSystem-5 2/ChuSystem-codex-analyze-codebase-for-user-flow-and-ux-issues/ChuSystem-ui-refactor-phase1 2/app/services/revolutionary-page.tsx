"use client"

import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import SidebarRevolutionary from "@/components/sidebar-revolutionary"
import Floating<PERSON>ommandCenter from "@/components/ui/floating-command-center"
import { 
  CalendarDaysIcon,
  PlayIcon,
  MicrophoneIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  MusicalNoteIcon,
  UserGroupIcon,
  ClockIcon,
  SparklesIcon,
  ArrowsPointingOutIcon,
  Squares2X2Icon,
  AdjustmentsHorizontalIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ViewColumnsIcon,
  QueueListIcon,
  DocumentDuplicateIcon as DuplicateIcon,
  ArrowPathIcon,
  BellIcon,
  CalendarIcon,
  TagIcon,
  FolderIcon,
  UsersIcon,
  CogIcon,
  InformationCircleIcon,
  StarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  VideoCameraIcon
} from "@heroicons/react/24/outline"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { GlassCard } from "@/components/ui/glass-card"
import Image from "next/image"

type LayoutMode = "schedule" | "planning" | "teams" | "templates"
type ViewMode = "month" | "week" | "list"

interface Service {
  id: string
  title: string
  date: Date
  time: string
  type: "traditional" | "contemporary" | "youth" | "midweek" | "special"
  status: "draft" | "planning" | "confirmed" | "completed"
  teamSize: number
  teamConfirmed: number
  duration: string
  template?: string
  location?: string
  expectedAttendance?: number
}

interface ServiceItem {
  id: string
  type: "song" | "message" | "prayer" | "announcement" | "transition" | "special"
  title: string
  duration: number
  assignedTo?: string[]
  notes?: string
  resources?: string[]
  confirmed?: boolean
  order: number
}

interface TeamMember {
  id: string
  name: string
  role: string
  avatar?: string
  availability: "confirmed" | "pending" | "declined" | "unavailable"
  conflicts?: string[]
  preferredRoles?: string[]
  lastServed?: string
  skills?: string[]
  email?: string
  phone?: string
}

interface ServiceTemplate {
  id: string
  name: string
  description: string
  type: string
  items: ServiceItem[]
  defaultTeamSize: number
  lastUsed?: Date
  usageCount: number
  tags: string[]
}

// Helper function to get services for the week
const getServicesForWeek = (): Service[] => {
  const services: Service[] = [
    { 
      id: "sun-8am", 
      title: "Sunday 8:00 AM", 
      date: new Date(), 
      time: "8:00 AM", 
      type: "traditional", 
      teamSize: 12, 
      teamConfirmed: 10,
      status: "confirmed",
      duration: "1h 15m"
    },
    { 
      id: "sun-10am", 
      title: "Sunday 10:00 AM", 
      date: new Date(), 
      time: "10:00 AM", 
      type: "contemporary", 
      teamSize: 18,
      teamConfirmed: 12, 
      status: "planning",
      duration: "1h 30m"
    },
    { 
      id: "sun-6pm", 
      title: "Sunday 6:00 PM", 
      date: new Date(), 
      time: "6:00 PM", 
      type: "youth", 
      teamSize: 8,
      teamConfirmed: 8, 
      status: "draft",
      duration: "1h"
    },
    { 
      id: "wed-7pm", 
      title: "Wednesday 7:00 PM", 
      date: new Date(), 
      time: "7:00 PM", 
      type: "midweek", 
      teamSize: 10,
      teamConfirmed: 9, 
      status: "confirmed",
      duration: "45m"
    },
    { 
      id: "fri-7pm", 
      title: "Friday Youth", 
      date: new Date(), 
      time: "7:00 PM", 
      type: "youth", 
      teamSize: 8,
      teamConfirmed: 6, 
      status: "planning",
      duration: "1h 15m"
    },
  ]
  return services
}

export default function RevolutionaryServicesPage() {
  const [layoutMode, setLayoutMode] = useState<LayoutMode>("schedule")
  const [viewMode, setViewMode] = useState<ViewMode>("week")
  const [selectedService, setSelectedService] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [showCreateFlow, setShowCreateFlow] = useState(false)
  const [showTeamAssignment, setShowTeamAssignment] = useState(false)
  const [keyboardShortcuts, setKeyboardShortcuts] = useState(true)
  const [autoSave, setAutoSave] = useState(true)
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)

  const handleLayoutModeChange = (mode: LayoutMode) => {
    setLayoutMode(mode)
  }

  // Sample team members data
  const teamMembers: TeamMember[] = [
    { id: "1", name: "Sarah Johnson", role: "Worship Leader", availability: "confirmed", lastServed: "Last Sunday" },
    { id: "2", name: "Mike Davis", role: "Keys", availability: "pending", lastServed: "2 weeks ago" },
    { id: "3", name: "Lisa Chen", role: "Vocals", availability: "confirmed", lastServed: "Last Sunday" },
    { id: "4", name: "Tom Wilson", role: "Guitar", availability: "declined", conflicts: ["Out of town"], lastServed: "3 weeks ago" },
    { id: "5", name: "Emily Brown", role: "Drums", availability: "confirmed", lastServed: "Last Sunday" },
    { id: "6", name: "James Miller", role: "Sound", availability: "unavailable", lastServed: "4 weeks ago" },
    { id: "7", name: "Anna Taylor", role: "Slides", availability: "confirmed", lastServed: "Last Sunday" },
    { id: "8", name: "David Lee", role: "Bass", availability: "pending", lastServed: "2 weeks ago" },
  ]

  // Get services for the week
  const getServicesForWeek = () => {
    const services = [
      { id: "sun-8am", name: "Sunday 8:00 AM", date: "Sun", time: "8:00 AM", type: "traditional", teamSize: 12, status: "confirmed" },
      { id: "sun-10am", name: "Sunday 10:00 AM", date: "Sun", time: "10:00 AM", type: "contemporary", teamSize: 18, status: "planning" },
      { id: "sun-6pm", name: "Sunday 6:00 PM", date: "Sun", time: "6:00 PM", type: "youth", teamSize: 8, status: "draft" },
      { id: "wed-7pm", name: "Wednesday 7:00 PM", date: "Wed", time: "7:00 PM", type: "midweek", teamSize: 10, status: "confirmed" },
      { id: "fri-7pm", name: "Friday Youth", date: "Fri", time: "7:00 PM", type: "youth", teamSize: 8, status: "planning" },
    ]
    return services
  }

  // Keyboard shortcuts
  React.useEffect(() => {
    if (!keyboardShortcuts) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case "1":
            e.preventDefault()
            handleLayoutModeChange("schedule")
            break
          case "2":
            e.preventDefault()
            handleLayoutModeChange("planning")
            break
          case "3":
            e.preventDefault()
            handleLayoutModeChange("teams")
            break
          case "4":
            e.preventDefault()
            handleLayoutModeChange("templates")
            break
          case "n":
            e.preventDefault()
            setShowCreateFlow(true)
            break
          case "t":
            e.preventDefault()
            setShowTeamAssignment(true)
            break
          case "s":
            e.preventDefault()
            // Auto-save functionality
            console.log("Service auto-saved")
            break
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [keyboardShortcuts, showCreateFlow, showTeamAssignment])

  // Auto-save functionality
  React.useEffect(() => {
    if (!autoSave) return

    const autoSaveTimer = setInterval(() => {
      console.log("Auto-saving service data...")
      // Here you would save to your backend/database
    }, 30000) // Auto-save every 30 seconds

    return () => clearInterval(autoSaveTimer)
  }, [autoSave, layoutMode, selectedService])

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Sidebar */}
      <SidebarRevolutionary />

      {/* Main Content - NO SCROLLING */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Sticky Top Bar with Mode Switcher */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] sticky top-0 z-30">
          <div className="h-full px-6 flex items-center justify-between">
            {/* Mode Switcher - Service Management Focus */}
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">Service Planning</h1>
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                {[
                  { id: "schedule", label: "Schedule", icon: CalendarDaysIcon },
                  { id: "planning", label: "Planning", icon: ClipboardDocumentListIcon },
                  { id: "teams", label: "Teams", icon: UserGroupIcon },
                  { id: "templates", label: "Templates", icon: DocumentTextIcon }
                ].map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => handleLayoutModeChange(mode.id as LayoutMode)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded transition-all",
                      layoutMode === mode.id
                        ? "bg-[var(--color-primary)] text-black"
                        : "hover:bg-white/10"
                    )}
                  >
                    <mode.icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{mode.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Controls & Actions */}
            <div className="flex items-center gap-4">
              {/* View Mode Switcher */}
              {layoutMode === "schedule" && (
                <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                  {[
                    { id: "month", icon: CalendarDaysIcon },
                    { id: "week", icon: ViewColumnsIcon },
                    { id: "list", icon: QueueListIcon }
                  ].map((view) => (
                    <button
                      key={view.id}
                      onClick={() => setViewMode(view.id as ViewMode)}
                      className={cn(
                        "p-2 rounded transition-colors",
                        viewMode === view.id
                          ? "bg-[var(--color-primary)] text-black"
                          : "hover:bg-white/10"
                      )}
                      title={`${view.id.charAt(0).toUpperCase() + view.id.slice(1)} view`}
                    >
                      <view.icon className="h-4 w-4" />
                    </button>
                  ))}
                </div>
              )}

              {/* Auto-save indicator */}
              {autoSave && (
                <div className="flex items-center gap-1 text-xs text-green-400">
                  <CheckCircleIcon className="h-3 w-3" />
                  <span>All changes saved</span>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button 
                  variant="glass" 
                  size="sm" 
                  leftIcon={<DuplicateIcon className="h-4 w-4" />}
                  title="Duplicate from template (⌘D)"
                >
                  Use Template
                </Button>
                <Button 
                  variant="glass" 
                  size="sm" 
                  leftIcon={<UserGroupIcon className="h-4 w-4" />}
                  onClick={() => setShowTeamAssignment(true)}
                  title="Assign teams (⌘T)"
                >
                  Assign Teams
                </Button>
                <Button 
                  variant="primary" 
                  size="sm" 
                  leftIcon={<PlusIcon className="h-4 w-4" />}
                  onClick={() => setShowCreateFlow(true)}
                  title="Create new service (⌘N)"
                >
                  New Service
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Context Bar - Shows different content based on mode */}
        <div className="h-12 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center justify-between sticky top-16 z-20">
          {layoutMode === "planning" && selectedService ? (
            <>
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setSelectedService(null)}
                  className="p-1 hover:bg-white/10 rounded transition-colors"
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>
                <h2 className="text-sm font-medium">Sunday 10:00 AM Service</h2>
                <Badge variant="secondary" size="sm" className="bg-amber-500/20 text-amber-400">
                  Planning
                </Badge>
                <div className="w-px h-5 bg-[var(--border-subtle)]" />
                <span className="text-sm text-zinc-400">March 24, 2025</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-zinc-400">Team: 12/18 confirmed</span>
                <div className="w-px h-5 bg-[var(--border-subtle)]" />
                <span className="text-xs text-zinc-400">Duration: 1h 30m</span>
              </div>
            </>
          ) : layoutMode === "schedule" ? (
            <>
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setSelectedDate(new Date(selectedDate.setDate(selectedDate.getDate() - 7)))}
                  className="p-1 hover:bg-white/10 rounded transition-colors"
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                </button>
                <h2 className="text-sm font-medium">
                  {viewMode === "week" ? `Week of ${selectedDate.toLocaleDateString()}` :
                   viewMode === "month" ? selectedDate.toLocaleDateString([], { month: 'long', year: 'numeric' }) :
                   "All Services"}
                </h2>
                <button
                  onClick={() => setSelectedDate(new Date(selectedDate.setDate(selectedDate.getDate() + 7)))}
                  className="p-1 hover:bg-white/10 rounded transition-colors"
                >
                  <ChevronRightIcon className="h-4 w-4" />
                </button>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="glass" size="sm" onClick={() => setSelectedDate(new Date())}>
                  Today
                </Button>
              </div>
            </>
          ) : (
            <div className="flex items-center gap-2 text-sm text-zinc-400">
              <SparklesIcon className="h-4 w-4" />
              <span>Select a service to start planning</span>
            </div>
          )}
        </div>

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          {layoutMode === "schedule" && (
            <ScheduleView 
              viewMode={viewMode} 
              selectedDate={selectedDate}
              onServiceClick={(service) => {
                setSelectedService(service.id)
                setLayoutMode("planning")
              }}
              onCreateNew={() => setShowCreateFlow(true)}
              onDateChange={setSelectedDate}
            />
          )}
          
          {layoutMode === "planning" && (
            <PlanningView 
              serviceId={selectedService}
              onBack={() => {
                setSelectedService(null)
                setLayoutMode("schedule")
              }}
              onTeamAssign={() => setShowTeamAssignment(true)}
              onTemplateSelect={() => setLayoutMode("templates")}
            />
          )}
          
          {layoutMode === "teams" && (
            <TeamsView 
              onAssignTeam={(teamMember, serviceId) => {
                setSelectedService(serviceId)
                setShowTeamAssignment(true)
              }}
              onCreateTeam={() => {}}
            />
          )}
          
          {layoutMode === "templates" && (
            <TemplatesView 
              selectedTemplate={selectedTemplate}
              onTemplateSelect={setSelectedTemplate}
              onUseTemplate={(templateId) => {
                setSelectedTemplate(templateId)
                setShowCreateFlow(true)
              }}
              onBack={() => setLayoutMode("schedule")}
            />
          )}
        </div>

        {/* Enhanced Bottom Status Bar - Service Management Focus */}
        <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-t border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <div className="flex items-center gap-8 text-sm">
            {layoutMode === "schedule" && (
              <>
                <div className="flex items-center gap-2">
                  <CalendarDaysIcon className="h-4 w-4 text-zinc-400" />
                  <span>{getServicesForWeek().length} services this week</span>
                </div>
                <div className="flex items-center gap-2">
                  <UserGroupIcon className="h-4 w-4 text-zinc-400" />
                  <span>45 team members scheduled</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="h-4 w-4 text-green-400" />
                  <span>12/15 confirmed</span>
                </div>
              </>
            )}
            
            {layoutMode === "planning" && selectedService && (
              <>
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-zinc-400" />
                  <span>Duration: 1h 30m</span>
                </div>
                <div className="flex items-center gap-2">
                  <UserGroupIcon className="h-4 w-4 text-zinc-400" />
                  <span>Team: 12/18 confirmed</span>
                </div>
                <div className="flex items-center gap-2">
                  <DocumentTextIcon className="h-4 w-4 text-zinc-400" />
                  <span>5 resources attached</span>
                </div>
              </>
            )}
            
            {layoutMode === "teams" && (
              <>
                <div className="flex items-center gap-2">
                  <UsersIcon className="h-4 w-4 text-zinc-400" />
                  <span>156 active volunteers</span>
                </div>
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-zinc-400" />
                  <span>23 with conflicts this month</span>
                </div>
                <div className="flex items-center gap-2">
                  <StarIcon className="h-4 w-4 text-amber-400" />
                  <span>4.8 avg satisfaction</span>
                </div>
              </>
            )}
            
            {layoutMode === "templates" && (
              <>
                <div className="flex items-center gap-2">
                  <FolderIcon className="h-4 w-4 text-zinc-400" />
                  <span>24 templates available</span>
                </div>
                <div className="flex items-center gap-2">
                  <ArrowPathIcon className="h-4 w-4 text-zinc-400" />
                  <span>Used 142 times</span>
                </div>
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-zinc-400" />
                  <span>Avg. 45min saved per service</span>
                </div>
              </>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            {/* Auto-save indicator */}
            {autoSave && (
              <div className="flex items-center gap-1 text-xs text-green-400">
                <CheckCircleIcon className="h-3 w-3" />
                <span>All changes saved</span>
              </div>
            )}
            
            {/* Connection status */}
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-xs text-green-400">Connected</span>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <AnimatePresence>
        {showCreateFlow && (
          <CreateServiceFlow 
            templateId={selectedTemplate}
            onClose={() => {
              setShowCreateFlow(false)
              setSelectedTemplate(null)
            }}
            onCreate={(serviceId) => {
              setSelectedService(serviceId)
              setLayoutMode("planning")
              setShowCreateFlow(false)
            }}
          />
        )}
        
        {showTeamAssignment && (
          <TeamAssignmentModal 
            serviceId={selectedService}
            onClose={() => setShowTeamAssignment(false)}
            onAssign={() => {
              setShowTeamAssignment(false)
              // Refresh team data
            }}
          />
        )}
      </AnimatePresence>

      {/* Floating Command Center */}
      <FloatingCommandCenter position="bottom-right" />
    </div>
  )
}

// Component: ScheduleView
const ScheduleView: React.FC<{
  viewMode: ViewMode
  selectedDate: Date
  onServiceClick: (service: Service) => void
  onCreateNew: () => void
  onDateChange: (date: Date) => void
}> = ({ viewMode, selectedDate, onServiceClick, onCreateNew, onDateChange }) => {
  const services = getServicesForWeek()
  
  return (
    <div className="h-full p-6">
      {viewMode === "week" ? (
        <div className="h-full">
          {/* Week View */}
          <div className="grid grid-cols-7 gap-4 h-full">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day, idx) => {
              const dayServices = services.filter(s => {
                // For demo purposes, map services to days
                const dayMap: {[key: string]: string[]} = {
                  "Sun": ["sun-8am", "sun-10am", "sun-6pm"],
                  "Wed": ["wed-7pm"],
                  "Fri": ["fri-7pm"]
                }
                return dayMap[day]?.includes(s.id) || false
              })
              
              return (
                <div key={day} className="flex flex-col">
                  <div className="text-center mb-4">
                    <p className="text-sm font-medium text-zinc-400">{day}</p>
                    <p className="text-2xl font-bold">{new Date(selectedDate.getTime() + idx * 24 * 60 * 60 * 1000).getDate()}</p>
                  </div>
                  
                  <div className="flex-1 space-y-2">
                    {dayServices.map((service) => (
                      <motion.div
                        key={service.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        whileHover={{ scale: 1.02 }}
                        onClick={() => onServiceClick(service)}
                        className={cn(
                          "p-3 rounded-lg border cursor-pointer transition-all",
                          "bg-[var(--bg-glass)] backdrop-blur-xl",
                          service.status === "confirmed" 
                            ? "border-green-500/30 hover:border-green-500/50"
                            : service.status === "planning"
                            ? "border-amber-500/30 hover:border-amber-500/50"
                            : "border-[var(--border-subtle)] hover:border-white/20"
                        )}
                      >
                        <p className="text-sm font-medium">{service.time}</p>
                        <p className="text-xs text-zinc-400 mt-1">{service.type}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <div className="flex -space-x-1">
                            {[...Array(Math.min(3, service.teamConfirmed))].map((_, i) => (
                              <div key={i} className="w-5 h-5 rounded-full bg-zinc-700 border border-black" />
                            ))}
                            {service.teamConfirmed > 3 && (
                              <div className="w-5 h-5 rounded-full bg-zinc-800 border border-black flex items-center justify-center">
                                <span className="text-xs">+{service.teamConfirmed - 3}</span>
                              </div>
                            )}
                          </div>
                          <Badge 
                            variant="secondary" 
                            size="sm"
                            className={cn(
                              service.status === "confirmed" && "bg-green-500/20 text-green-400",
                              service.status === "planning" && "bg-amber-500/20 text-amber-400",
                              service.status === "draft" && "bg-zinc-500/20 text-zinc-400"
                            )}
                          >
                            {service.teamConfirmed}/{service.teamSize}
                          </Badge>
                        </div>
                      </motion.div>
                    ))}
                    
                    {idx === 0 && dayServices.length < 3 && (
                      <button
                        onClick={onCreateNew}
                        className="p-3 border border-dashed border-[var(--border-subtle)] rounded-lg hover:border-[var(--color-primary)]/50 hover:bg-[var(--color-primary)]/5 transition-all flex items-center justify-center gap-2 group"
                      >
                        <PlusIcon className="h-4 w-4 text-zinc-400 group-hover:text-[var(--color-primary)]" />
                        <span className="text-xs text-zinc-400 group-hover:text-[var(--color-primary)]">Add Service</span>
                      </button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      ) : viewMode === "month" ? (
        <div className="h-full">
          {/* Month View */}
          <div className="grid grid-cols-7 gap-2 h-full">
            {/* Calendar grid implementation */}
            <p className="col-span-7 text-center text-zinc-400">Month view coming soon...</p>
          </div>
        </div>
      ) : (
        <div className="h-full">
          {/* List View */}
          <div className="max-w-4xl mx-auto space-y-2">
            {services.map((service) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                whileHover={{ x: 4 }}
                onClick={() => onServiceClick(service)}
                className="p-4 bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl hover:border-white/20 cursor-pointer transition-all"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <p className="text-sm font-medium text-zinc-400">{new Date(service.date).toLocaleDateString('en-US', { weekday: 'short' })}</p>
                      <p className="text-lg font-bold">{service.time}</p>
                    </div>
                    <div>
                      <p className="font-medium">{service.title}</p>
                      <p className="text-sm text-zinc-400">{service.type} • {service.duration}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-sm font-medium">{service.teamConfirmed}/{service.teamSize}</p>
                      <p className="text-xs text-zinc-400">Team Members</p>
                    </div>
                    <Badge 
                      variant="secondary"
                      className={cn(
                        service.status === "confirmed" && "bg-green-500/20 text-green-400",
                        service.status === "planning" && "bg-amber-500/20 text-amber-400",
                        service.status === "draft" && "bg-zinc-500/20 text-zinc-400"
                      )}
                    >
                      {service.status}
                    </Badge>
                    <ChevronRightIcon className="h-5 w-5 text-zinc-400" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Component: PlanningView
const PlanningView: React.FC<{
  serviceId: string | null
  onBack: () => void
  onTeamAssign: () => void
  onTemplateSelect: () => void
}> = ({ serviceId, onBack, onTeamAssign, onTemplateSelect }) => {
  const [serviceItems, setServiceItems] = useState<ServiceItem[]>([
    { id: "1", type: "song", title: "Opening Song", duration: 5, order: 1, assignedTo: ["Sarah Johnson"] },
    { id: "2", type: "prayer", title: "Opening Prayer", duration: 3, order: 2 },
    { id: "3", type: "song", title: "Worship Set", duration: 15, order: 3, assignedTo: ["Sarah Johnson", "Mike Davis", "Lisa Chen"] },
    { id: "4", type: "announcement", title: "Announcements", duration: 5, order: 4 },
    { id: "5", type: "message", title: "Message", duration: 35, order: 5, assignedTo: ["Pastor John"] },
    { id: "6", type: "song", title: "Response Song", duration: 5, order: 6 },
    { id: "7", type: "prayer", title: "Closing Prayer", duration: 2, order: 7 },
  ])
  
  const handleDragEnd = (result: any) => {
    if (!result.destination) return
    
    const items = Array.from(serviceItems)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)
    
    // Update order numbers
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index + 1
    }))
    
    setServiceItems(updatedItems)
  }
  
  const totalDuration = serviceItems.reduce((sum, item) => sum + item.duration, 0)
  
  if (!serviceId) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <CalendarDaysIcon className="h-12 w-12 text-zinc-400 mx-auto mb-4" />
          <p className="text-lg font-medium mb-2">No Service Selected</p>
          <p className="text-sm text-zinc-400">Select a service from the schedule to start planning</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="h-full flex">
      {/* Main Planning Area */}
      <div className="flex-1 p-6">
        <div className="max-w-4xl mx-auto">
          {/* Service Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <button
                  onClick={onBack}
                  className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>
                <div>
                  <h2 className="text-xl font-bold">Sunday 10:00 AM Service</h2>
                  <p className="text-sm text-zinc-400">March 24, 2025 • Contemporary Service</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  variant="glass" 
                  size="sm"
                  leftIcon={<DuplicateIcon className="h-4 w-4" />}
                  onClick={onTemplateSelect}
                >
                  Use Template
                </Button>
                <Button 
                  variant="glass" 
                  size="sm"
                  leftIcon={<UserGroupIcon className="h-4 w-4" />}
                  onClick={onTeamAssign}
                >
                  Assign Team
                </Button>
                <Button 
                  variant="primary" 
                  size="sm"
                  leftIcon={<CheckCircleIcon className="h-4 w-4" />}
                >
                  Publish
                </Button>
              </div>
            </div>
            
            {/* Service Stats */}
            <div className="grid grid-cols-4 gap-4">
              <GlassCard className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-zinc-400">Duration</p>
                    <p className="text-lg font-bold">{totalDuration} min</p>
                  </div>
                  <ClockIcon className="h-8 w-8 text-zinc-600" />
                </div>
              </GlassCard>
              <GlassCard className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-zinc-400">Team</p>
                    <p className="text-lg font-bold">12/18</p>
                  </div>
                  <UserGroupIcon className="h-8 w-8 text-zinc-600" />
                </div>
              </GlassCard>
              <GlassCard className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-zinc-400">Elements</p>
                    <p className="text-lg font-bold">{serviceItems.length}</p>
                  </div>
                  <ClipboardDocumentListIcon className="h-8 w-8 text-zinc-600" />
                </div>
              </GlassCard>
              <GlassCard className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-zinc-400">Status</p>
                    <p className="text-lg font-bold">Planning</p>
                  </div>
                  <Badge variant="secondary" className="bg-amber-500/20 text-amber-400">
                    In Progress
                  </Badge>
                </div>
              </GlassCard>
            </div>
          </div>
          
          {/* Service Timeline */}
          <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Service Flow</h3>
              <Button variant="glass" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Add Element
              </Button>
            </div>
            
            <div className="space-y-2">
              {serviceItems.map((item, index) => (
                <motion.div
                  key={item.id}
                  layout
                  drag="y"
                  dragConstraints={{ top: 0, bottom: 0 }}
                  dragElastic={0.2}
                  whileDrag={{ scale: 1.02, zIndex: 1 }}
                  className="group"
                >
                  <div className="flex items-center gap-3 p-4 bg-white/5 rounded-lg border border-[var(--border-subtle)] hover:border-white/20 transition-all">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity cursor-grab">
                      <Squares2X2Icon className="h-4 w-4 text-zinc-500" />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <span className="text-xs text-zinc-500 font-mono w-8">{index + 1}.</span>
                        <p className="font-medium">{item.title}</p>
                        <Badge variant="secondary" size="sm">
                          {item.type}
                        </Badge>
                        <span className="text-sm text-zinc-400">{item.duration} min</span>
                      </div>
                      {item.assignedTo && item.assignedTo.length > 0 && (
                        <div className="flex items-center gap-2 mt-2 ml-11">
                          <span className="text-xs text-zinc-400">Assigned to:</span>
                          {item.assignedTo.map((person) => (
                            <Badge key={person} variant="secondary" size="sm" className="bg-blue-500/20 text-blue-400">
                              {person}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button className="p-1 hover:bg-white/10 rounded">
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button className="p-1 hover:bg-white/10 rounded">
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* Right Sidebar - Resources & Notes */}
      <div className="w-80 border-l border-[var(--border-subtle)] p-6 space-y-6">
        {/* Team Overview */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
          <h3 className="text-sm font-medium mb-3">Team Overview</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-zinc-400">Confirmed</span>
              <span className="font-medium text-green-400">12</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-zinc-400">Pending</span>
              <span className="font-medium text-amber-400">4</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-zinc-400">Unassigned</span>
              <span className="font-medium text-red-400">2</span>
            </div>
          </div>
          <Button variant="glass" size="sm" className="w-full mt-3" onClick={onTeamAssign}>
            Manage Team
          </Button>
        </div>
        
        {/* Resources */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
          <h3 className="text-sm font-medium mb-3">Resources</h3>
          <div className="space-y-2">
            <div className="p-2 bg-white/5 rounded-lg flex items-center gap-2">
              <DocumentTextIcon className="h-4 w-4 text-zinc-400" />
              <span className="text-sm">Sermon Notes.pdf</span>
            </div>
            <div className="p-2 bg-white/5 rounded-lg flex items-center gap-2">
              <MusicalNoteIcon className="h-4 w-4 text-zinc-400" />
              <span className="text-sm">Chord Charts.zip</span>
            </div>
            <div className="p-2 bg-white/5 rounded-lg flex items-center gap-2">
              <DocumentTextIcon className="h-4 w-4 text-zinc-400" />
              <span className="text-sm">Announcements.docx</span>
            </div>
          </div>
          <Button variant="glass" size="sm" className="w-full mt-3" leftIcon={<PlusIcon className="h-4 w-4" />}>
            Add Resource
          </Button>
        </div>
        
        {/* Notes */}
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
          <h3 className="text-sm font-medium mb-3">Service Notes</h3>
          <textarea
            className="w-full h-32 p-3 bg-white/5 border border-[var(--border-subtle)] rounded-lg text-sm resize-none focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
            placeholder="Add notes for this service..."
            defaultValue="Remember to test new microphones before service starts. Guest speaker arriving at 9:30 AM."
          />
        </div>
      </div>
    </div>
  )
}

// Component: TeamsView
const TeamsView: React.FC<{
  onAssignTeam: (member: TeamMember, serviceId: string) => void
  onCreateTeam: () => void
}> = ({ onAssignTeam, onCreateTeam }) => {
  const [selectedRole, setSelectedRole] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  
  const teamMembers: TeamMember[] = [
    { id: "1", name: "Sarah Johnson", role: "Worship Leader", availability: "confirmed", lastServed: "Last Sunday", skills: ["Vocals", "Piano", "Leadership"] },
    { id: "2", name: "Mike Davis", role: "Keys", availability: "pending", lastServed: "2 weeks ago", skills: ["Piano", "Organ", "Synthesizer"] },
    { id: "3", name: "Lisa Chen", role: "Vocals", availability: "confirmed", lastServed: "Last Sunday", skills: ["Vocals", "Harmony"] },
    { id: "4", name: "Tom Wilson", role: "Guitar", availability: "declined", conflicts: ["Out of town"], lastServed: "3 weeks ago", skills: ["Electric Guitar", "Acoustic Guitar", "Bass"] },
    { id: "5", name: "Emily Brown", role: "Drums", availability: "confirmed", lastServed: "Last Sunday", skills: ["Drums", "Percussion"] },
    { id: "6", name: "James Miller", role: "Sound", availability: "unavailable", lastServed: "4 weeks ago", skills: ["Sound Engineering", "Mixing", "Recording"] },
    { id: "7", name: "Anna Taylor", role: "Slides", availability: "confirmed", lastServed: "Last Sunday", skills: ["ProPresenter", "Graphics", "Video"] },
    { id: "8", name: "David Lee", role: "Bass", availability: "pending", lastServed: "2 weeks ago", skills: ["Bass Guitar", "Music Theory"] },
  ]
  
  const roles = [...new Set(teamMembers.map(m => m.role))]
  
  const filteredMembers = teamMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.skills?.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesRole = !selectedRole || member.role === selectedRole
    return matchesSearch && matchesRole
  })
  
  return (
    <div className="h-full flex">
      {/* Main Team Grid */}
      <div className="flex-1 p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold">Team Management</h2>
            <div className="flex items-center gap-2">
              <Button variant="glass" size="sm" leftIcon={<BellIcon className="h-4 w-4" />}>
                Send Reminders
              </Button>
              <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />} onClick={onCreateTeam}>
                Add Member
              </Button>
            </div>
          </div>
          
          {/* Filters */}
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search by name, role, or skill..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
              />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-zinc-400">Filter by role:</span>
              <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
                <button
                  onClick={() => setSelectedRole(null)}
                  className={cn(
                    "px-3 py-1.5 rounded text-sm transition-colors",
                    !selectedRole ? "bg-[var(--color-primary)] text-black" : "hover:bg-white/10"
                  )}
                >
                  All
                </button>
                {roles.map(role => (
                  <button
                    key={role}
                    onClick={() => setSelectedRole(role)}
                    className={cn(
                      "px-3 py-1.5 rounded text-sm transition-colors",
                      selectedRole === role ? "bg-[var(--color-primary)] text-black" : "hover:bg-white/10"
                    )}
                  >
                    {role}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Team Grid */}
        <div className="grid grid-cols-3 gap-4">
          {filteredMembers.map((member) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              whileHover={{ y: -4 }}
              className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-6 hover:border-white/20 transition-all cursor-pointer"
              onClick={() => onAssignTeam(member, "current-service")}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-lg font-bold">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <p className="font-medium">{member.name}</p>
                    <p className="text-sm text-zinc-400">{member.role}</p>
                  </div>
                </div>
                <Badge 
                  variant="secondary" 
                  size="sm"
                  className={cn(
                    member.availability === "confirmed" && "bg-green-500/20 text-green-400",
                    member.availability === "pending" && "bg-amber-500/20 text-amber-400",
                    member.availability === "declined" && "bg-red-500/20 text-red-400",
                    member.availability === "unavailable" && "bg-zinc-500/20 text-zinc-400"
                  )}
                >
                  {member.availability}
                </Badge>
              </div>
              
              {member.skills && (
                <div className="mb-3">
                  <p className="text-xs text-zinc-400 mb-1">Skills</p>
                  <div className="flex flex-wrap gap-1">
                    {member.skills.map(skill => (
                      <Badge key={skill} variant="secondary" size="sm" className="bg-blue-500/20 text-blue-400">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="space-y-1 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-zinc-400">Last served</span>
                  <span>{member.lastServed}</span>
                </div>
                {member.conflicts && (
                  <div className="flex items-center justify-between">
                    <span className="text-zinc-400">Conflict</span>
                    <span className="text-red-400">{member.conflicts[0]}</span>
                  </div>
                )}
              </div>
              
              <div className="mt-4 flex items-center gap-2">
                <Button variant="glass" size="sm" className="flex-1">
                  View Schedule
                </Button>
                <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
                  <EllipsisVerticalIcon className="h-4 w-4" />
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
      
      {/* Right Sidebar - Team Stats */}
      <div className="w-80 border-l border-[var(--border-subtle)] p-6 space-y-6">
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
          <h3 className="text-sm font-medium mb-3">Team Statistics</h3>
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-zinc-400">Total Members</span>
                <span className="text-sm font-medium">156</span>
              </div>
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <div className="h-full w-full bg-[var(--color-primary)]" />
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-zinc-400">Available This Week</span>
                <span className="text-sm font-medium">124</span>
              </div>
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <div className="h-full w-4/5 bg-green-500" />
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-zinc-400">With Conflicts</span>
                <span className="text-sm font-medium">23</span>
              </div>
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <div className="h-full w-1/6 bg-amber-500" />
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
          <h3 className="text-sm font-medium mb-3">Upcoming Needs</h3>
          <div className="space-y-2">
            <div className="p-2 bg-red-500/10 border border-red-500/30 rounded-lg">
              <p className="text-sm font-medium">Sunday 8:00 AM</p>
              <p className="text-xs text-red-400">Need: Sound Tech, Vocalist</p>
            </div>
            <div className="p-2 bg-amber-500/10 border border-amber-500/30 rounded-lg">
              <p className="text-sm font-medium">Wednesday 7:00 PM</p>
              <p className="text-xs text-amber-400">Need: Drummer</p>
            </div>
          </div>
        </div>
        
        <div className="bg-[var(--bg-glass)] backdrop-blur-xl border border-[var(--border-subtle)] rounded-xl p-4">
          <h3 className="text-sm font-medium mb-3">Quick Actions</h3>
          <div className="space-y-2">
            <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<CalendarIcon className="h-4 w-4" />}>
              View Full Schedule
            </Button>
            <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
              Manage Roles
            </Button>
            <Button variant="glass" size="sm" className="w-full justify-start" leftIcon={<DocumentTextIcon className="h-4 w-4" />}>
              Export Report
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Component: TemplatesView
const TemplatesView: React.FC<{
  selectedTemplate: string | null
  onTemplateSelect: (id: string) => void
  onUseTemplate: (id: string) => void
  onBack: () => void
}> = ({ selectedTemplate, onTemplateSelect, onUseTemplate, onBack }) => {
  const templates: ServiceTemplate[] = [
    {
      id: "1",
      name: "Contemporary Sunday Morning",
      description: "High-energy contemporary worship with full band",
      type: "contemporary",
      items: [],
      defaultTeamSize: 18,
      usageCount: 42,
      tags: ["sunday", "contemporary", "full-band"]
    },
    {
      id: "2",
      name: "Traditional Service",
      description: "Traditional hymns with organ and choir",
      type: "traditional",
      items: [],
      defaultTeamSize: 12,
      usageCount: 38,
      tags: ["traditional", "hymns", "choir"]
    },
    {
      id: "3",
      name: "Youth Night",
      description: "Energetic worship designed for youth ministry",
      type: "youth",
      items: [],
      defaultTeamSize: 10,
      usageCount: 24,
      tags: ["youth", "friday", "contemporary"]
    },
    {
      id: "4",
      name: "Midweek Prayer",
      description: "Intimate midweek prayer and worship",
      type: "midweek",
      items: [],
      defaultTeamSize: 8,
      usageCount: 52,
      tags: ["wednesday", "prayer", "intimate"]
    },
    {
      id: "5",
      name: "Christmas Eve",
      description: "Special Christmas Eve candlelight service",
      type: "special",
      items: [],
      defaultTeamSize: 25,
      usageCount: 4,
      tags: ["christmas", "special", "candlelight"]
    },
    {
      id: "6",
      name: "Easter Sunrise",
      description: "Early morning Easter celebration",
      type: "special",
      items: [],
      defaultTeamSize: 20,
      usageCount: 3,
      tags: ["easter", "special", "outdoor"]
    }
  ]
  
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedTag, setSelectedTag] = useState<string | null>(null)
  
  const allTags = [...new Set(templates.flatMap(t => t.tags))]
  
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesTag = !selectedTag || template.tags.includes(selectedTag)
    return matchesSearch && matchesTag
  })
  
  return (
    <div className="h-full p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <button
                onClick={onBack}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <ChevronLeftIcon className="h-5 w-5" />
              </button>
              <h2 className="text-xl font-bold">Service Templates</h2>
            </div>
            <Button variant="primary" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
              Create Template
            </Button>
          </div>
          
          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
              />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-zinc-400">Filter:</span>
              <div className="flex items-center gap-1">
                <Badge 
                  variant={!selectedTag ? "primary" : "secondary"} 
                  size="sm" 
                  className="cursor-pointer"
                  onClick={() => setSelectedTag(null)}
                >
                  All
                </Badge>
                {allTags.map(tag => (
                  <Badge 
                    key={tag}
                    variant={selectedTag === tag ? "primary" : "secondary"} 
                    size="sm" 
                    className="cursor-pointer"
                    onClick={() => setSelectedTag(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Templates Grid */}
        <div className="grid grid-cols-3 gap-4">
          {filteredTemplates.map((template) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -4 }}
              onClick={() => onTemplateSelect(template.id)}
              className={cn(
                "bg-[var(--bg-glass)] backdrop-blur-xl border rounded-xl p-6 cursor-pointer transition-all",
                selectedTemplate === template.id 
                  ? "border-[var(--color-primary)] shadow-glow" 
                  : "border-[var(--border-subtle)] hover:border-white/20"
              )}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="font-medium text-lg mb-1">{template.name}</h3>
                  <p className="text-sm text-zinc-400">{template.description}</p>
                </div>
                {template.usageCount > 30 && (
                  <Badge variant="secondary" size="sm" className="bg-green-500/20 text-green-400">
                    Popular
                  </Badge>
                )}
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-zinc-400">Team Size</span>
                  <span className="font-medium">{template.defaultTeamSize} people</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-zinc-400">Used</span>
                  <span className="font-medium">{template.usageCount} times</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {template.tags.map(tag => (
                    <Badge key={tag} variant="secondary" size="sm">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
              
              {selectedTemplate === template.id && (
                <div className="mt-4 flex items-center gap-2">
                  <Button 
                    variant="primary" 
                    size="sm" 
                    className="flex-1"
                    onClick={(e) => {
                      e.stopPropagation()
                      onUseTemplate(template.id)
                    }}
                  >
                    Use Template
                  </Button>
                  <Button variant="glass" size="sm">
                    Preview
                  </Button>
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Component: CreateServiceFlow
const CreateServiceFlow: React.FC<{
  templateId: string | null
  onClose: () => void
  onCreate: (serviceId: string) => void
}> = ({ templateId, onClose, onCreate }) => {
  const [step, setStep] = useState(1)
  const [serviceData, setServiceData] = useState({
    title: "",
    date: "",
    time: "",
    type: "contemporary",
    location: "Main Sanctuary",
    expectedAttendance: 200,
    template: templateId
  })
  
  const handleCreate = () => {
    // Create service and return ID
    onCreate("new-service-id")
  }
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-6"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="w-full max-w-2xl bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)] rounded-2xl shadow-glow-2xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="h-16 bg-[var(--bg-glass)] border-b border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <h2 className="text-xl font-bold">Create New Service</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/10 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            {[1, 2, 3].map((s) => (
              <React.Fragment key={s}>
                <div
                  className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center font-medium transition-colors",
                    step >= s 
                      ? "bg-[var(--color-primary)] text-black" 
                      : "bg-white/10 text-zinc-400"
                  )}
                >
                  {s}
                </div>
                {s < 3 && (
                  <div className={cn(
                    "w-24 h-0.5 transition-colors",
                    step > s ? "bg-[var(--color-primary)]" : "bg-white/10"
                  )} />
                )}
              </React.Fragment>
            ))}
          </div>
          
          {/* Step Content */}
          {step === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium mb-4">Basic Information</h3>
              <div>
                <label className="block text-sm font-medium mb-2">Service Title</label>
                <input
                  type="text"
                  value={serviceData.title}
                  onChange={(e) => setServiceData({...serviceData, title: e.target.value})}
                  className="w-full px-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                  placeholder="e.g., Sunday Morning Worship"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Date</label>
                  <input
                    type="date"
                    value={serviceData.date}
                    onChange={(e) => setServiceData({...serviceData, date: e.target.value})}
                    className="w-full px-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Time</label>
                  <input
                    type="time"
                    value={serviceData.time}
                    onChange={(e) => setServiceData({...serviceData, time: e.target.value})}
                    className="w-full px-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Service Type</label>
                <select
                  value={serviceData.type}
                  onChange={(e) => setServiceData({...serviceData, type: e.target.value})}
                  className="w-full px-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                >
                  <option value="contemporary">Contemporary</option>
                  <option value="traditional">Traditional</option>
                  <option value="youth">Youth</option>
                  <option value="midweek">Midweek</option>
                  <option value="special">Special Event</option>
                </select>
              </div>
            </div>
          )}
          
          {step === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium mb-4">Service Details</h3>
              <div>
                <label className="block text-sm font-medium mb-2">Location</label>
                <select
                  value={serviceData.location}
                  onChange={(e) => setServiceData({...serviceData, location: e.target.value})}
                  className="w-full px-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                >
                  <option value="Main Sanctuary">Main Sanctuary</option>
                  <option value="Chapel">Chapel</option>
                  <option value="Youth Room">Youth Room</option>
                  <option value="Online Only">Online Only</option>
                  <option value="Outdoor">Outdoor</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Expected Attendance</label>
                <input
                  type="number"
                  value={serviceData.expectedAttendance}
                  onChange={(e) => setServiceData({...serviceData, expectedAttendance: parseInt(e.target.value)})}
                  className="w-full px-4 py-2 bg-white/5 border border-[var(--border-subtle)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/20"
                />
              </div>
              {templateId && (
                <div className="p-4 bg-[var(--color-primary)]/10 border border-[var(--color-primary)]/30 rounded-lg">
                  <p className="text-sm">Using template: <strong>Contemporary Sunday Morning</strong></p>
                  <p className="text-xs text-zinc-400 mt-1">Service flow and team assignments will be pre-filled</p>
                </div>
              )}
            </div>
          )}
          
          {step === 3 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium mb-4">Review & Create</h3>
              <div className="bg-white/5 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-zinc-400">Title</span>
                  <span className="font-medium">{serviceData.title || "Untitled Service"}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-zinc-400">Date & Time</span>
                  <span className="font-medium">{serviceData.date} at {serviceData.time}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-zinc-400">Type</span>
                  <Badge variant="secondary">{serviceData.type}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-zinc-400">Location</span>
                  <span className="font-medium">{serviceData.location}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-zinc-400">Expected Attendance</span>
                  <span className="font-medium">{serviceData.expectedAttendance}</span>
                </div>
              </div>
              
              <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                <p className="text-sm text-green-400">✓ Service will be created in draft mode</p>
                <p className="text-sm text-green-400">✓ You can assign teams and resources after creation</p>
                <p className="text-sm text-green-400">✓ Service can be published when ready</p>
              </div>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="h-16 bg-[var(--bg-glass)] border-t border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <Button 
            variant="glass" 
            onClick={() => step > 1 ? setStep(step - 1) : onClose()}
          >
            {step === 1 ? "Cancel" : "Back"}
          </Button>
          <div className="flex items-center gap-2">
            {step < 3 ? (
              <Button 
                variant="primary" 
                onClick={() => setStep(step + 1)}
                disabled={step === 1 && !serviceData.title}
              >
                Next
              </Button>
            ) : (
              <Button 
                variant="primary" 
                onClick={handleCreate}
                leftIcon={<CheckCircleIcon className="h-4 w-4" />}
              >
                Create Service
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

// Component: TeamAssignmentModal
const TeamAssignmentModal: React.FC<{
  serviceId: string | null
  onClose: () => void
  onAssign: () => void
}> = ({ serviceId, onClose, onAssign }) => {
  const [assignedMembers, setAssignedMembers] = useState<{[role: string]: TeamMember[]}>({
    "Worship Leader": [],
    "Vocals": [],
    "Keys": [],
    "Guitar": [],
    "Bass": [],
    "Drums": [],
    "Sound": [],
    "Slides": [],
    "Video": [],
    "Ushers": [],
    "Greeting": []
  })
  
  const availableMembers: TeamMember[] = [
    { id: "1", name: "Sarah Johnson", role: "Worship Leader", availability: "confirmed", skills: ["Vocals", "Piano", "Leadership"] },
    { id: "2", name: "Mike Davis", role: "Keys", availability: "confirmed", skills: ["Piano", "Organ"] },
    { id: "3", name: "Lisa Chen", role: "Vocals", availability: "confirmed", skills: ["Vocals", "Harmony"] },
    { id: "4", name: "Tom Wilson", role: "Guitar", availability: "confirmed", skills: ["Electric Guitar", "Acoustic Guitar"] },
    { id: "5", name: "Emily Brown", role: "Drums", availability: "confirmed", skills: ["Drums", "Percussion"] },
  ]
  
  const handleDragEnd = (result: any) => {
    if (!result.destination) return
    
    const sourceRole = result.source.droppableId
    const destRole = result.destination.droppableId
    
    if (sourceRole === "available") {
      // Adding to a role
      const member = availableMembers.find(m => m.id === result.draggableId)
      if (member) {
        setAssignedMembers({
          ...assignedMembers,
          [destRole]: [...(assignedMembers[destRole] || []), member]
        })
      }
    } else if (destRole === "available") {
      // Removing from a role
      setAssignedMembers({
        ...assignedMembers,
        [sourceRole]: assignedMembers[sourceRole].filter(m => m.id !== result.draggableId)
      })
    } else {
      // Moving between roles
      const member = assignedMembers[sourceRole].find(m => m.id === result.draggableId)
      if (member) {
        setAssignedMembers({
          ...assignedMembers,
          [sourceRole]: assignedMembers[sourceRole].filter(m => m.id !== result.draggableId),
          [destRole]: [...(assignedMembers[destRole] || []), member]
        })
      }
    }
  }
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-6"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="w-full max-w-6xl h-[80vh] bg-[var(--bg-glass)] backdrop-blur-2xl border border-[var(--border-subtle)] rounded-2xl shadow-glow-2xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="h-16 bg-[var(--bg-glass)] border-b border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <h2 className="text-xl font-bold">Assign Team Members</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/10 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="h-[calc(100%-8rem)] p-6">
          <div className="h-full flex gap-6">
            {/* Available Members */}
            <div className="w-80 bg-white/5 rounded-xl p-4">
              <h3 className="text-sm font-medium mb-3">Available Team Members</h3>
              <div className="space-y-2">
                {availableMembers.map((member) => (
                  <motion.div
                    key={member.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="p-3 bg-white/5 rounded-lg cursor-pointer hover:bg-white/10 transition-colors"
                    onClick={() => {
                      // Simple click to assign logic
                      console.log("Assign member:", member)
                    }}
                  >
                    <p className="font-medium text-sm">{member.name}</p>
                    <p className="text-xs text-zinc-400">{member.skills?.join(", ")}</p>
                  </motion.div>
                ))}
              </div>
            </div>
            
            {/* Role Assignments */}
            <div className="flex-1 grid grid-cols-3 gap-4">
              {Object.entries(assignedMembers).map(([role, members]) => (
                <div key={role} className="bg-white/5 rounded-xl p-4">
                  <h4 className="text-sm font-medium mb-3">{role}</h4>
                  <div className="min-h-[100px] space-y-2">
                    {members.map((member) => (
                      <motion.div
                        key={member.id}
                        whileHover={{ scale: 1.02 }}
                        className="p-2 bg-[var(--color-primary)]/20 rounded cursor-pointer text-sm"
                      >
                        {member.name}
                      </motion.div>
                    ))}
                    {members.length === 0 && (
                      <div className="h-20 border-2 border-dashed border-[var(--border-subtle)] rounded-lg flex items-center justify-center">
                        <span className="text-xs text-zinc-500">Click member to assign</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="h-16 bg-[var(--bg-glass)] border-t border-[var(--border-subtle)] px-6 flex items-center justify-between">
          <div className="text-sm">
            <span className="text-zinc-400">Assigned: </span>
            <span className="font-medium">{Object.values(assignedMembers).flat().length} members</span>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="glass" onClick={onClose}>Cancel</Button>
            <Button variant="primary" onClick={onAssign}>Save Assignments</Button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

