"use client"

import {
  ArrowLeftIcon,
  PlusIcon,
  MusicalNoteIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentTextIcon,
} from "@heroicons/react/16/solid"
import { Clock, Edit, Trash2, Play, Plus } from "lucide-react"
import ModuleLayout from "@/components/ui/module-layout"
import { Button } from "@/components/ui/button"
import GlassTooltip from "@/components/ui/glass-tooltip"
import { Card } from "@/components/ui/card"
import QuickActions from "@/components/ui/quick-actions"
import Link from "next/link"
import { Input } from "@/components/ui/input"

export default function SongLibraryPage() {
  const songLibraryHelpContent = (
    <div>
      <p>The Song Library helps you manage all your worship songs in one place.</p>
      <ul className="mt-2 space-y-1">
        <li>• Store song details, lyrics, and chord charts</li>
        <li>• Track song usage history and CCLI reporting</li>
        <li>• Organize songs by theme, key, and tempo</li>
        <li>• Easily add songs to service plans</li>
      </ul>
      <p className="mt-2">Use the filters to quickly find the perfect songs for your service.</p>
    </div>
  )

  const quickActions = [
    {
      id: "add-song",
      icon: <PlusIcon className="h-5 w-5" />,
      label: "Add Song",
      onClick: () => console.log("Add song"),
      color: "bg-blue-500",
    },
    {
      id: "import-songs",
      icon: <MusicalNoteIcon className="h-5 w-5" />,
      label: "Import Songs",
      onClick: () => console.log("Import songs"),
      color: "bg-purple-500",
    },
    {
      id: "ccli-report",
      icon: <DocumentTextIcon className="h-5 w-5" />,
      label: "CCLI Report",
      onClick: () => console.log("CCLI report"),
      color: "bg-green-500",
    },
  ]

  return (
    <ModuleLayout
      title="Song Library"
      description="Manage your worship songs and arrangements"
      icon={<MusicalNoteIcon className="h-6 w-6 text-zinc-400" />}
      searchPlaceholder="Search songs..."
      actions={
        <div className="flex gap-3">
          <Button variant="outline" size="md" leftIcon={<ArrowLeftIcon className="h-4 w-4" />} asChild>
            <Link href="/services">Back to Services</Link>
          </Button>
          <Button variant="glass" size="md" leftIcon={<PlusIcon className="h-4 w-4" />}>
            Add Song
          </Button>
        </div>
      }
      quickActions={<QuickActions actions={quickActions} />}
    >
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters */}
        <div className="space-y-6">
          <Card variant="glass" padding="md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Filters</h3>
              <Button variant="outline" size="sm">
                Reset
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm text-zinc-400 block mb-1">Search</label>
                <div className="relative">
                  <Input placeholder="Search songs..." className="pl-9 bg-black/20 border-white/5" />
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-zinc-400" />
                </div>
              </div>

              <div>
                <label className="text-sm text-zinc-400 block mb-1">Song Theme</label>
                <select className="w-full bg-black/20 border border-white/5 rounded-lg p-2 text-sm">
                  <option>All Themes</option>
                  <option>Praise & Worship</option>
                  <option>Grace & Salvation</option>
                  <option>God's Love</option>
                  <option>Faith & Trust</option>
                  <option>Communion</option>
                  <option>Prayer</option>
                </select>
              </div>

              <div>
                <label className="text-sm text-zinc-400 block mb-1">Key</label>
                <div className="grid grid-cols-4 gap-2">
                  {["C", "D", "E", "F", "G", "A", "B", "Bb"].map((key) => (
                    <Button key={key} variant="outline" size="sm" className="rounded-lg text-xs py-1">
                      {key}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm text-zinc-400 block mb-1">Tempo Range</label>
                <div className="flex items-center gap-2">
                  <Input type="number" placeholder="Min" className="w-full bg-black/20 border-white/5" />
                  <span className="text-zinc-400">-</span>
                  <Input type="number" placeholder="Max" className="w-full bg-black/20 border-white/5" />
                  <span className="text-xs text-zinc-400">BPM</span>
                </div>
              </div>

              <div>
                <label className="text-sm text-zinc-400 block mb-1">Last Used</label>
                <select className="w-full bg-black/20 border border-white/5 rounded-lg p-2 text-sm">
                  <option>Any time</option>
                  <option>Last week</option>
                  <option>Last month</option>
                  <option>Last 3 months</option>
                  <option>Last 6 months</option>
                  <option>Last year</option>
                  <option>Never used</option>
                </select>
              </div>

              <Button
                variant="outline"
                size="sm"
                className="w-full rounded-xl"
                leftIcon={<FunnelIcon className="h-4 w-4" />}
              >
                Apply Filters
              </Button>
            </div>
          </Card>

          <Card variant="glass" padding="md">
            <h3 className="text-lg font-semibold mb-4">Song Stats</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Total Songs</span>
                <span className="text-lg font-bold">124</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Used This Month</span>
                <span className="text-lg font-bold">18</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Recently Added</span>
                <span className="text-lg font-bold">5</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">CCLI Reporting</span>
                <span className="px-2 py-0.5 bg-green-500/20 text-green-400 rounded-full text-xs">Up to date</span>
              </div>
            </div>
          </Card>
        </div>

        {/* Song List */}
        <div className="lg:col-span-3">
          <Card variant="glass" padding="lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Song Library</h2>
              <div className="flex gap-2">
                <select className="bg-black/20 border border-white/5 rounded-lg p-1.5 text-xs">
                  <option>Sort by Title</option>
                  <option>Sort by Last Used</option>
                  <option>Sort by Most Used</option>
                  <option>Sort by Key</option>
                  <option>Sort by Tempo</option>
                </select>
                <Button variant="outline" size="sm" className="rounded-xl text-xs">
                  Export
                </Button>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-black/30 rounded-xl text-sm">
                <div className="w-12"></div>
                <div className="flex-1 font-medium">Song Title</div>
                <div className="w-32 text-center">Key</div>
                <div className="w-32 text-center">Tempo</div>
                <div className="w-32 text-center">Last Used</div>
                <div className="w-32 text-center">Actions</div>
              </div>

              {[
                {
                  title: "Amazing Grace (My Chains Are Gone)",
                  artist: "Chris Tomlin",
                  key: "G",
                  tempo: "72 BPM",
                  lastUsed: "2 weeks ago",
                },
                {
                  title: "How Great Is Our God",
                  artist: "Chris Tomlin",
                  key: "C",
                  tempo: "78 BPM",
                  lastUsed: "1 month ago",
                },
                {
                  title: "10,000 Reasons (Bless the Lord)",
                  artist: "Matt Redman",
                  key: "D",
                  tempo: "73 BPM",
                  lastUsed: "3 weeks ago",
                },
                {
                  title: "What A Beautiful Name",
                  artist: "Hillsong Worship",
                  key: "D",
                  tempo: "68 BPM",
                  lastUsed: "Last Sunday",
                },
                {
                  title: "Great Are You Lord",
                  artist: "All Sons & Daughters",
                  key: "G",
                  tempo: "72 BPM",
                  lastUsed: "Last Sunday",
                },
                {
                  title: "Cornerstone",
                  artist: "Hillsong Worship",
                  key: "Bb",
                  tempo: "70 BPM",
                  lastUsed: "Last Sunday",
                },
                { title: "Build My Life", artist: "Housefires", key: "D", tempo: "72 BPM", lastUsed: "Last Sunday" },
                { title: "It Is Well", artist: "Bethel Music", key: "D", tempo: "68 BPM", lastUsed: "Last Sunday" },
                {
                  title: "Goodness of God",
                  artist: "Bethel Music",
                  key: "C",
                  tempo: "63 BPM",
                  lastUsed: "2 months ago",
                },
                {
                  title: "Raise A Hallelujah",
                  artist: "Bethel Music",
                  key: "G",
                  tempo: "82 BPM",
                  lastUsed: "3 months ago",
                },
              ].map((song, index) => (
                <SongRow
                  key={index}
                  title={song.title}
                  artist={song.artist}
                  songKey={song.key}
                  tempo={song.tempo}
                  lastUsed={song.lastUsed}
                />
              ))}

              <div className="flex justify-between items-center pt-3">
                <Button variant="outline" size="sm" className="rounded-xl">
                  Previous
                </Button>
                <div className="text-sm">
                  Page <span className="font-medium">1</span> of <span className="font-medium">13</span>
                </div>
                <Button variant="outline" size="sm" className="rounded-xl">
                  Next
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </ModuleLayout>
  )
}

function SongRow({
  title,
  artist,
  songKey,
  tempo,
  lastUsed,
}: {
  title: string
  artist: string
  songKey: string
  tempo: string
  lastUsed: string
}) {
  return (
    <div className="flex items-center p-3 bg-black/20 hover:bg-black/30 rounded-xl transition-colors">
      <div className="w-12 flex justify-center">
        <GlassTooltip content="Preview Song" position="top">
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
            <Play className="h-4 w-4" />
          </Button>
        </GlassTooltip>
      </div>
      <div className="flex-1">
        <div className="font-medium">{title}</div>
        <div className="text-xs text-zinc-400">{artist}</div>
      </div>
      <div className="w-32 text-center">
        <span className="px-2 py-1 bg-black/30 rounded-md text-sm">{songKey}</span>
      </div>
      <div className="w-32 text-center flex items-center justify-center">
        <Clock className="h-3.5 w-3.5 mr-1.5 text-zinc-400" />
        <span>{tempo}</span>
      </div>
      <div className="w-32 text-center text-zinc-400">{lastUsed}</div>
      <div className="w-32 flex justify-center space-x-1">
        <GlassTooltip content="Add to Service" position="top">
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-lg">
            <Plus className="h-4 w-4" />
          </Button>
        </GlassTooltip>
        <GlassTooltip content="Edit Song" position="top">
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-lg">
            <Edit className="h-4 w-4" />
          </Button>
        </GlassTooltip>
        <GlassTooltip content="Delete Song" position="top">
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-lg text-red-400">
            <Trash2 className="h-4 w-4" />
          </Button>
        </GlassTooltip>
      </div>
    </div>
  )
}
