"use client"

import {
  PlusIcon,
  ChartBarIcon,
  CalendarIcon,
  UserGroupIcon,
  HeartIcon,
  ArrowRightIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
} from "@heroicons/react/16/solid"
import { ModuleLayout } from "@/components/ui/module-layout"
import { Card } from "@/components/ui/card"
import { GlassCard } from "@/components/ui/glass-card"
import { GlassModal } from "@/components/ui/glass-modal"
import { GlassSheet } from "@/components/ui/glass-sheet"
import DataCard from "@/components/ui/data-card"
import MinistryCard from "@/components/ministry-card"
import TaskCard from "@/components/task-card"
import UpcomingEvents from "@/components/upcoming-events"
import AttendanceSnapshot from "@/components/attendance-snapshot"
import { Button } from "@/components/ui/button"
import QuickActions from "@/components/ui/quick-actions"
import Link from "next/link"
import { useState } from "react"
import GlassTooltip from "@/components/ui/glass-tooltip"

export default function Home() {
  const [isServiceModalOpen, setIsServiceModalOpen] = useState(false)
  const [isPeopleSheetOpen, setIsPeopleSheetOpen] = useState(false)
  const [isEventsModalOpen, setIsEventsModalOpen] = useState(false)
  const [isGivingSheetOpen, setIsGivingSheetOpen] = useState(false)

  const quickActions = [
    {
      id: "new-service",
      icon: <PlusIcon className="h-5 w-5" />,
      label: "New Service",
      onClick: () => setIsServiceModalOpen(true),
      color: "bg-blue-500",
    },
    {
      id: "add-person",
      icon: <UserGroupIcon className="h-5 w-5" />,
      label: "Add Person",
      onClick: () => setIsPeopleSheetOpen(true),
      color: "bg-green-500",
    },
    {
      id: "schedule-event",
      icon: <CalendarIcon className="h-5 w-5" />,
      label: "Schedule Event", 
      onClick: () => setIsEventsModalOpen(true),
      color: "bg-purple-500",
    },
    {
      id: "record-giving",
      icon: <CurrencyDollarIcon className="h-5 w-5" />,
      label: "Record Giving",
      onClick: () => setIsGivingSheetOpen(true),
      color: "bg-amber-500",
    },
  ]

  return (
    <ModuleLayout
      title="Church Dashboard"
      subtitle="Welcome back! Here's an overview of your church's activities."
      icon={<ChartBarIcon className="h-6 w-6 text-zinc-400" />}
      actions={
        <div className="flex gap-3">
          <GlassTooltip content="Filter by date range" side="bottom">
            <Button variant="outline" size="md">
              Last 30 Days
            </Button>
          </GlassTooltip>
          <GlassTooltip content="Create new content" side="bottom">
            <Button variant="glass" size="md" leftIcon={<PlusIcon className="h-4 w-4" />}>
              Create
            </Button>
          </GlassTooltip>
        </div>
      }
      quickActions={<QuickActions actions={quickActions} />}
    >
      <div className="space-y-8 p-6"> {/* Added wrapper div with padding and spacing */}
        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Link href="/services/new" className="contents">
            <Card
              className="hover-lift hover:bg-gradient-to-br hover:from-blue-900/20 hover:to-blue-800/5 group animate-fade-in-up"
              variant="glass"
              padding="lg"
            >
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-blue-500/10 border border-blue-500/20 group-hover:bg-blue-500/20">
                <CalendarIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-1 group-hover:text-blue-400">Schedule Service</h3>
                <p className="text-sm text-zinc-400 mb-3">Create a new service plan</p>
                <div className="flex items-center text-sm text-blue-400 font-medium">
                  <span>Get Started</span>
                  <ArrowRightIcon className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </div>
          </Card>
        </Link>

          <Link href="/people" className="contents">
            <Card
              className="hover-lift hover:bg-gradient-to-br hover:from-green-900/20 hover:to-green-800/5 group animate-fade-in-up animation-delay-100"
              variant="glass"
              padding="lg"
            >
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-green-500/10 border border-green-500/20 group-hover:bg-green-500/20">
                <UserGroupIcon className="h-6 w-6 text-green-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-1 group-hover:text-green-400">Manage People</h3>
                <p className="text-sm text-zinc-400 mb-3">View and manage church members</p>
                <div className="flex items-center text-sm text-green-400 font-medium">
                  <span>View Directory</span>
                  <ArrowRightIcon className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </div>
          </Card>
        </Link>

          <GlassTooltip content="Manage church events and ministry activities">
            <Card
              className="hover-lift hover:bg-gradient-to-br hover:from-purple-900/20 hover:to-purple-800/5 group animate-fade-in-up animation-delay-200 cursor-pointer"
              variant="glass"
              padding="lg"
              onClick={() => setIsEventsModalOpen(true)}
            >
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-purple-500/10 border border-purple-500/20 group-hover:bg-purple-500/20">
                  <HeartIcon className="h-6 w-6 text-purple-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-1 group-hover:text-purple-400">Ministry Overview</h3>
                  <p className="text-sm text-zinc-400 mb-3">View all ministry activities</p>
                  <div className="flex items-center text-sm text-purple-400 font-medium">
                    <span>View Ministries</span>
                    <ArrowRightIcon className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </Card>
          </GlassTooltip>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <DataCard
            title="Total Attendance"
          value="1,248"
          icon={<UserGroupIcon className="h-6 w-6 text-zinc-400" />}
          trend={{ value: 12, isPositive: true, label: "vs last month" }}
          variant="glass"
          badgeText="Growing"
          badgeColor="success"
          className="animate-fade-in-up"
        />
        <DataCard
          title="New Members"
          value="36"
          icon={<PlusIcon className="h-6 w-6 text-zinc-400" />}
          trend={{ value: 8, isPositive: true, label: "vs last month" }}
          variant="glass"
          className="animate-fade-in-up animation-delay-100"
        />
        <DataCard
          title="Upcoming Events"
          value="12"
          icon={<CalendarIcon className="h-6 w-6 text-zinc-400" />}
          trend={{ value: 2, isPositive: false, label: "vs last month" }}
          variant="glass"
          className="animate-fade-in-up animation-delay-200"
        />
        <DataCard
          title="Active Ministries"
          value="8"
          icon={<HeartIcon className="h-6 w-6 text-zinc-400" />}
          trend={{ value: 1, isPositive: true, label: "vs last month" }}
          variant="glass"
          className="animate-fade-in-up animation-delay-300"
        />
      </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            <Card variant="glass" padding="lg" className="h-[400px] animate-fade-in-up">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Attendance Trends</h2>
                <Button variant="outline" size="sm">
                  Last 3 Months
                </Button>
              </div>
              <AttendanceSnapshot />
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <TaskCard
                title="Prepare Sunday Service"
                description="Finalize worship set, sermon notes, and coordinate with the tech team."
                dueDate="May 28, 2023"
                priority="high"
                status="in-progress"
                assignee={{
                  name: "Sarah Johnson",
                  avatar: "/avatar2.png",
                }}
                variant="glass"
                className="animate-fade-in-up animation-delay-100"
              />
              <TaskCard
                title="Youth Group Planning"
                description="Plan activities and discussion topics for next month's youth group meetings."
                dueDate="June 5, 2023"
                priority="medium"
                status="not-started"
                variant="glass"
                className="animate-fade-in-up animation-delay-200"
              />
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            <Card variant="glass" padding="lg" className="animate-fade-in-up animation-delay-300">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Upcoming Events</h2>
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </div>
              <UpcomingEvents />
            </Card>

            <MinistryCard
              title="Worship Ministry"
              status="Active"
              note="Preparing for Sunday service"
              avatars={["/avatar1.png", "/avatar2.png", "/avatar3.png", "/avatar4.png"]}
              stats={{
                members: 12,
                events: 4,
                activity: 85,
              }}
              chartColor="blue"
              badgeText="Music"
              badgeColor="blue"
              variant="glass"
              className="animate-fade-in-up animation-delay-500"
            />
          </div>
        </div>

        {/* Ministry Cards */}
        <div className="space-y-6">
          <div className="flex items-center justify-between animate-fade-in-up">
            <h2 className="text-xl font-semibold">Active Ministries</h2>
            <Button variant="outline" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
              Add Ministry
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <MinistryCard
              title="Youth Ministry"
            status="Active"
            note="Planning summer camp"
            avatars={["/avatar2.png", "/avatar3.png", "/avatar4.png"]}
            stats={{
              members: 8,
              events: 2,
              activity: 70,
            }}
            chartColor="green"
            badgeText="Youth"
            badgeColor="green"
            variant="glass"
            className="animate-fade-in-up animation-delay-100"
          />
          <MinistryCard
            title="Outreach Ministry"
            status="Active"
            note="Community food drive"
            avatars={["/avatar1.png", "/avatar4.png"]}
            stats={{
              members: 15,
              events: 3,
              activity: 90,
            }}
            chartColor="orange"
            badgeText="Outreach"
            badgeColor="orange"
            variant="glass"
            className="animate-fade-in-up animation-delay-200"
          />
          <MinistryCard
            title="Prayer Team"
            status="Active"
            note="Weekly prayer meetings"
            avatars={["/avatar2.png", "/avatar3.png"]}
            stats={{
              members: 10,
              events: 1,
              activity: 65,
            }}
            chartColor="purple"
            badgeText="Prayer"
            badgeColor="purple"
            variant="glass"
            className="animate-fade-in-up animation-delay-300"
            />
          </div>
        </div>
      </div> {/* End of wrapper div */}
      
      {/* Service Planning Modal */}
      <Dialog open={isServiceModalOpen} onOpenChange={setIsServiceModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Schedule New Service</DialogTitle>
            <DialogDescription>
              Create a new worship service with planning tools and team coordination.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-zinc-300">Service planning features:</p>
            <ul className="list-disc list-inside text-sm text-zinc-400 space-y-1">
              <li>Schedule worship elements and timing</li>
              <li>Assign team members and volunteers</li>
              <li>Coordinate music and media resources</li>
              <li>Send automated notifications</li>
            </ul>
            <div className="flex gap-3 pt-4">
              <Button variant="primary" leftIcon={<CalendarIcon className="h-4 w-4" />}>
                Create Service
              </Button>
              <Button variant="glass" onClick={() => setIsServiceModalOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* People Management Modal */}
      <Dialog open={isPeopleModalOpen} onOpenChange={setIsPeopleModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>People Management</DialogTitle>
            <DialogDescription>
              Access your church directory and member management tools.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-zinc-300">Available actions:</p>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="glass" size="sm" leftIcon={<UserGroupIcon className="h-4 w-4" />}>
                View Directory
              </Button>
              <Button variant="glass" size="sm" leftIcon={<PlusIcon className="h-4 w-4" />}>
                Add Member
              </Button>
            </div>
            <div className="flex gap-3 pt-4">
              <Link href="/people">
                <Button variant="primary">
                  Go to People
                </Button>
              </Link>
              <Button variant="glass" onClick={() => setIsPeopleModalOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Events & Ministry Modal */}
      <Dialog open={isEventsModalOpen} onOpenChange={setIsEventsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Events & Ministry</DialogTitle>
            <DialogDescription>
              Manage church events, ministries, and community activities.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-zinc-300">Quick actions:</p>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="glass" size="sm" leftIcon={<CalendarIcon className="h-4 w-4" />}>
                Create Event
              </Button>
              <Button variant="glass" size="sm" leftIcon={<HeartIcon className="h-4 w-4" />}>
                View Ministries
              </Button>
            </div>
            <div className="flex gap-3 pt-4">
              <Link href="/events">
                <Button variant="primary">
                  Go to Events
                </Button>
              </Link>
              <Button variant="glass" onClick={() => setIsEventsModalOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </ModuleLayout>
  )
}
