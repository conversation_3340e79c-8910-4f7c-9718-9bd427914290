"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import {
  PlusIcon,
  MusicalNoteIcon,
  UserGroupIcon,
  CalendarIcon,
  DocumentTextIcon,
  CogIcon,
  PlayIcon,
  PrinterIcon,
  ArchiveBoxIcon,
  ClipboardDocumentListIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CreditCardIcon,
  ChartBarIcon,
  UserPlusIcon,
  QrCodeIcon,
  ShieldCheckIcon,
  BellIcon,
  CameraIcon,
  VideoCameraIcon,
  MicrophoneIcon,
  BookOpenIcon,
  AcademicCapIcon,
  HeartIcon,
  GiftIcon,
  StarIcon,
  LightBulbIcon,
  GlobeAltIcon,
  ArrowUpTrayIcon,
  PaintBrushIcon,
  LinkIcon,
  ShareIcon,
  SparklesIcon,
  CodeBracketIcon,
} from "@heroicons/react/16/solid"

interface QuickAction {
  id: string
  icon: React.ReactNode
  label: string
  onClick: () => void
  color?: string
  href?: string
  shortcut?: string
}

interface ActionsContextType {
  actions: QuickAction[]
  setActions: (actions: QuickAction[]) => void
}

const ActionsContext = createContext<ActionsContextType | undefined>(undefined)

export function ActionsProvider({ children }: { children: React.ReactNode }) {
  const [actions, setActions] = useState<QuickAction[]>([])
  const pathname = usePathname()

  // Define actions for each module
  const moduleActions: Record<string, QuickAction[]> = {
    "/": [
      {
        id: "new-service",
        icon: <PlusIcon className="h-4 w-4" />,
        label: "New Service",
        onClick: () => console.log("New service"),
        color: "bg-blue-500",
        href: "/services/new",
        shortcut: "⌘N",
      },
      {
        id: "add-person",
        icon: <UserPlusIcon className="h-4 w-4" />,
        label: "Add Person",
        onClick: () => console.log("Add person"),
        color: "bg-green-500",
        href: "/people/new",
        shortcut: "⌘P",
      },
      {
        id: "schedule-event",
        icon: <CalendarIcon className="h-4 w-4" />,
        label: "Schedule Event",
        onClick: () => console.log("Schedule event"),
        color: "bg-purple-500",
        href: "/events/new",
        shortcut: "⌘E",
      },
      {
        id: "record-donation",
        icon: <CreditCardIcon className="h-4 w-4" />,
        label: "Record Donation",
        onClick: () => console.log("Record donation"),
        color: "bg-amber-500",
        href: "/giving/new",
        shortcut: "⌘D",
      },
      {
        id: "quick-checkin",
        icon: <QrCodeIcon className="h-4 w-4" />,
        label: "Quick Check-in",
        onClick: () => console.log("Quick check-in"),
        color: "bg-red-500",
        href: "/check-in",
        shortcut: "⌘Q",
      },
      {
        id: "send-message",
        icon: <EnvelopeIcon className="h-4 w-4" />,
        label: "Send Message",
        onClick: () => console.log("Send message"),
        color: "bg-cyan-500",
        href: "/messaging/new",
        shortcut: "⌘M",
      },
    ],
    "/services": [
      {
        id: "new-service",
        icon: <PlusIcon className="h-4 w-4" />,
        label: "New Service",
        onClick: () => console.log("New service"),
        color: "bg-blue-500",
        href: "/services/new",
        shortcut: "N",
      },
      {
        id: "add-song",
        icon: <MusicalNoteIcon className="h-4 w-4" />,
        label: "Add Song",
        onClick: () => console.log("Add song"),
        color: "bg-purple-500",
        href: "/services/song-library/new",
        shortcut: "S",
      },
      {
        id: "schedule-team",
        icon: <UserGroupIcon className="h-4 w-4" />,
        label: "Schedule Team",
        onClick: () => console.log("Schedule team"),
        color: "bg-green-500",
        href: "/services/teams",
        shortcut: "T",
      },
      {
        id: "live-mode",
        icon: <PlayIcon className="h-4 w-4" />,
        label: "Live Mode",
        onClick: () => console.log("Live mode"),
        color: "bg-red-500",
        shortcut: "L",
      },
      {
        id: "templates",
        icon: <DocumentTextIcon className="h-4 w-4" />,
        label: "Templates",
        onClick: () => console.log("Templates"),
        color: "bg-amber-500",
        href: "/services/templates",
        shortcut: "⇧T",
      },
      {
        id: "song-library",
        icon: <BookOpenIcon className="h-4 w-4" />,
        label: "Song Library",
        onClick: () => console.log("Song library"),
        color: "bg-indigo-500",
        href: "/services/song-library",
        shortcut: "⇧S",
      },
      {
        id: "media-manager",
        icon: <CameraIcon className="h-4 w-4" />,
        label: "Media Manager",
        onClick: () => console.log("Media manager"),
        color: "bg-pink-500",
        href: "/services/media",
        shortcut: "M",
      },
      {
        id: "rehearsal-mode",
        icon: <MicrophoneIcon className="h-4 w-4" />,
        label: "Rehearsal Mode",
        onClick: () => console.log("Rehearsal mode"),
        color: "bg-orange-500",
        shortcut: "R",
      },
    ],
    "/people": [
      {
        id: "add-person",
        icon: <UserPlusIcon className="h-4 w-4" />,
        label: "Add Person",
        onClick: () => console.log("Add person"),
        color: "bg-green-500",
        href: "/people/new",
        shortcut: "N",
      },
      {
        id: "import-data",
        icon: <ArchiveBoxIcon className="h-4 w-4" />,
        label: "Import Data",
        onClick: () => console.log("Import data"),
        color: "bg-blue-500",
        href: "/people/import",
        shortcut: "I",
      },
      {
        id: "send-email",
        icon: <EnvelopeIcon className="h-4 w-4" />,
        label: "Send Email",
        onClick: () => console.log("Send email"),
        color: "bg-purple-500",
        href: "/people/email",
        shortcut: "E",
      },
      {
        id: "create-group",
        icon: <UserGroupIcon className="h-4 w-4" />,
        label: "Create Group",
        onClick: () => console.log("Create group"),
        color: "bg-amber-500",
        href: "/groups/new",
        shortcut: "G",
      },
      {
        id: "generate-directory",
        icon: <ClipboardDocumentListIcon className="h-4 w-4" />,
        label: "Generate Directory",
        onClick: () => console.log("Generate directory"),
        color: "bg-indigo-500",
        shortcut: "D",
      },
      {
        id: "visitor-followup",
        icon: <PhoneIcon className="h-4 w-4" />,
        label: "Visitor Follow-up",
        onClick: () => console.log("Visitor follow-up"),
        color: "bg-red-500",
        href: "/people/visitors",
        shortcut: "V",
      },
      {
        id: "attendance-report",
        icon: <ChartBarIcon className="h-4 w-4" />,
        label: "Attendance Report",
        onClick: () => console.log("Attendance report"),
        color: "bg-cyan-500",
        href: "/people/attendance",
        shortcut: "A",
      },
      {
        id: "member-care",
        icon: <HeartIcon className="h-4 w-4" />,
        label: "Member Care",
        onClick: () => console.log("Member care"),
        color: "bg-pink-500",
        href: "/people/care",
        shortcut: "C",
      },
    ],
    "/events": [
      {
        id: "create-event",
        icon: <PlusIcon className="h-4 w-4" />,
        label: "Create Event",
        onClick: () => console.log("Create event"),
        color: "bg-purple-500",
        href: "/events/new",
        shortcut: "N",
      },
      {
        id: "book-room",
        icon: <MapPinIcon className="h-4 w-4" />,
        label: "Book Room",
        onClick: () => console.log("Book room"),
        color: "bg-blue-500",
        href: "/events/rooms",
        shortcut: "R",
      },
      {
        id: "assign-volunteers",
        icon: <UserGroupIcon className="h-4 w-4" />,
        label: "Assign Volunteers",
        onClick: () => console.log("Assign volunteers"),
        color: "bg-green-500",
        href: "/events/volunteers",
        shortcut: "V",
      },
      {
        id: "send-invitations",
        icon: <EnvelopeIcon className="h-4 w-4" />,
        label: "Send Invitations",
        onClick: () => console.log("Send invitations"),
        color: "bg-amber-500",
        shortcut: "I",
      },
      {
        id: "setup-registration",
        icon: <ClipboardDocumentListIcon className="h-4 w-4" />,
        label: "Setup Registration",
        onClick: () => console.log("Setup registration"),
        color: "bg-indigo-500",
        href: "/events/registration",
        shortcut: "⇧R",
      },
      {
        id: "event-checkin",
        icon: <QrCodeIcon className="h-4 w-4" />,
        label: "Event Check-in",
        onClick: () => console.log("Event check-in"),
        color: "bg-red-500",
        href: "/events/checkin",
        shortcut: "C",
      },
      {
        id: "live-streaming",
        icon: <VideoCameraIcon className="h-4 w-4" />,
        label: "Live Streaming",
        onClick: () => console.log("Live streaming"),
        color: "bg-pink-500",
        href: "/events/streaming",
        shortcut: "S",
      },
      {
        id: "event-feedback",
        icon: <StarIcon className="h-4 w-4" />,
        label: "Event Feedback",
        onClick: () => console.log("Event feedback"),
        color: "bg-orange-500",
        href: "/events/feedback",
        shortcut: "F",
      },
    ],
    "/giving": [
      {
        id: "record-donation",
        icon: <PlusIcon className="h-4 w-4" />,
        label: "Record Donation",
        onClick: () => console.log("Record donation"),
        color: "bg-green-500",
        href: "/giving/new",
        shortcut: "N",
      },
      {
        id: "process-batch",
        icon: <ArchiveBoxIcon className="h-4 w-4" />,
        label: "Process Batch",
        onClick: () => console.log("Process batch"),
        color: "bg-blue-500",
        href: "/giving/batch",
        shortcut: "B",
      },
      {
        id: "generate-statements",
        icon: <DocumentTextIcon className="h-4 w-4" />,
        label: "Generate Statements",
        onClick: () => console.log("Generate statements"),
        color: "bg-purple-500",
        href: "/giving/statements",
        shortcut: "S",
      },
      {
        id: "manage-funds",
        icon: <CreditCardIcon className="h-4 w-4" />,
        label: "Manage Funds",
        onClick: () => console.log("Manage funds"),
        color: "bg-amber-500",
        href: "/giving/funds",
        shortcut: "F",
      },
      {
        id: "pledge-tracking",
        icon: <ChartBarIcon className="h-4 w-4" />,
        label: "Pledge Tracking",
        onClick: () => console.log("Pledge tracking"),
        color: "bg-indigo-500",
        href: "/giving/pledges",
        shortcut: "P",
      },
      {
        id: "donor-communication",
        icon: <EnvelopeIcon className="h-4 w-4" />,
        label: "Donor Communication",
        onClick: () => console.log("Donor communication"),
        color: "bg-pink-500",
        href: "/giving/communication",
        shortcut: "C",
      },
      {
        id: "financial-reports",
        icon: <ClipboardDocumentListIcon className="h-4 w-4" />,
        label: "Financial Reports",
        onClick: () => console.log("Financial reports"),
        color: "bg-red-500",
        href: "/giving/reports",
        shortcut: "R",
      },
      {
        id: "online-giving",
        icon: <GiftIcon className="h-4 w-4" />,
        label: "Online Giving",
        onClick: () => console.log("Online giving"),
        color: "bg-cyan-500",
        href: "/giving/online",
        shortcut: "O",
      },
    ],
    "/check-in": [
      {
        id: "quick-checkin",
        icon: <QrCodeIcon className="h-4 w-4" />,
        label: "Quick Check-in",
        onClick: () => console.log("Quick check-in"),
        color: "bg-blue-500",
        shortcut: "Q",
      },
      {
        id: "add-visitor",
        icon: <UserPlusIcon className="h-4 w-4" />,
        label: "Add Visitor",
        onClick: () => console.log("Add visitor"),
        color: "bg-green-500",
        href: "/check-in/visitor",
        shortcut: "V",
      },
      {
        id: "print-nametags",
        icon: <PrinterIcon className="h-4 w-4" />,
        label: "Print Name Tags",
        onClick: () => console.log("Print name tags"),
        color: "bg-purple-500",
        shortcut: "P",
      },
      {
        id: "security-check",
        icon: <ShieldCheckIcon className="h-4 w-4" />,
        label: "Security Check",
        onClick: () => console.log("Security check"),
        color: "bg-red-500",
        shortcut: "S",
      },
      {
        id: "kiosk-mode",
        icon: <CogIcon className="h-4 w-4" />,
        label: "Kiosk Mode",
        onClick: () => console.log("Kiosk mode"),
        color: "bg-amber-500",
        href: "/check-in/kiosk",
        shortcut: "K",
      },
      {
        id: "attendance-report",
        icon: <ChartBarIcon className="h-4 w-4" />,
        label: "Attendance Report",
        onClick: () => console.log("Attendance report"),
        color: "bg-indigo-500",
        href: "/check-in/reports",
        shortcut: "R",
      },
    ],
    "/groups": [
      {
        id: "create-group",
        icon: <PlusIcon className="h-4 w-4" />,
        label: "Create Group",
        onClick: () => console.log("Create group"),
        color: "bg-indigo-500",
        href: "/groups/new",
        shortcut: "N",
      },
      {
        id: "small-groups",
        icon: <BookOpenIcon className="h-4 w-4" />,
        label: "Small Groups",
        onClick: () => console.log("Small groups"),
        color: "bg-blue-500",
        href: "/groups/small-groups",
        shortcut: "S",
      },
      {
        id: "bible-studies",
        icon: <AcademicCapIcon className="h-4 w-4" />,
        label: "Bible Studies",
        onClick: () => console.log("Bible studies"),
        color: "bg-green-500",
        href: "/groups/bible-studies",
        shortcut: "B",
      },
      {
        id: "group-communication",
        icon: <EnvelopeIcon className="h-4 w-4" />,
        label: "Group Communication",
        onClick: () => console.log("Group communication"),
        color: "bg-purple-500",
        href: "/groups/communication",
        shortcut: "C",
      },
      {
        id: "group-resources",
        icon: <DocumentTextIcon className="h-4 w-4" />,
        label: "Group Resources",
        onClick: () => console.log("Group resources"),
        color: "bg-amber-500",
        href: "/groups/resources",
        shortcut: "R",
      },
      {
        id: "leader-training",
        icon: <LightBulbIcon className="h-4 w-4" />,
        label: "Leader Training",
        onClick: () => console.log("Leader training"),
        color: "bg-pink-500",
        href: "/groups/training",
        shortcut: "T",
      },
    ],
    "/ministries": [
      {
        id: "create-ministry",
        icon: <PlusIcon className="h-4 w-4" />,
        label: "Create Ministry",
        onClick: () => console.log("Create ministry"),
        color: "bg-pink-500",
        href: "/ministries/new",
        shortcut: "N",
      },
      {
        id: "volunteer-management",
        icon: <UserGroupIcon className="h-4 w-4" />,
        label: "Volunteer Management",
        onClick: () => console.log("Volunteer management"),
        color: "bg-green-500",
        href: "/ministries/volunteers",
        shortcut: "V",
      },
      {
        id: "ministry-events",
        icon: <CalendarIcon className="h-4 w-4" />,
        label: "Ministry Events",
        onClick: () => console.log("Ministry events"),
        color: "bg-blue-500",
        href: "/ministries/events",
        shortcut: "E",
      },
      {
        id: "resource-allocation",
        icon: <GiftIcon className="h-4 w-4" />,
        label: "Resource Allocation",
        onClick: () => console.log("Resource allocation"),
        color: "bg-amber-500",
        href: "/ministries/resources",
        shortcut: "R",
      },
      {
        id: "ministry-reports",
        icon: <ChartBarIcon className="h-4 w-4" />,
        label: "Ministry Reports",
        onClick: () => console.log("Ministry reports"),
        color: "bg-indigo-500",
        href: "/ministries/reports",
        shortcut: "⇧R",
      },
      {
        id: "outreach-programs",
        icon: <HeartIcon className="h-4 w-4" />,
        label: "Outreach Programs",
        onClick: () => console.log("Outreach programs"),
        color: "bg-red-500",
        href: "/ministries/outreach",
        shortcut: "O",
      },
    ],
    "/messaging": [
      {
        id: "compose-message",
        icon: <PlusIcon className="h-4 w-4" />,
        label: "Compose Message",
        onClick: () => console.log("Compose message"),
        color: "bg-cyan-500",
        href: "/messaging/compose",
        shortcut: "N",
      },
      {
        id: "email-campaign",
        icon: <EnvelopeIcon className="h-4 w-4" />,
        label: "Email Campaign",
        onClick: () => console.log("Email campaign"),
        color: "bg-blue-500",
        href: "/messaging/email",
        shortcut: "E",
      },
      {
        id: "sms-broadcast",
        icon: <PhoneIcon className="h-4 w-4" />,
        label: "SMS Broadcast",
        onClick: () => console.log("SMS broadcast"),
        color: "bg-green-500",
        href: "/messaging/sms",
        shortcut: "S",
      },
      {
        id: "push-notifications",
        icon: <BellIcon className="h-4 w-4" />,
        label: "Push Notifications",
        onClick: () => console.log("Push notifications"),
        color: "bg-purple-500",
        href: "/messaging/push",
        shortcut: "P",
      },
      {
        id: "newsletter",
        icon: <DocumentTextIcon className="h-4 w-4" />,
        label: "Newsletter",
        onClick: () => console.log("Newsletter"),
        color: "bg-amber-500",
        href: "/messaging/newsletter",
        shortcut: "⇧N",
      },
      {
        id: "message-templates",
        icon: <ClipboardDocumentListIcon className="h-4 w-4" />,
        label: "Message Templates",
        onClick: () => console.log("Message templates"),
        color: "bg-indigo-500",
        href: "/messaging/templates",
        shortcut: "T",
      },
    ],
    "/publish": [
      {
        id: "preview-site",
        icon: <GlobeAltIcon className="h-4 w-4" />,
        label: "Preview Site",
        onClick: () => console.log("Preview site"),
        color: "bg-blue-500",
        href: "/publish/preview",
        shortcut: "P",
      },
      {
        id: "publish-changes",
        icon: <ArrowUpTrayIcon className="h-4 w-4" />,
        label: "Publish Changes",
        onClick: () => console.log("Publish changes"),
        color: "bg-green-500",
        href: "/publish/deploy",
        shortcut: "⌘P",
      },
      {
        id: "theme-selector",
        icon: <PaintBrushIcon className="h-4 w-4" />,
        label: "Change Theme",
        onClick: () => console.log("Change theme"),
        color: "bg-purple-500",
        href: "/publish/themes",
        shortcut: "T",
      },
      {
        id: "analytics",
        icon: <ChartBarIcon className="h-4 w-4" />,
        label: "Analytics",
        onClick: () => console.log("Analytics"),
        color: "bg-amber-500",
        href: "/publish/analytics",
        shortcut: "A",
      },
      {
        id: "domain-settings",
        icon: <LinkIcon className="h-4 w-4" />,
        label: "Domain Settings",
        onClick: () => console.log("Domain settings"),
        color: "bg-indigo-500",
        href: "/publish/domains",
        shortcut: "D",
      },
      {
        id: "social-media",
        icon: <ShareIcon className="h-4 w-4" />,
        label: "Social Media",
        onClick: () => console.log("Social media"),
        color: "bg-pink-500",
        href: "/publish/social",
        shortcut: "S",
      },
      {
        id: "ai-content",
        icon: <SparklesIcon className="h-4 w-4" />,
        label: "AI Content Assistant",
        onClick: () => console.log("AI content"),
        color: "bg-purple-500",
        href: "/publish/ai",
        shortcut: "⇧A",
      },
      {
        id: "embed-generator",
        icon: <CodeBracketIcon className="h-4 w-4" />,
        label: "Embed Generator",
        onClick: () => console.log("Embed generator"),
        color: "bg-cyan-500",
        href: "/publish/embed",
        shortcut: "E",
      },
    ],
  }

  // Update actions based on current path
  useEffect(() => {
    const currentActions = moduleActions[pathname] || []
    setActions(currentActions)
  }, [pathname])

  return <ActionsContext.Provider value={{ actions, setActions }}>{children}</ActionsContext.Provider>
}

export function useActions() {
  const context = useContext(ActionsContext)
  if (context === undefined) {
    throw new Error("useActions must be used within an ActionsProvider")
  }
  return context
}
