// Utility functions for consistent formatting across server and client
export function formatNumber(value: number): string {
  // Use a consistent locale to prevent hydration mismatches
  return new Intl.NumberFormat('en-US').format(value)
}

export function formatCompactNumber(value: number): string {
  // Format large numbers in compact form (e.g., 1.2K, 54.2K)
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return formatNumber(value)
}
