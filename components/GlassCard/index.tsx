import { cn } from "@/lib/utils"
import { ReactNode } from "react"

interface GlassCardProps {
  children: ReactNode
  className?: string
  hover?: boolean
  interactive?: boolean
  padding?: "none" | "sm" | "md" | "lg"
}

export default function GlassCard({ 
  children, 
  className, 
  hover = false, 
  interactive = false,
  padding = "md" 
}: GlassCardProps) {
  const paddingClasses = {
    none: "",
    sm: "p-3",
    md: "p-5",
    lg: "p-6"
  }

  return (
    <div
      className={cn(
        "glass-card",
        paddingClasses[padding],
        hover && "hover:bg-white/10 transition-all duration-300",
        interactive && "cursor-pointer hover:scale-[1.02] transition-transform duration-200",
        className
      )}
    >
      {children}
    </div>
  )
}
