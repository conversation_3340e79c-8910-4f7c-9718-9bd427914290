"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import Image from "next/image"
import { cn } from "@/lib/utils"
import Icon from "@/components/Icon"

const navigation = [
  { name: "Dashboard", href: "/", icon: "home" },
  { name: "People", href: "/people", icon: "profile" },
  { name: "Services", href: "/services", icon: "calendar" },
  { name: "Events", href: "/events", icon: "star" },
  { name: "Groups", href: "/groups", icon: "profile" },
  { name: "Giving", href: "/giving", icon: "chart-line" },
  { name: "Communications", href: "/communications", icon: "chat-think" },
  { name: "Check-ins", href: "/check-ins", icon: "check" },
]

interface Contact {
  id: string
  name: string
  role: string
  avatar: string
  status: "active" | "inactive"
}

export default function RevolutionarySidebar() {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  const contacts: Contact[] = [
    { id: "1", name: "<PERSON>", role: "Pastor", avatar: "/images/avatars/1.png", status: "active" },
    { id: "2", name: "Sarah Johnson", role: "Worship Leader", avatar: "/images/avatars/2.png", status: "active" },
    { id: "3", name: "Michael <PERSON>", role: "Youth Pastor", avatar: "/images/avatars/3.png", status: "active" },
    { id: "4", name: "Emily Wilson", role: "Administrator", avatar: "/images/avatars/4.png", status: "active" },
  ]

  const filteredContacts = contacts.filter(
    (contact) =>
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.role.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <div
      className={cn(
        "flex flex-col h-screen bg-[var(--bg-sidebar)] backdrop-blur-2xl border-r border-[var(--border-subtle)] transition-all duration-300 fixed left-0 top-0 z-30",
        isCollapsed ? "w-20" : "w-80",
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-[var(--border-subtle)]">
        <div className="flex items-center justify-between">
          <div className={cn("flex items-center gap-3", isCollapsed && "justify-center")}>
            <Image src="/images/logo-light.png" alt="ChurchCore" width={32} height={32} className="rounded-lg" />
            {!isCollapsed && <span className="text-lg font-semibold text-white">ChurchCore</span>}
          </div>
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1.5 rounded-xl hover:bg-white/10 transition-colors icon-button"
          >
            <Icon className="h-5 w-5 fill-white" name={isCollapsed ? "arrow-right" : "arrow-left"} />
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-3 space-y-1" aria-label="Main navigation">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "sidebar-item-modern rounded-xl",
                isActive && "sidebar-item-modern-active",
                isCollapsed && "justify-center",
              )}
            >
              <Icon className="h-5 w-5 flex-shrink-0 fill-current" name={item.icon} />
              {!isCollapsed && (
                <div className="flex items-center justify-between flex-1">
                  <span className="text-sm font-medium">{item.name}</span>
                </div>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Contacts Section */}
      {!isCollapsed && (
        <div className="border-t border-[var(--border-subtle)] p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-zinc-400">Team Members</h3>
            <span className="text-xs text-zinc-500">{contacts.length}</span>
          </div>

          {/* Search */}
          <div className="relative mb-3">
            <Icon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 fill-zinc-500" name="search" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search team..."
              className="input-modern"
            />
          </div>

          {/* New Contact Button */}
          <button className="w-full flex items-center gap-2 px-3 py-2 mb-3 rounded-xl transition-colors action-button">
            <div className="w-8 h-8 rounded-full bg-white/5 flex items-center justify-center">
              <Icon className="h-4 w-4 fill-current" name="plus" />
            </div>
            <span className="text-sm font-medium">Add Member</span>
          </button>

          {/* Contact List */}
          <div className="space-y-1 max-h-64 overflow-y-auto custom-scrollbar">
            {filteredContacts.map((contact) => (
              <button
                key={contact.id}
                className="w-full flex items-center gap-3 px-3 py-2 rounded-xl text-left contact-item"
              >
                <Image
                  src={contact.avatar || "/images/avatar-sm.png"}
                  alt={contact.name}
                  width={32}
                  height={32}
                  className="rounded-full object-cover"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">{contact.name}</p>
                  <p className="text-xs text-zinc-500 truncate">{contact.role}</p>
                </div>
              </button>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="mt-4 pt-4 border-t border-[var(--border-subtle)]">
            <button className="w-full flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-white/5 transition-colors">
              <div className="w-8 h-8 rounded-full bg-white/5 flex items-center justify-center">
                <Icon className="h-4 w-4 fill-current" name="gear" />
              </div>
              <span className="text-sm font-medium text-white">Settings</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
