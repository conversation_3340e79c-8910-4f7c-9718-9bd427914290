import { ReactNode } from "react"
import { cn } from "@/lib/utils"
import Icon from "@/components/Icon"
import GlassCard from "@/components/GlassCard"

interface DashboardWidgetProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: "increase" | "decrease"
  }
  icon: string
  color?: string
  children?: ReactNode
  size?: "small" | "medium" | "large"
  interactive?: boolean
  loading?: boolean
}

export default function DashboardWidget({
  title,
  value,
  change,
  icon,
  color = "blue",
  children,
  size = "medium",
  interactive = false,
  loading = false
}: DashboardWidgetProps) {
  const sizeClasses = {
    small: "col-span-1",
    medium: "col-span-2", 
    large: "col-span-3"
  }

  const colorClasses = {
    blue: "from-blue-500 to-blue-600",
    green: "from-green-500 to-green-600",
    purple: "from-purple-500 to-purple-600",
    amber: "from-amber-500 to-amber-600",
    red: "from-red-500 to-red-600",
    cyan: "from-cyan-500 to-cyan-600"
  }

  return (
    <GlassCard 
      className={cn(sizeClasses[size], "relative overflow-hidden")}
      interactive={interactive}
      hover={interactive}
    >
      {loading && (
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-10">
          <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin" />
        </div>
      )}
      
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={cn(
            "w-10 h-10 rounded-xl flex items-center justify-center bg-gradient-to-br",
            colorClasses[color as keyof typeof colorClasses] || colorClasses.blue
          )}>
            <Icon className="h-5 w-5 fill-white" name={icon} />
          </div>
          <div>
            <h3 className="text-sm font-medium text-zinc-400">{title}</h3>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-white">{value}</span>
              {change && (
                <div className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                  change.type === "increase" 
                    ? "bg-green-500/20 text-green-400" 
                    : "bg-red-500/20 text-red-400"
                )}>
                  <Icon 
                    className="h-3 w-3 fill-current" 
                    name={change.type === "increase" ? "arrow-up" : "arrow-down"} 
                  />
                  <span>{Math.abs(change.value)}%</span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {interactive && (
          <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
            <Icon className="h-4 w-4 fill-zinc-400" name="dots" />
          </button>
        )}
      </div>

      {/* Content */}
      {children && (
        <div className="mt-4">
          {children}
        </div>
      )}
    </GlassCard>
  )
}
