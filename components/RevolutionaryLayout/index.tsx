"use client"

import React, { ReactNode, useState } from "react"
import RevolutionarySidebar from "@/components/RevolutionarySidebar"
import { cn } from "@/lib/utils"
import Icon from "@/components/Icon"

interface RevolutionaryLayoutProps {
  children: ReactNode
  title?: string
  showTopBar?: boolean
  showFloatingActions?: boolean
}

export default function RevolutionaryLayout({ 
  children, 
  title,
  showTopBar = true,
  showFloatingActions = true 
}: RevolutionaryLayoutProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [liveMode, setLiveMode] = useState(true)

  // Update time every second
  React.useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="flex h-screen bg-black overflow-hidden">
      {/* Revolutionary Sidebar */}
      <RevolutionarySidebar />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-80">
        {/* Top Bar */}
        {showTopBar && (
          <div className="h-16 bg-[var(--bg-glass)] backdrop-blur-xl border-b border-[var(--border-subtle)] px-6 flex items-center justify-between">
            <div className="flex items-center gap-4">
              {title && <h1 className="text-xl font-bold text-white">{title}</h1>}
            </div>
            
            <div className="flex items-center gap-4">
              {/* Live Time */}
              <div className="text-lg font-mono text-white">
                {currentTime.toLocaleTimeString('en-US', { 
                  hour: '2-digit', 
                  minute: '2-digit', 
                  second: '2-digit' 
                })}
              </div>
              
              {/* Live Mode Toggle */}
              <button
                onClick={() => setLiveMode(!liveMode)}
                className={cn(
                  "px-4 py-2 rounded-lg flex items-center gap-2 transition-all",
                  liveMode ? "bg-green-500/20 text-green-400" : "bg-white/10 text-white"
                )}
              >
                <Icon className="h-4 w-4 fill-current" name={liveMode ? "arrow" : "pause"} />
                <span className="text-sm font-medium">{liveMode ? "Live Mode" : "Paused"}</span>
              </button>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 relative overflow-auto">
          <div className="p-6">
            {children}
          </div>
        </div>

        {/* Floating Action Buttons */}
        {showFloatingActions && (
          <div className="fixed bottom-6 right-6 flex flex-col gap-3 z-40">
            <button className="w-12 h-12 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center shadow-lg transition-colors">
              <Icon className="h-6 w-6 fill-white" name="plus" />
            </button>
            <button className="w-12 h-12 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center shadow-lg transition-colors">
              <Icon className="h-6 w-6 fill-white" name="chat-think" />
            </button>
            <button className="w-12 h-12 bg-purple-500 hover:bg-purple-600 rounded-full flex items-center justify-center shadow-lg transition-colors">
              <Icon className="h-6 w-6 fill-white" name="search" />
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
