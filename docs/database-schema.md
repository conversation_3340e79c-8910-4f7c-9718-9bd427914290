# Church Management System - Database Schema

## Core Entities

### 1. People & Relationships

```sql
-- Core People table (extends current Customer model)
CREATE TABLE people (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    middle_name <PERSON><PERSON><PERSON><PERSON>(100),
    preferred_name <PERSON><PERSON><PERSON><PERSON>(100),
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    mobile_phone VARCHAR(20),
    date_of_birth DATE,
    gender VARCHAR(20),
    marital_status VARCHAR(50),
    avatar_url TEXT,
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    membership_status VARCHAR(50) DEFAULT 'visitor', -- visitor, member, inactive, deceased
    membership_date DATE,
    baptism_date DATE,
    campus_id UUID REFERENCES campuses(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES people(id),
    updated_by UUID REFERENCES people(id)
);

-- Households/Families
CREATE TABLE households (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    primary_contact_id UUID REFERENCES people(id),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- People-Household relationships
CREATE TABLE household_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    person_id UUID REFERENCES people(id),
    household_id UUID REFERENCES households(id),
    relationship VARCHAR(50), -- head, spouse, child, other
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Custom fields for people profiles
CREATE TABLE people_custom_fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    field_type VARCHAR(50) NOT NULL, -- text, number, date, boolean, select
    options JSONB, -- for select fields
    is_required BOOLEAN DEFAULT FALSE,
    campus_id UUID REFERENCES campuses(id),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE people_custom_values (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    person_id UUID REFERENCES people(id),
    field_id UUID REFERENCES people_custom_fields(id),
    value TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Campus & Organization Structure

```sql
-- Multi-campus support
CREATE TABLE campuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Ministries/Departments
CREATE TABLE ministries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    campus_id UUID REFERENCES campuses(id),
    leader_id UUID REFERENCES people(id),
    parent_ministry_id UUID REFERENCES ministries(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Services & Worship Planning

```sql
-- Service Types (Sunday Morning, Evening, Youth, etc.)
CREATE TABLE service_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    campus_id UUID REFERENCES campuses(id),
    default_duration_minutes INTEGER DEFAULT 90,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Service Plans
CREATE TABLE service_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    service_type_id UUID REFERENCES service_types(id),
    plan_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    series_title VARCHAR(255),
    theme VARCHAR(255),
    notes TEXT,
    status VARCHAR(50) DEFAULT 'draft', -- draft, published, archived
    created_by UUID REFERENCES people(id),
    updated_by UUID REFERENCES people(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Service Items (Songs, Sermon, Announcements, etc.)
CREATE TABLE service_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_plan_id UUID REFERENCES service_plans(id),
    item_type VARCHAR(50) NOT NULL, -- song, sermon, announcement, prayer, offering, etc.
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration_minutes INTEGER,
    sort_order INTEGER,
    notes TEXT,
    attachments JSONB, -- file URLs, links, etc.
    created_at TIMESTAMP DEFAULT NOW()
);

-- Song Library
CREATE TABLE songs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    artist VARCHAR(255),
    album VARCHAR(255),
    ccli_number VARCHAR(50),
    copyright_info TEXT,
    lyrics TEXT,
    chord_chart TEXT,
    key_signature VARCHAR(10),
    tempo INTEGER,
    time_signature VARCHAR(10),
    themes JSONB, -- array of theme tags
    audio_url TEXT,
    video_url TEXT,
    sheet_music_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Song arrangements for different keys
CREATE TABLE song_arrangements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    song_id UUID REFERENCES songs(id),
    key_signature VARCHAR(10) NOT NULL,
    chord_chart TEXT,
    audio_url TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 4. Teams & Volunteer Management

```sql
-- Teams (Worship, Tech, Ushers, etc.)
CREATE TABLE teams (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    ministry_id UUID REFERENCES ministries(id),
    leader_id UUID REFERENCES people(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Team Positions
CREATE TABLE team_positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID REFERENCES teams(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    requirements TEXT,
    is_leader BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Team Members
CREATE TABLE team_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID REFERENCES teams(id),
    person_id UUID REFERENCES people(id),
    position_id UUID REFERENCES team_positions(id),
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, pending
    joined_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Volunteer Scheduling
CREATE TABLE volunteer_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_plan_id UUID REFERENCES service_plans(id),
    team_id UUID REFERENCES teams(id),
    person_id UUID REFERENCES people(id),
    position_id UUID REFERENCES team_positions(id),
    status VARCHAR(50) DEFAULT 'scheduled', -- scheduled, confirmed, declined, substitute
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Volunteer Availability
CREATE TABLE volunteer_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    person_id UUID REFERENCES people(id),
    team_id UUID REFERENCES teams(id),
    available_date DATE,
    is_available BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Groups & Community

```sql
-- Groups (Small Groups, Classes, Ministries)
CREATE TABLE groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    group_type VARCHAR(50), -- small_group, class, ministry, committee
    campus_id UUID REFERENCES campuses(id),
    ministry_id UUID REFERENCES ministries(id),
    leader_id UUID REFERENCES people(id),
    co_leader_id UUID REFERENCES people(id),
    meeting_day VARCHAR(20),
    meeting_time TIME,
    location VARCHAR(255),
    max_members INTEGER,
    is_open BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Group Members
CREATE TABLE group_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES groups(id),
    person_id UUID REFERENCES people(id),
    role VARCHAR(50) DEFAULT 'member', -- leader, co_leader, member
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, pending
    joined_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Group Attendance
CREATE TABLE group_attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES groups(id),
    person_id UUID REFERENCES people(id),
    meeting_date DATE,
    attended BOOLEAN DEFAULT FALSE,
    notes TEXT,
    recorded_by UUID REFERENCES people(id),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 6. Events & Calendar

```sql
-- Events
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(50), -- service, meeting, conference, social, outreach
    campus_id UUID REFERENCES campuses(id),
    ministry_id UUID REFERENCES ministries(id),
    location VARCHAR(255),
    start_datetime TIMESTAMP NOT NULL,
    end_datetime TIMESTAMP NOT NULL,
    all_day BOOLEAN DEFAULT FALSE,
    recurring_pattern VARCHAR(50), -- none, daily, weekly, monthly, yearly
    recurring_end_date DATE,
    max_attendees INTEGER,
    registration_required BOOLEAN DEFAULT FALSE,
    registration_deadline TIMESTAMP,
    cost DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active', -- active, cancelled, completed
    created_by UUID REFERENCES people(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Event Registration
CREATE TABLE event_registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID REFERENCES events(id),
    person_id UUID REFERENCES people(id),
    registration_date TIMESTAMP DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'registered', -- registered, attended, cancelled, no_show
    payment_status VARCHAR(50) DEFAULT 'pending', -- pending, paid, refunded
    amount_paid DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Facilities & Resources
CREATE TABLE facilities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    campus_id UUID REFERENCES campuses(id),
    capacity INTEGER,
    equipment JSONB, -- available equipment/features
    is_bookable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Facility Bookings
CREATE TABLE facility_bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    facility_id UUID REFERENCES facilities(id),
    event_id UUID REFERENCES events(id),
    booked_by UUID REFERENCES people(id),
    start_datetime TIMESTAMP NOT NULL,
    end_datetime TIMESTAMP NOT NULL,
    purpose VARCHAR(255),
    status VARCHAR(50) DEFAULT 'confirmed', -- confirmed, cancelled, pending
    created_at TIMESTAMP DEFAULT NOW()
);
```
