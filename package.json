{"name": "core-2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@tailwindcss/postcss": "^4.0.9", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/react-scroll": "^1.8.10", "@types/react-slider": "^1.3.6", "@types/react-tagsinput": "^3.20.6", "add": "^2.0.6", "emoji-picker-react": "^4.12.0", "framer-motion": "^12.5.0", "millify": "^6.1.0", "next": "15.2.0", "next-themes": "^0.4.4", "postcss": "^8.5.3", "react": "^19.0.0", "react-animate-height": "^3.2.3", "react-datepicker": "^8.2.0", "react-dom": "^19.0.0", "react-number-format": "^5.4.3", "react-remove-scroll": "^2.6.3", "react-scroll": "^1.9.3", "react-slider": "^2.0.6", "react-tagsinput": "^3.20.3", "react-textarea-autosize": "^8.5.8", "react-tooltip": "^5.28.0", "react-use": "^17.6.0", "recharts": "^2.15.1", "sharp": "^0.33.5", "swiper": "^11.2.4", "tailwindcss": "^4.0.9", "yarn": "^1.22.22"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "tailwind-scrollbar": "^4.0.1", "typescript": "^5"}}