export interface Person {
    id: string;
    firstName: string;
    lastName: string;
    preferredName?: string;
    email: string;
    phone?: string;
    mobilePhone?: string;
    dateOfBirth?: string;
    gender?: 'male' | 'female' | 'other';
    maritalStatus?: 'single' | 'married' | 'divorced' | 'widowed';
    avatar: string;
    address?: {
        line1?: string;
        line2?: string;
        city?: string;
        state?: string;
        postalCode?: string;
        country?: string;
    };
    membershipStatus: 'visitor' | 'member' | 'inactive' | 'deceased';
    membershipDate?: string;
    baptismDate?: string;
    campusId?: string;
    householdId?: string;
    groups: string[];
    teams: string[];
    lastAttendance?: string;
    totalDonations?: number;
    notes?: string;
    customFields?: Record<string, any>;
    createdAt: string;
    updatedAt: string;
}

export interface Household {
    id: string;
    name: string;
    primaryContactId: string;
    members: string[];
    address?: {
        line1?: string;
        line2?: string;
        city?: string;
        state?: string;
        postalCode?: string;
        country?: string;
    };
    createdAt: string;
    updatedAt: string;
}

export interface PersonCustomField {
    id: string;
    name: string;
    fieldType: 'text' | 'number' | 'date' | 'boolean' | 'select';
    options?: string[];
    isRequired: boolean;
    campusId?: string;
    createdAt: string;
}

export interface PersonNote {
    id: string;
    personId: string;
    content: string;
    category?: string;
    isPrivate: boolean;
    createdBy: string;
    createdAt: string;
    updatedAt: string;
}
