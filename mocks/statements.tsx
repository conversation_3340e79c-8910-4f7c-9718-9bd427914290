export const statistics = [
    {
        id: 1,
        title: "My funds",
        icon: "wallet",
        tooltip: "Maximum 100 characters. No HTML or emoji allowed",
        price: 16523,
    },
    {
        id: 2,
        title: "Earnings",
        icon: "product-think",
        tooltip: "Maximum 100 characters. No HTML or emoji allowed",
        counter: 20876,
    },
    {
        id: 3,
        title: "Fees",
        icon: "profile",
        tooltip: "Maximum 100 characters. No HTML or emoji allowed",
        counter: 1652,
    },
];

export const transactions = [
    {
        id: 1,
        title: "Quantum Interaction Kit",
        category: "WordPress Theme",
        image: "/images/products/2.png",
        type: "refund",
        date: "22 Apr, 6:45 PM",
        orderId: "UI8–321654987",
        price: 32,
        amount: -32,
    },
    {
        id: 2,
        title: "Quantum Design System Toolkit",
        category: "UI Kit",
        image: "/images/products/3.png",
        type: "paid",
        date: "20 Apr, 7:12 PM",
        orderId: "UI8–321654423",
        price: 132,
        amount: 132,
    },
    {
        id: 3,
        title: "Quantum Dashboard Templates",
        category: "WordPress Theme",
        image: "/images/products/4.png",
        type: "refund",
        date: "19 Apr, 6:45 PM",
        orderId: "UI8–321654011",
        price: 56,
        amount: -56,
    },
    {
        id: 4,
        title: "Quantum User Flow Diagrams",
        category: "UI Kit",
        image: "/images/products/5.png",
        type: "paid",
        date: "18 Apr, 7:12 PM",
        orderId: "UI8–321655322",
        price: 12,
        amount: 12,
    },
    {
        id: 5,
        title: "Quantum Interaction Kit",
        category: "WordPress Theme",
        image: "/images/products/6.png",
        type: "paid",
        date: "15 Apr, 5:12 PM",
        orderId: "UI8–321652202",
        price: 32,
        amount: -32,
    },
    {
        id: 6,
        title: "Quantum Design System Toolkit",
        category: "UI Kit",
        image: "/images/products/7.png",
        type: "paid",
        date: "12 Apr, 5:44 PM",
        orderId: "UI8–321654102",
        price: 132,
        amount: 132,
    },
    {
        id: 7,
        title: "Quantum Dashboard Templates",
        category: "WordPress Theme",
        image: "/images/products/8.png",
        type: "paid",
        date: "8 Apr, 6:45 PM",
        orderId: "UI8–321654500",
        price: 256,
        amount: 256,
    },
    {
        id: 8,
        title: "Quantum User Flow Diagrams",
        category: "UI Kit",
        image: "/images/products/9.png",
        type: "paid",
        date: "5 Apr, 7:12 PM",
        orderId: "UI8–321654421",
        price: 12,
        amount: 12,
    },
    {
        id: 9,
        title: "Quantum Interaction Kit",
        category: "WordPress Theme",
        image: "/images/products/10.png",
        type: "paid",
        date: "3 Apr, 6:45 PM",
        orderId: "UI8–321654985",
        price: 88,
        amount: -88,
    },
    {
        id: 10,
        title: "Quantum Design System Toolkit",
        category: "UI Kit",
        image: "/images/products/1.png",
        type: "paid",
        date: "30 Mar, 7:44 PM",
        orderId: "UI8–321654477",
        price: 132,
        amount: 132,
    },
];
