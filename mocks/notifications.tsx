export const newNotifications = [
    {
        id: 1,
        login: "conceptual_artist",
        action: "purchased",
        product: "3D Artistry Pack",
        avatar: "/images/avatars/1.png",
        time: "1h ago",
    },
    {
        id: 2,
        login: "imaginative_vision",
        action: "liked",
        product: "Interactive Design Assets",
        avatar: "/images/avatars/2.png",
        time: "3h ago",
    },
    {
        id: 3,
        login: "aesthetic_explorer",
        action: "commented on",
        product: "CreativeSpace UI Kit",
        avatar: "/images/avatars/3.png",
        time: "5h ago",
    },
    {
        id: 4,
        login: "style_savant",
        action: "liked",
        product: "GraphicGenius Fonts",
        avatar: "/images/avatars/4.png",
        time: "7h ago",
    },
    {
        id: 5,
        login: "visual_vortex",
        action: "purchased",
        product: "DesignWave Toolkit",
        avatar: "/images/avatars/5.png",
        time: "12h ago",
    },
    {
        id: 6,
        login: "innovative_ideas",
        action: "You followed a new account!",
        product: "Virtual Reality Elements",
        avatar: "/images/avatars/6.png",
        time: "15h ago",
    },
    {
        id: 7,
        login: "design_enthusiast",
        action: "purchased",
        product: "FontFusion Collection",
        avatar: "/images/avatars/7.png",
        time: "18h ago",
    },
    {
        id: 8,
        login: "artistic_mindset",
        action: "send a refund request",
        product: "3D Visionary Assets",
        avatar: "/images/avatars/8.png",
        time: "20h ago",
    },
    {
        id: 9,
        login: "creative_spark",
        action: "commented on",
        product: "PixelCraft UI Kit",
        avatar: "/images/avatars/9.png",
        time: "22h ago",
    },
    {
        id: 10,
        login: "conceptual_artist",
        action: "purchased",
        product: "3D Artistry Pack",
        avatar: "/images/avatars/1.png",
        time: "1d ago",
    },
    {
        id: 11,
        login: "imaginative_vision",
        action: "liked",
        product: "Interactive Design Assets",
        avatar: "/images/avatars/2.png",
        time: "1d ago",
    },
    {
        id: 12,
        login: "aesthetic_explorer",
        action: "commented on",
        product: "CreativeSpace UI Kit",
        avatar: "/images/avatars/3.png",
        time: "1d ago",
    },
];

export const allNotifications = [
    {
        id: 1,
        type: "purchase",
        login: "spectrum",
        action: "purchased",
        product: "User Experience Toolkit",
        content:
            "Hello! I’d like to request a new product, perhaps a UI Kit that can simplify our design workflow.",
        avatar: "/images/avatars/8.png",
        time: "2h",
        new: true,
    },
    {
        id: 2,
        type: "like",
        login: "vortex",
        action: "liked your comment in",
        product: "Core Dashboard 2.0",
        content:
            "Hello, I would like to request a new product, could you please check...",
        avatar: "/images/avatar.png",
        time: "18h",
        new: true,
    },
    {
        id: 3,
        type: "like",
        login: "zephyr",
        action: "liked your reply in",
        product: "Interactive Prototype Suite",
        content:
            "Hi! I’d like to put forward a suggestion for a new product, like a collection of modern Fonts that could inspire creativity.",
        avatar: "/images/avatars/9.png",
        time: "23h",
        new: true,
    },
    {
        id: 4,
        type: "reply",
        login: "quasar",
        action: "send a refund request",
        product: "Animation Toolkit",
        content:
            "Hi! I’m looking to suggest a new product, maybe a UI Kit that can help us create more engaging interfaces.",
        avatar: "/images/avatars/1.png",
        time: "1d",
        new: true,
    },
    {
        id: 5,
        type: "purchase",
        login: "solstice",
        action: "purchased",
        product: "Iconography Library",
        content:
            "Hello! I’d like to recommend a new product, such as a set of stylish Fonts that can add flair to our designs.",
        avatar: "/images/avatars/3.png",
        time: "2d",
        new: false,
    },
    {
        id: 6,
        type: "reply",
        login: "nova",
        action: "send a refund request",
        product: "3D Visualization Toolkit",
        content:
            "Hey! I’d like to propose a new product idea, maybe some fresh Fonts that can elevate our design projects.",
        avatar: "/images/avatars/6.png",
        time: "3d",
        new: false,
    },
    {
        id: 7,
        type: "purchase",
        login: "lynx",
        action: "purchased",
        product: "Color Palette Creator",
        content:
            "Hey there! I want to request a new product, perhaps a UI Kit that could really enhance our current tools.",
        avatar: "/images/avatars/8.png",
        time: "1mo",
        new: false,
    },
    {
        id: 8,
        type: "reply",
        login: "orion",
        action: "send a refund request",
        product: "Font Collection Pro",
        content:
            "Hello! I’m interested in recommending a new product, such as a UI Kit that could streamline our design process.",
        avatar: "/images/avatars/7.png",
        time: "1mo",
        new: false,
    },
    {
        id: 9,
        type: "like",
        login: "jupiter",
        action: "liked your comment in",
        product: "UI Design Hub",
        content:
            "Hi there! I'd love to suggest a new addition to our offerings, perhaps a UI Kit that enhances user experience.",
        avatar: "/images/avatars/4.png",
        time: "2mo",
        new: false,
    },
];
