import { Person, Household } from "@/types/person";

export const people: Person[] = [
    {
        id: "1",
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        phone: "(*************",
        mobilePhone: "(*************",
        dateOfBirth: "1985-03-15",
        gender: "male",
        maritalStatus: "married",
        avatar: "/images/avatars/1.png",
        address: {
            line1: "123 Main Street",
            city: "Springfield",
            state: "IL",
            postalCode: "62701",
            country: "USA"
        },
        membershipStatus: "member",
        membershipDate: "2020-01-15",
        baptismDate: "2020-02-23",
        householdId: "h1",
        groups: ["small-group-1", "mens-ministry"],
        teams: ["worship-team", "ushers"],
        lastAttendance: "2024-01-14",
        totalDonations: 12500.00,
        createdAt: "2020-01-15T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    },
    {
        id: "2",
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        phone: "(*************",
        mobilePhone: "(*************",
        dateOfBirth: "1987-07-22",
        gender: "female",
        maritalStatus: "married",
        avatar: "/images/avatars/2.png",
        address: {
            line1: "123 Main Street",
            city: "Springfield",
            state: "IL",
            postalCode: "62701",
            country: "USA"
        },
        membershipStatus: "member",
        membershipDate: "2020-01-15",
        baptismDate: "2020-02-23",
        householdId: "h1",
        groups: ["small-group-1", "womens-ministry"],
        teams: ["childrens-ministry"],
        lastAttendance: "2024-01-14",
        totalDonations: 12500.00,
        createdAt: "2020-01-15T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    },
    {
        id: "3",
        firstName: "Michael",
        lastName: "Johnson",
        email: "<EMAIL>",
        phone: "(*************",
        mobilePhone: "(*************",
        dateOfBirth: "1992-11-08",
        gender: "male",
        maritalStatus: "single",
        avatar: "/images/avatars/3.png",
        address: {
            line1: "456 Oak Avenue",
            city: "Springfield",
            state: "IL",
            postalCode: "62702",
            country: "USA"
        },
        membershipStatus: "member",
        membershipDate: "2021-06-10",
        baptismDate: "2021-07-18",
        groups: ["young-adults", "mens-ministry"],
        teams: ["tech-team", "setup-team"],
        lastAttendance: "2024-01-14",
        totalDonations: 3200.00,
        createdAt: "2021-06-10T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    },
    {
        id: "4",
        firstName: "Emily",
        lastName: "Davis",
        email: "<EMAIL>",
        phone: "(*************",
        mobilePhone: "(*************",
        dateOfBirth: "1990-05-12",
        gender: "female",
        maritalStatus: "single",
        avatar: "/images/avatars/4.png",
        address: {
            line1: "789 Pine Street",
            city: "Springfield",
            state: "IL",
            postalCode: "62703",
            country: "USA"
        },
        membershipStatus: "visitor",
        groups: ["newcomers"],
        teams: [],
        lastAttendance: "2024-01-07",
        totalDonations: 150.00,
        createdAt: "2024-01-01T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    },
    {
        id: "5",
        firstName: "David",
        lastName: "Wilson",
        email: "<EMAIL>",
        phone: "(*************",
        mobilePhone: "(*************",
        dateOfBirth: "1978-09-25",
        gender: "male",
        maritalStatus: "married",
        avatar: "/images/avatars/5.png",
        address: {
            line1: "321 Elm Drive",
            city: "Springfield",
            state: "IL",
            postalCode: "62704",
            country: "USA"
        },
        membershipStatus: "member",
        membershipDate: "2018-03-20",
        baptismDate: "2018-04-15",
        householdId: "h2",
        groups: ["small-group-2", "mens-ministry"],
        teams: ["worship-team", "prayer-team"],
        lastAttendance: "2024-01-14",
        totalDonations: 18750.00,
        createdAt: "2018-03-20T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    },
    {
        id: "6",
        firstName: "Lisa",
        lastName: "Wilson",
        email: "<EMAIL>",
        phone: "(*************",
        mobilePhone: "(*************",
        dateOfBirth: "1980-12-03",
        gender: "female",
        maritalStatus: "married",
        avatar: "/images/avatars/6.png",
        address: {
            line1: "321 Elm Drive",
            city: "Springfield",
            state: "IL",
            postalCode: "62704",
            country: "USA"
        },
        membershipStatus: "member",
        membershipDate: "2018-03-20",
        baptismDate: "2018-04-15",
        householdId: "h2",
        groups: ["small-group-2", "womens-ministry"],
        teams: ["hospitality-team"],
        lastAttendance: "2024-01-14",
        totalDonations: 18750.00,
        createdAt: "2018-03-20T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    },
    {
        id: "7",
        firstName: "James",
        lastName: "Brown",
        email: "<EMAIL>",
        phone: "(*************",
        mobilePhone: "(*************",
        dateOfBirth: "1995-02-14",
        gender: "male",
        maritalStatus: "single",
        avatar: "/images/avatars/7.png",
        address: {
            line1: "654 Maple Lane",
            city: "Springfield",
            state: "IL",
            postalCode: "62705",
            country: "USA"
        },
        membershipStatus: "visitor",
        groups: ["young-adults"],
        teams: [],
        lastAttendance: "2024-01-14",
        totalDonations: 75.00,
        createdAt: "2023-12-15T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    },
    {
        id: "8",
        firstName: "Amanda",
        lastName: "Taylor",
        email: "<EMAIL>",
        phone: "(*************",
        mobilePhone: "(*************",
        dateOfBirth: "1988-08-30",
        gender: "female",
        maritalStatus: "divorced",
        avatar: "/images/avatars/8.png",
        address: {
            line1: "987 Cedar Court",
            city: "Springfield",
            state: "IL",
            postalCode: "62706",
            country: "USA"
        },
        membershipStatus: "member",
        membershipDate: "2019-09-12",
        baptismDate: "2019-10-27",
        groups: ["single-parents", "womens-ministry"],
        teams: ["childrens-ministry"],
        lastAttendance: "2024-01-14",
        totalDonations: 4800.00,
        createdAt: "2019-09-12T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    }
];

export const households: Household[] = [
    {
        id: "h1",
        name: "Smith Family",
        primaryContactId: "1",
        members: ["1", "2"],
        address: {
            line1: "123 Main Street",
            city: "Springfield",
            state: "IL",
            postalCode: "62701",
            country: "USA"
        },
        createdAt: "2020-01-15T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    },
    {
        id: "h2",
        name: "Wilson Family",
        primaryContactId: "5",
        members: ["5", "6"],
        address: {
            line1: "321 Elm Drive",
            city: "Springfield",
            state: "IL",
            postalCode: "62704",
            country: "USA"
        },
        createdAt: "2018-03-20T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
    }
];

export const peopleOverview = [
    {
        id: 1,
        title: "Total People",
        icon: "profile",
        tooltip: "Total number of people in the database",
        counter: "1,247",
        percentage: 8.2,
        dataChart: [
            { name: "Jan", amt: 1180 },
            { name: "Feb", amt: 1195 },
            { name: "Mar", amt: 1210 },
            { name: "Apr", amt: 1225 },
            { name: "May", amt: 1235 },
            { name: "Jun", amt: 1247 },
        ],
    },
    {
        id: 2,
        title: "Active Members",
        icon: "check",
        tooltip: "People with active membership status",
        counter: "892",
        percentage: 5.4,
        dataChart: [
            { name: "Jan", amt: 845 },
            { name: "Feb", amt: 858 },
            { name: "Mar", amt: 867 },
            { name: "Apr", amt: 875 },
            { name: "May", amt: 884 },
            { name: "Jun", amt: 892 },
        ],
    },
    {
        id: 3,
        title: "New Visitors",
        icon: "star",
        tooltip: "First-time visitors this month",
        counter: "23",
        percentage: 15.2,
        dataChart: [
            { name: "Jan", amt: 18 },
            { name: "Feb", amt: 22 },
            { name: "Mar", amt: 19 },
            { name: "Apr", amt: 25 },
            { name: "May", amt: 20 },
            { name: "Jun", amt: 23 },
        ],
    },
];
