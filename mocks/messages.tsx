export const messagesCustomers = [
    {
        id: 1,
        name: "<PERSON>",
        login: "joyce",
        avatar: "/images/avatars/4.png",
        content:
            "Hello, I would like to request a new product, could you please check...",
        date: "09:05 AM",
    },
    {
        id: 2,
        name: "<PERSON><PERSON><PERSON>",
        login: "glady",
        avatar: "/images/avatars/1.png",
        content: "Amazing work! Do you take on custom projects?",
        date: "12:43 AM",
    },
    {
        id: 3,
        name: "<PERSON>",
        login: "joyce",
        avatar: "/images/avatars/2.png",
        content:
            "Hello, I would like to request a new product, could you please check...",
        date: "14:34 AM",
    },
];

export const messages = [
    {
        id: 1,
        login: "emilyjones",
        avatar: "/images/avatars/2.png",
        content:
            "Got a rough date for when the coded version of Core Dashboard Builder 2.0 will be out?",
        time: "now",
        new: true,
    },
    {
        id: 2,
        login: "orvalcasper",
        avatar: "/images/avatars/3.png",
        content: "When do you release the coded for the Fleet - Travel kit?",
        time: "2h",
        new: true,
    },
    {
        id: 3,
        login: "sarah<PERSON><PERSON><PERSON>",
        avatar: "/images/avatars/4.png",
        content:
            "Can you tell us when the coded version of Core Dashboard Builder 2.0 is launching?",
        time: "1h",
        new: true,
    },
    {
        id: 4,
        login: "michaelbrown",
        avatar: "/images/avatars/5.png",
        content:
            "When can we expect the coded version of Core Dashboard Builder 2.0?",
        time: "3h",
        new: true,
    },
    {
        id: 5,
        login: "davidwilson",
        avatar: "/images/avatars/6.png",
        content:
            "What’s the timeline for the coded version of Core Dashboard Builder 2.0?",
        time: "4h",
        new: false,
    },
    {
        id: 6,
        login: "laurawhite",
        avatar: "/images/avatars/7.png",
        content:
            "When’s the coded version of Core Dashboard Builder 2.0 coming out?",
        time: "6h",
        new: false,
    },
    {
        id: 7,
        login: "kevinlee",
        avatar: "/images/avatars/8.png",
        content:
            "Any updates on the release of the coded version for Core Dashboard Builder 2.0?",
        time: "10h",
        new: false,
    },
    {
        id: 8,
        login: "jessicagreen",
        avatar: "/images/avatars/9.png",
        content:
            "Any news on when the coded version of Core Dashboard Builder 2.0 will be available?",
        time: "15h",
        new: false,
    },
    {
        id: 9,
        login: "emilyjones",
        avatar: "/images/avatars/1.png",
        content:
            "Got a rough date for when the coded version of Core Dashboard Builder 2.0 will be out?",
        time: "20h",
        new: false,
    },
    {
        id: 10,
        login: "orvalcasper",
        avatar: "/images/avatars/2.png",
        content: "When do you release the coded for the Fleet - Travel kit?",
        time: "1d",
        new: false,
    },
    {
        id: 11,
        login: "sarahjohnson",
        avatar: "/images/avatars/4.png",
        content:
            "Can you tell us when the coded version of Core Dashboard Builder 2.0 is launching?",
        time: "3d",
        new: false,
    },
    {
        id: 12,
        login: "michaelbrown",
        avatar: "/images/avatars/5.png",
        content:
            "When can we expect the coded version of Core Dashboard Builder 2.0?",
        time: "3d",
        new: false,
    },
    {
        id: 13,
        login: "davidwilson",
        avatar: "/images/avatars/6.png",
        content:
            "What’s the timeline for the coded version of Core Dashboard Builder 2.0?",
        time: "4d",
        new: false,
    },
];

export const chat = [
    {
        id: 1,
        name: "Orval Casper",
        login: "starjumper",
        avatar: "/images/avatars/1.png",
        time: "1mo",
        content: (
            <>
                <p>
                    When do you release the coded for the Fleet - Travel kit?{" "}
                    <br></br>
                    <a href="ui8.net" target="_blank" rel="noreferrer">
                        https://ui8.net/product-link
                    </a>
                </p>
            </>
        ),
    },
    {
        id: 2,
        name: "Dash",
        login: "cloudchaser",
        avatar: "/images/avatars/3.png",
        time: "24m",
        content: (
            <>
                <p>
                    Hi @orval, thanks for contacting.<br></br>Yes, I’m working
                    on it. It would be released next 2 weeks. You could check
                    the progress here:
                    <a href="ui8.net" target="_blank" rel="noreferrer">
                        https://ui8.net/progress
                    </a>
                </p>
                <p>
                    Thanks for your patience and understanding. 🙌<br></br>
                    Regards,
                </p>
                <p>Br</p>
            </>
        ),
    },
];
