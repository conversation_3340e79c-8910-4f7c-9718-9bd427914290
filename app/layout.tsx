import { headers } from "next/headers";
import type { Metadata, Viewport } from "next";
import localFont from "next/font/local";
import Providers from "./providers";
import "./globals.css";

const interDisplay = localFont({
    src: [
        {
            path: "../public/fonts/InterDisplay-Light.woff2",
            weight: "300",
        },
        {
            path: "../public/fonts/InterDisplay-Regular.woff2",
            weight: "400",
        },
        {
            path: "../public/fonts/InterDisplay-Medium.woff2",
            weight: "500",
        },
        {
            path: "../public/fonts/InterDisplay-SemiBold.woff2",
            weight: "600",
        },
        {
            path: "../public/fonts/InterDisplay-Bold.woff2",
            weight: "700",
        },
    ],
    variable: "--font-inter-display",
});

export const metadata: Metadata = {
    title: "ChurchCore - Church Management System",
    description: "Comprehensive church management software for modern ministries",
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en" suppressHydrationWarning>
            <head>
                {/* Description no longer than 155 characters */}
                <meta
                    name="description"
                    content="ChurchCore - Comprehensive church management software for modern ministries"
                />
                {/* Product Name */}
                <meta
                    name="product-name"
                    content="ChurchCore - Church Management System"
                />
                {/* Twitter Card data */}
                <meta name="twitter:card" content="summary" />
                <meta name="twitter:site" content="@churchcore" />
                <meta
                    name="twitter:title"
                    content="ChurchCore - Church Management System"
                />
                <meta
                    name="twitter:description"
                    content="Comprehensive church management software for modern ministries 🏛️"
                />
                <meta name="twitter:creator" content="@churchcore" />
                <meta
                    name="twitter:image"
                    content="%PUBLIC_URL%/twitter-card.png"
                />
                {/* Open Graph data for Facebook */}
                <meta
                    property="og:title"
                    content="ChurchCore - Church Management System"
                />
                <meta property="og:type" content="Article" />
                <meta
                    property="og:url"
                    content="https://churchcore.app"
                />
                <meta
                    property="og:image"
                    content="%PUBLIC_URL%/fb-og-image.png"
                />
                <meta
                    property="og:description"
                    content="Comprehensive church management software for modern ministries 🏛️"
                />
                <meta
                    property="og:site_name"
                    content="ChurchCore - Church Management System"
                />
                <meta property="fb:admins" content="132951670226590" />
                {/* Open Graph data for LinkedIn */}
                <meta
                    property="og:title"
                    content="ChurchCore - Church Management System"
                />
                <meta
                    property="og:url"
                    content="https://churchcore.app"
                />
                <meta
                    property="og:image"
                    content="%PUBLIC_URL%/linkedin-og-image.png"
                />
                <meta
                    property="og:description"
                    content="Comprehensive church management software for modern ministries 🏛️"
                />
                {/* Open Graph data for Pinterest */}
                <meta
                    property="og:title"
                    content="ChurchCore - Church Management System"
                />
                <meta
                    property="og:url"
                    content="https://churchcore.app"
                />
                <meta
                    property="og:image"
                    content="%PUBLIC_URL%/pinterest-og-image.png"
                />
                <meta
                    property="og:description"
                    content="Comprehensive church management software for modern ministries 🏛️"
                />
            </head>
            <body
                className={`${interDisplay.variable} bg-b-surface1 font-inter text-body-1 text-t-primary antialiased`}
            >
                <Providers>{children}</Providers>
            </body>
        </html>
    );
}

export async function generateViewport(): Promise<Viewport> {
    const userAgent = (await headers()).get("user-agent");
    const isiPhone = /iphone/i.test(userAgent ?? "");
    return isiPhone
        ? {
              width: "device-width",
              initialScale: 1,
              maximumScale: 1, // disables auto-zoom on ios safari
          }
        : {};
}
