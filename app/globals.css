@import "tailwindcss";
@plugin "tailwind-scrollbar";

@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));

:root {
    --shade-01: #141414;
    --shade-02: #101010;
    --shade-03: #191919;
    --shade-04: #222222;
    --shade-05: #4c4c4c;
    --shade-06: #727272;
    --shade-07: #7b7b7b;
    --shade-08: #e2e2e2;
    --shade-09: #f1f1f1;
    --shade-10: #fdfdfd;
    --primary-01: #2a85ff;
    --primary-02: #00a656;
    --primary-03: #ff381c;
    --primary-04: #7f5fff;
    --primary-05: #ff9d34;
    --secondary-01: #ffbc99;
    --secondary-02: #cabdff;
    --secondary-03: #b1e5fc;
    --secondary-04: #b5e4ca;
    --secondary-05: #ffd88d;
    --backgrounds-surface1: var(--shade-09);
    --backgrounds-surface2: var(--shade-10);
    --backgrounds-surface2-overlay: var(--shade-10);
    --backgrounds-surface3: rgba(241, 241, 241, 0.5);
    --backgrounds-pop: var(--shade-10);
    --backgrounds-dark1: var(--shade-03);
    --backgrounds-dark2: var(--shade-01);
    --backgrounds-primary: var(--primary-01);
    --backgrounds-highlight: #f9f9f9;
    --backgrounds-depth: #f9f9f9;
    --backgrounds-depth2: #f9f9f9;
    --text-primary: var(--shade-02);
    --text-secondary: var(--shade-06);
    --text-tertiary: var(--shade-07);
    --text-light: var(--shade-10);
    --text-blue: var(--primary-01);
    --stroke-border: var(--shade-10);
    --stroke-subtle: rgba(123, 123, 123, 0.1);
    --stroke-focus: var(--primary-01);
    --stroke-highlight: rgba(123, 123, 123, 0.5);
    --stroke-stroke2: var(--shade-08);
    --accent: #f52495;
    --mask-1-stop50: rgba(253, 253, 253, 0);
    --mask-1-stop100: var(--shade-10);
    --mask-2-stop1: var(--shade-09);
    --mask-2-stop2: rgba(241, 241, 241, 0);
    --chart-green: #00b512;
    --chart-purple: var(--primary-04);
    --chart-yellow: var(--primary-05);
    --chart-min: var(--shade-08);
    --gradient-menu: rgba(226, 226, 226, 0.5), var(--shade-08);
    --gradient-card: var(--shade-09), #ebebeb;
    --box-shadow-depth: 0px 2.15px 0.5px -2px rgba(0, 0, 0, 0.25),
        0px 24px 24px -16px rgba(8, 8, 8, 0.04),
        0px 6px 13px 0px rgba(8, 8, 8, 0.03),
        0px 6px 4px -4px rgba(8, 8, 8, 0.05),
        0px 5px 1.5px -4px rgba(8, 8, 8, 0.09);
    --box-shadow-depth-dark-widget: 0px 6px 4px -4px rgba(8, 8, 8, 0.25),
        0px 6px 13px 0px rgba(8, 8, 8, 0.12),
        0px 24px 24px -16px rgba(8, 8, 8, 0.08);
    --box-shadow-depth-toggle: 0px 0px 36px -8px rgba(0, 0, 0, 0.05),
        0px 6px 4px -4px rgba(8, 8, 8, 0.05),
        0px 5px 1.5px -4px rgba(8, 8, 8, 0.09);
    --box-shadow-depth-menu: 0px 6px 4px -4px rgba(8, 8, 8, 0.05),
        0px 5px 1.5px -4px rgba(8, 8, 8, 0.09);
    --box-shadow-press-pressing: 0px 1px 2px 1px rgba(18, 18, 18, 0.05) inset;
    --box-shadow-press-pressing-dark: 0px 2px 4px 1px rgba(18, 18, 18, 0.35)
        inset;
    --box-shadow-widget: 0px 5px 1.5px -4px rgba(8, 8, 8, 0.09),
        0px 6px 4px -4px rgba(8, 8, 8, 0.05);
    --box-shadow-hover-light: 0px 0px 0px 3px #fff inset;
    --box-shadow-input-typing: 0px 4px 4px 0px rgba(157, 157, 157, 0.1) inset,
        0px 0px 0px 3px #fff inset;
    --box-shadow-dropdown: 0px 0px 10px 0px rgba(0, 0, 0, 0.05),
        0px 2.15px 0.5px -2px rgba(0, 0, 0, 0.25),
        0px 24px 24px -16px rgba(8, 8, 8, 0.04),
        0px 6px 13px 0px rgba(8, 8, 8, 0.03),
        0px 6px 4px -4px rgba(8, 8, 8, 0.05),
        0px 5px 1.5px -4px rgba(8, 8, 8, 0.09);
}

[data-theme="dark"] {
    --backgrounds-surface1: var(--shade-02);
    --backgrounds-surface2: var(--shade-03);
    --backgrounds-surface2-overlay: var(--shade-02);
    --backgrounds-surface3: rgba(16, 16, 16, 0.5);
    --backgrounds-pop: var(--shade-04);
    --backgrounds-dark1: var(--shade-08);
    --backgrounds-dark2: var(--shade-10);
    --backgrounds-primary: var(--primary-01);
    --backgrounds-highlight: var(--shade-04);
    --backgrounds-depth: #1d1d1d;
    --backgrounds-depth2: rgba(16, 16, 16, 0.5);
    --text-primary: var(--shade-09);
    --text-secondary: var(--shade-07);
    --text-tertiary: var(--shade-06);
    --text-light: var(--shade-01);
    --text-blue: var(--primary-01);
    --stroke-border: #272727;
    --stroke-subtle: #1e1e1e;
    --stroke-focus: var(--primary-01);
    --stroke-highlight: rgba(114, 114, 114, 0.5);
    --stroke-stroke2: #282828;
    --accent: #f52495;
    --mask-1-stop50: rgba(40, 40, 40, 0);
    --mask-1-stop100: var(--shade-03);
    --mask-2-stop1: var(--shade-02);
    --mask-2-stop2: rgba(27, 27, 27, 0);
    --chart-min: var(--shade-04);
    --gradient-menu: rgba(226, 226, 226, 0.025), rgba(226, 226, 226, 0.05);
    --gradient-card: rgba(241, 241, 241, 0.075), rgba(235, 235, 235, 0.075);
    --box-shadow-depth: 0px 2.15px 0.5px -2px rgba(0, 0, 0, 0.25),
        0px 5px 1.5px -4px rgba(8, 8, 8, 0.2),
        0px 6px 4px -4px rgba(8, 8, 8, 0.16),
        0px 6px 13px 0px rgba(8, 8, 8, 0.12),
        0px 24px 24px -16px rgba(8, 8, 8, 0.08),
        2px 4px 16px 0px rgba(253, 253, 253, 0.05) inset;
    --box-shadow-depth-toggle: 0px 0px 36px -8px rgba(0, 0, 0, 0.5),
        0px 2.15px 0.5px -2px rgba(0, 0, 0, 0.25),
        0px 24px 24px -16px rgba(8, 8, 8, 0.04),
        0px 6px 13px 0px rgba(8, 8, 8, 0.03),
        0px 6px 4px -4px rgba(8, 8, 8, 0.05),
        0px 5px 1.5px -4px rgba(8, 8, 8, 0.09);
    --box-shadow-widget: 0px 5px 1.5px -4px rgba(8, 8, 8, 0.5),
        0px 6px 4px -4px rgba(8, 8, 8, 0.05);
    --box-shadow-input-typing: 0px 4px 4px 0px rgba(18, 18, 18, 0.81) inset,
        0px 0px 0px 3px rgba(40, 40, 40, 0.1) inset;
    --box-shadow-dropdown: 0px 16px 48px -4px rgba(0, 0, 0, 0.75),
        0px 0px 10px 0px rgba(0, 0, 0, 0.5),
        0px 2.15px 0.5px -2px rgba(0, 0, 0, 0.25),
        0px 24px 24px -16px rgba(8, 8, 8, 0.04),
        0px 6px 13px 0px rgba(8, 8, 8, 0.03),
        0px 6px 4px -4px rgba(8, 8, 8, 0.05),
        0px 5px 1.5px -4px rgba(8, 8, 8, 0.09);
}

@theme {
    --breakpoint-*: initial;
    --breakpoint-sm: 480px;
    --breakpoint-md: 767px;
    --breakpoint-lg: 1023px;
    --breakpoint-xl: 1259px;
    --breakpoint-2xl: 1419px;
    --breakpoint-3xl: 1719px;
    --breakpoint-4xl: 1899px;
    --color-shade-01: var(--shade-01);
    --color-shade-02: var(--shade-02);
    --color-shade-03: var(--shade-03);
    --color-shade-04: var(--shade-04);
    --color-shade-05: var(--shade-05);
    --color-shade-06: var(--shade-06);
    --color-shade-07: var(--shade-07);
    --color-shade-08: var(--shade-08);
    --color-shade-09: var(--shade-09);
    --color-shade-10: var(--shade-10);
    --color-primary-01: var(--primary-01);
    --color-primary-02: var(--primary-02);
    --color-primary-03: var(--primary-03);
    --color-primary-04: var(--primary-04);
    --color-primary-05: var(--primary-05);
    --color-secondary-01: var(--secondary-01);
    --color-secondary-02: var(--secondary-02);
    --color-secondary-03: var(--secondary-03);
    --color-secondary-04: var(--secondary-04);
    --color-secondary-05: var(--secondary-05);
    --color-b-surface1: var(--backgrounds-surface1);
    --color-b-surface2: var(--backgrounds-surface2);
    --color-b-surface2-overlay: var(--backgrounds-surface2-overlay);
    --color-b-surface3: var(--backgrounds-surface3);
    --color-b-pop: var(--backgrounds-pop);
    --color-b-dark1: var(--backgrounds-dark1);
    --color-b-dark2: var(--backgrounds-dark2);
    --color-b-primary: var(--backgrounds-primary);
    --color-b-highlight: var(--backgrounds-highlight);
    --color-b-depth: var(--backgrounds-depth);
    --color-b-depth2: var(--backgrounds-depth2);
    --color-t-primary: var(--text-primary);
    --color-t-secondary: var(--text-secondary);
    --color-t-tertiary: var(--text-tertiary);
    --color-t-light: var(--text-light);
    --color-t-blue: var(--text-blue);
    --color-s-border: var(--stroke-border);
    --color-s-subtle: var(--stroke-subtle);
    --color-s-focus: var(--stroke-focus);
    --color-s-highlight: var(--stroke-highlight);
    --color-s-stroke2: var(--stroke-stroke2);
    --color-accent: var(--accent);
    --color-mask-1-stop50: var(--mask-1-stop50);
    --color-mask-1-stop100: var(--mask-1-stop100);
    --color-mask-2-stop1: var(--mask-2-stop1);
    --color-mask-2-stop2: var(--mask-2-stop2);
    --color-chart-green: var(--chart-green);
    --color-chart-purple: var(--chart-purple);
    --color-chart-yellow: var(--chart-yellow);
    --default-transition-duration: 0.2s;
    --default-border-width: 1.5px;
    --border-width-1: 1px;
    --font-inter: var(--font-inter-display);
    --text-0: 0;
    --text-h1: 6rem;
    --text-h1--line-height: 1.15;
    --text-h1--letter-spacing: -0.015em;
    --text-h1--font-weight: 300;
    --text-h2: 3.75rem;
    --text-h2--line-height: 1.25;
    --text-h2--letter-spacing: -0.015em;
    --text-h2--font-weight: 500;
    --text-h3: 3rem;
    --text-h3--line-height: 1.25;
    --text-h3--font-weight: 500;
    --text-h4: 2rem;
    --text-h4--line-height: 1.45;
    --text-h4--letter-spacing: 0.003em;
    --text-h4--font-weight: 600;
    --text-h5: 1.5rem;
    --text-h5--line-height: 1.45;
    --text-h5--letter-spacing: -0.01em;
    --text-h5--font-weight: 500;
    --text-h6: 1.25rem;
    --text-h6--line-height: 1.45;
    --text-h6--letter-spacing: -0.01em;
    --text-h6--font-weight: 600;
    --text-sub-title-1: 1rem;
    --text-sub-title-1--line-height: 1.5;
    --text-sub-title-1--letter-spacing: -0.015em;
    --text-sub-title-1--font-weight: 600;
    --text-sub-title-2: 0.875rem;
    --text-sub-title-2--line-height: 1.55;
    --text-sub-title-2--letter-spacing: -0.015em;
    --text-sub-title-2--font-weight: 700;
    --text-body-1: 1rem;
    --text-body-1--line-height: 1.5;
    --text-body-1--letter-spacing: -0.015em;
    --text-body-2: 0.875rem;
    --text-body-2--line-height: 1.5;
    --text-body-2--letter-spacing: -0.025em;
    --text-button: 0.875rem;
    --text-button--line-height: 1;
    --text-button--letter-spacing: -0.015em;
    --text-button--font-weight: 600;
    --text-caption: 0.75rem;
    --text-caption--line-height: 1.6;
    --text-caption--letter-spacing: -0.02em;
    --text-overline: 0.625rem;
    --text-overline--line-height: 1;
    --text-overline--letter-spacing: 0.02em;
    --text-overline--font-weight: 500;
    --rounded-4xl: 2rem;
    --shadow-depth: var(--box-shadow-depth);
    --shadow-depth-dark-widget: var(--box-shadow-depth-dark-widget);
    --shadow-depth-toggle: var(--box-shadow-depth-toggle);
    --shadow-depth-menu: var(--box-shadow-depth-menu);
    --shadow-press-pressing: var(--box-shadow-press-pressing);
    --shadow-press-pressing-dark: var(--box-shadow-press-pressing-dark);
    --shadow-widget: var(--box-shadow-widget);
    --shadow-hover-light: var(--box-shadow-hover-light);
    --shadow-input-typing: var(--box-shadow-input-typing);
    --shadow-dropdown: var(--box-shadow-dropdown);
    --animate-spin-reverse: spin-reverse 1s linear infinite;
    @keyframes spin-reverse {
        to {
            transform: rotate(-360deg);
        }
    }
}

@layer base {
    html {
        @apply text-[calc(0.7rem+0.4vw)] max-[2300px]:text-[calc(0.7rem+0.3vw)] max-[2150px]:text-[calc(0.7rem+0.25vw)] max-[2000px]:text-[1rem];
    }
    button {
        @apply cursor-pointer;
    }
}

@layer components {
    .gradient-menu {
        background-image: linear-gradient(to bottom, var(--gradient-menu));
    }
    .gradient-card {
        background-image: linear-gradient(to bottom, var(--gradient-card));
    }
    .gradient-repeat-lines {
        background-image: linear-gradient(
            to right,
            rgba(123, 123, 123, 0.6) 0,
            rgba(123, 123, 123, 0.6) 2px,
            transparent 2px
        );
        background-repeat: repeat-x;
        background-size: 3px 100%;
    }
    .gradient-repeat-lines-green {
        background-image: linear-gradient(
            to right,
            #00b512 0,
            #00b512 2px,
            transparent 2px
        );
        background-repeat: repeat-x;
        background-size: 3px 100%;
    }
    .center {
        @apply w-full mx-auto px-5 max-md:px-3;
    }
    .center-with-sidebar {
        @apply w-full mx-auto pr-5 max-xl:pl-5 max-md:px-3;
    }
    .col-right {
        @apply shrink-0 w-[30rem] max-3xl:w-[23rem] max-2xl:w-[21.5rem] max-[1349px]:w-[19.5rem] max-xl:w-[21.5rem] max-lg:w-full;
    }
    .col-left {
        @apply w-[calc(100%-30.75rem)] mr-3 max-3xl:w-[calc(100%-23.75rem)] max-2xl:w-[calc(100%-22.25rem)] max-[1349px]:w-[calc(100%-20.25rem)] max-xl:w-[calc(100%-22.25rem)] max-lg:w-full max-lg:mr-0 max-lg:mb-3;
    }
    .card {
        @apply mb-3 p-3 rounded-4xl bg-b-surface2 shadow-widget last:mb-0 dark:shadow-[inset_0_0_0_1.5px_rgba(229,229,229,0.04),0px_5px_1.5px_-4px_rgba(8,8,8,0.5),0px_6px_4px_-4px_rgba(8,8,8,0.05)];
    }
    .chart-tooltip {
        @apply p-2 bg-b-dark1 rounded-lg text-t-light;
    }
    .chart-tooltip-up {
        @apply p-2 bg-b-dark1 rounded-lg text-t-light before:absolute before:top-full before:left-1/2 before:-translate-x-1/2 before:border-l-[5px] before:border-l-transparent before:border-r-[5px] before:border-r-transparent before:border-t-[6px] before:border-b-dark1;
    }
    .label {
        @apply inline-flex items-center h-7 px-1.75 rounded-lg text-button;
    }
    .label-green {
        @apply border border-[#00A656]/15 bg-[#00A656]/5 text-[#00A656];
    }
    .label-red {
        @apply border border-[#FF6A55]/15 bg-[#FF6A55]/5 text-[#FF6A55];
    }
    .label-yellow {
        @apply border border-[#EF9D0E]/15 bg-[#EF9D0E]/5 text-[#EF9D0E];
    }
    .label-gray {
        @apply border border-s-stroke2 bg-b-surface1 text-t-secondary;
    }
    .box-hover {
        @apply absolute inset-0 rounded-[20px] bg-linear-to-b from-shade-09 to-[#ebebeb] before:absolute before:inset-[1.5px] before:bg-b-highlight before:rounded-[18.5px] before:border-[1.5px] before:border-b-surface2 invisible opacity-0 transition-all group-hover:visible group-hover:opacity-100 dark:from-shade-09/[0.075] dark:to-[#ebebeb]/[0.075];
    }
    .action {
        @apply flex items-center gap-1 px-1 h-6 border border-transparent rounded text-button text-t-secondary/80 fill-t-secondary transition-colors hover:border-s-stroke2 hover:text-t-primary hover:fill-t-primary max-md:px-0.75 dark:hover:border-shade-05/50;
    }
    .action svg {
        @apply !size-4 fill-inherit;
    }
    .react-tooltip {
        @apply z-50 !bg-b-dark1 !opacity-100 !px-2 !py-0.5 !rounded-[6px] !text-caption !text-t-light;
    }
    .tiptap {
        @apply min-h-48 px-4.5 py-4 outline-none [&_ul]:list-disc [&_ul]:pl-4;
    }
    .custom-datepicker .react-datepicker {
        @apply relative z-3 p-4 rounded-3xl bg-b-surface1 font-inter;
    }
    .custom-datepicker .react-datepicker__day-names,
    .custom-datepicker .react-datepicker__week {
        @apply flex justify-center;
    }
    .custom-datepicker .react-datepicker__day-name {
        @apply flex justify-center items-center w-11 h-11 text-caption text-t-secondary/50;
    }
    .custom-datepicker .react-datepicker__day {
        @apply flex justify-center items-center w-11 h-11 rounded-full border border-transparent text-body-2 text-t-primary cursor-pointer transition-colors hover:border-b-dark1;
    }
    .custom-datepicker .react-datepicker__day--outside-month {
        @apply opacity-0 pointer-events-none;
    }
    .custom-datepicker .react-datepicker__day--disabled {
        @apply text-t-secondary/50 pointer-events-none;
    }
    .custom-datepicker .react-datepicker__day--selected {
        @apply bg-b-dark1 border-b-dark1 text-t-light;
    }
    .custom-datepicker .react-datepicker__aria-live {
        @apply hidden;
    }
    .custom-datepicker .react-datepicker--time-only {
        @apply absolute inset-0;
    }
    .custom-datepicker .react-datepicker__header--time--only {
        @apply hidden;
    }
    .custom-datepicker .react-datepicker__time-list {
        @apply absolute top-18 left-0 right-0 bottom-4 px-4 overflow-auto;
    }
    .custom-datepicker .react-datepicker__time-list-item {
        @apply relative flex items-center h-12 pl-12 rounded-xl text-body-2 text-t-secondary transition-colors cursor-pointer hover:bg-shade-08/70 hover:text-t-primary dark:hover:bg-shade-04;
    }
    .custom-datepicker .react-datepicker__time-list-item:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0.75rem;
        transform: translateY(-50%);
        width: 1.5rem;
        height: 1.5rem;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M18.406 5.541a.75.75 0 1 1 1.187.917l-9.27 12a.75.75 0 0 1-1.059.129l-4.73-3.75a.75.75 0 0 1 .932-1.175L9.6 16.94l8.806-11.399z' fill='%23var(--color-t-primary)'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% auto;
        opacity: 0;
        transition: opacity 0.2s;
    }
    html[data-theme="dark"]
        .custom-datepicker
        .react-datepicker__time-list-item:before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M18.406 5.541a.75.75 0 1 1 1.187.917l-9.27 12a.75.75 0 0 1-1.059.129l-4.73-3.75a.75.75 0 0 1 .932-1.175L9.6 16.94l8.806-11.399z' fill='%23F1F1F1'/%3E%3C/svg%3E");
    }
    .custom-datepicker
        .react-datepicker__time-list-item.react-datepicker__time-list-item--selected:before {
        opacity: 1;
    }
    .custom-datepicker .react-datepicker__time-list-item--selected {
        @apply text-t-primary;
    }
    .epr-header,
    .epr-emoji-category-label {
        @apply !hidden;
    }
    .epr-emoji-category-content {
        @apply !py-2.75;
    }
    .epr-body {
        @apply scrollbar-none;
    }
    .recharts-sector {
        @apply stroke-transparent outline-none;
    }
}
